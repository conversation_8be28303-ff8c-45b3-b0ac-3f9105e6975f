import { ID } from "@vendure/core";

// tslint:disable
export type Maybe<T> = T | null;
export type Exact<T extends { [key: string]: unknown }> = { [K in keyof T]: T[K] };
export type MakeOptional<T, K extends keyof T> = Omit<T, K> & { [SubKey in K]?: Maybe<T[SubKey]> };
export type MakeMaybe<T, K extends keyof T> = Omit<T, K> & { [SubKey in K]: Maybe<T[SubKey]> };
/** All built-in and custom scalars, mapped to their actual values */
export type Scalars = {
  ID: string;
  String: string;
  Boolean: boolean;
  Int: number;
  Float: number;
  Date: any;
  /** A date-time string at UTC, such as 2007-12-03T10:15:30Z, compliant with the `date-time` format outlined in section 5.6 of the RFC 3339 profile of the ISO 8601 standard for representation of dates and times using the Gregorian calendar. */
  DateTime: any;
  /** The `JSON` scalar type represents JSON values as specified by [ECMA-404](http://www.ecma-international.org/publications/files/ECMA-ST/ECMA-404.pdf). */
  JSON: any;
  /** The `Money` scalar type represents monetary values and supports signed double-precision fractional values as specified by [IEEE 754](https://en.wikipedia.org/wiki/IEEE_floating_point). */
  Money: any;
  Time: any;
  /** The `Upload` scalar type represents a file upload. */
  Upload: any;
};

export type ActivateMembershipCardInput = {
  name: Scalars['String'];
  phone?: Maybe<Scalars['String']>;
  dateBirth?: Maybe<Scalars['DateTime']>;
  address?: Maybe<CreateAddressInput>;
  wechatCode?: Maybe<Scalars['String']>;
  gender?: Maybe<Scalars['String']>;
};

export type ActiveOrderArgs = {
  activeOrderInput?: Maybe<Scalars['String']>;
};

export type ActiveOrderResult = Order | NoActiveOrderError;

export type ActivityAllotted = {
  __typename?: 'ActivityAllotted';
  allottedType?: Maybe<AllottedType>;
  startTime?: Maybe<Scalars['DateTime']>;
  endTime?: Maybe<Scalars['DateTime']>;
};

export type ActivityAllottedInput = {
  allottedType?: Maybe<AllottedType>;
  startTime?: Maybe<Scalars['DateTime']>;
  endTime?: Maybe<Scalars['DateTime']>;
};

export type ActivityContent = {
  __typename?: 'ActivityContent';
  fullMinus?: Maybe<Array<Maybe<Scalars['String']>>>;
  fullDiscount?: Maybe<Array<Maybe<Scalars['String']>>>;
  fullPresent?: Maybe<Array<Maybe<Scalars['String']>>>;
};

export type ActivityCountdown = Node & {
  __typename?: 'ActivityCountdown';
  id: Scalars['ID'];
  createdAt: Scalars['DateTime'];
  updatedAt: Scalars['DateTime'];
  activityType: PromotionType;
  activityId?: Maybe<Scalars['ID']>;
  name: Scalars['String'];
  countdownTime?: Maybe<Scalars['DateTime']>;
  priority?: Maybe<Scalars['Int']>;
  countdownShowTime?: Maybe<Scalars['Int']>;
  associatedName?: Maybe<Scalars['String']>;
  associatedStatus?: Maybe<ActivityStatus>;
  promotionId?: Maybe<Scalars['ID']>;
};

export type ActivityCountdownFilterParameter = {
  id?: Maybe<IdOperators>;
  createdAt?: Maybe<DateOperators>;
  updatedAt?: Maybe<DateOperators>;
  activityType?: Maybe<StringOperators>;
  activityId?: Maybe<IdOperators>;
  name?: Maybe<StringOperators>;
  countdownTime?: Maybe<DateOperators>;
  priority?: Maybe<NumberOperators>;
  countdownShowTime?: Maybe<NumberOperators>;
  associatedName?: Maybe<StringOperators>;
  associatedStatus?: Maybe<StringOperators>;
  promotionId?: Maybe<IdOperators>;
};

export type ActivityCountdownInput = {
  id?: Maybe<Scalars['ID']>;
  activityType: PromotionType;
  activityId: Scalars['ID'];
  name: Scalars['String'];
  countdownTime?: Maybe<Scalars['DateTime']>;
  priority?: Maybe<Scalars['Int']>;
  countdownShowTime?: Maybe<Scalars['Int']>;
};

export type ActivityCountdownList = PaginatedList & {
  __typename?: 'ActivityCountdownList';
  items: Array<ActivityCountdown>;
  totalItems: Scalars['Int'];
};

export type ActivityCountdownListOptions = {
  /** Skips the first n results, for use in pagination */
  skip?: Maybe<Scalars['Int']>;
  /** Takes n results, for use in pagination */
  take?: Maybe<Scalars['Int']>;
  /** Specifies which properties to sort the results by */
  sort?: Maybe<ActivityCountdownSortParameter>;
  /** Allows the results to be filtered */
  filter?: Maybe<ActivityCountdownFilterParameter>;
  /** Specifies whether multiple "filter" arguments should be combines with a logical AND or OR operation. Defaults to AND. */
  filterOperator?: Maybe<LogicalOperator>;
};

export type ActivityCountdownSortParameter = {
  id?: Maybe<SortOrder>;
  createdAt?: Maybe<SortOrder>;
  updatedAt?: Maybe<SortOrder>;
  activityId?: Maybe<SortOrder>;
  name?: Maybe<SortOrder>;
  countdownTime?: Maybe<SortOrder>;
  priority?: Maybe<SortOrder>;
  countdownShowTime?: Maybe<SortOrder>;
  associatedName?: Maybe<SortOrder>;
  promotionId?: Maybe<SortOrder>;
};

export enum ActivityState {
  Open = 'open',
  Close = 'close'
}

export enum ActivityStatus {
  Normal = 'normal',
  Failure = 'failure',
  NotStarted = 'notStarted',
  HaveEnded = 'haveEnded'
}

export type ActivitySynopsis = {
  __typename?: 'ActivitySynopsis';
  synopsisStr?: Maybe<Scalars['String']>;
  synopsisTags?: Maybe<Array<Maybe<Scalars['String']>>>;
};

export type AddPaymentToOrderResult = Order | OrderPaymentStateError | IneligiblePaymentMethodError | PaymentFailedError | PaymentDeclinedError | OrderStateTransitionError | NoActiveOrderError;

export type Address = Node & {
  __typename?: 'Address';
  id: Scalars['ID'];
  createdAt: Scalars['DateTime'];
  updatedAt: Scalars['DateTime'];
  fullName?: Maybe<Scalars['String']>;
  company?: Maybe<Scalars['String']>;
  streetLine1: Scalars['String'];
  streetLine2?: Maybe<Scalars['String']>;
  city?: Maybe<Scalars['String']>;
  province?: Maybe<Scalars['String']>;
  postalCode?: Maybe<Scalars['String']>;
  country: Country;
  phoneNumber?: Maybe<Scalars['String']>;
  defaultShippingAddress?: Maybe<Scalars['Boolean']>;
  defaultBillingAddress?: Maybe<Scalars['Boolean']>;
  customFields?: Maybe<AddressCustomFields>;
};

export type AddressCustomFields = {
  __typename?: 'AddressCustomFields';
  district?: Maybe<Scalars['String']>;
};

export type Adjustment = {
  __typename?: 'Adjustment';
  adjustmentSource: Scalars['String'];
  type: AdjustmentType;
  description: Scalars['String'];
  amount: Scalars['Money'];
  data?: Maybe<Scalars['JSON']>;
};

export enum AdjustmentType {
  Promotion = 'PROMOTION',
  DistributedOrderPromotion = 'DISTRIBUTED_ORDER_PROMOTION',
  Other = 'OTHER'
}

export type AdvertisingAttributionInput = {
  clickId: Scalars['String'];
  attribution?: Maybe<Scalars['JSON']>;
  openIdKey?: Maybe<Scalars['String']>;
};

export type AfterSale = Node & {
  __typename?: 'AfterSale';
  id: Scalars['ID'];
  createdAt?: Maybe<Scalars['DateTime']>;
  updatedAt?: Maybe<Scalars['DateTime']>;
  type?: Maybe<AfterSaleType>;
  mode?: Maybe<AfterSaleMode>;
  cause?: Maybe<Scalars['String']>;
  description?: Maybe<Scalars['String']>;
  img?: Maybe<Array<Maybe<Scalars['String']>>>;
  price?: Maybe<Scalars['Int']>;
  code?: Maybe<Scalars['String']>;
  applyAt?: Maybe<Scalars['DateTime']>;
  auditAt?: Maybe<Scalars['DateTime']>;
  deliverAt?: Maybe<Scalars['DateTime']>;
  cancelAt?: Maybe<Scalars['DateTime']>;
  merchantDeliveryTime?: Maybe<Scalars['DateTime']>;
  confirmReceiptAt?: Maybe<Scalars['DateTime']>;
  state?: Maybe<AfterSaleState>;
  groundsRefusal?: Maybe<Scalars['String']>;
  afterSaleHistories?: Maybe<Array<Maybe<AfterSaleHistory>>>;
  logisticses?: Maybe<Array<Maybe<AfterSaleLogistics>>>;
  channelAddress?: Maybe<AfterSaleChannelAddress>;
  afterSaleLines?: Maybe<Array<Maybe<AfterSaleLine>>>;
  order?: Maybe<Order>;
  auditTimeout?: Maybe<Scalars['DateTime']>;
  logisticsTimeout?: Maybe<Scalars['DateTime']>;
  merchantConfirmReceiptTimeout?: Maybe<Scalars['DateTime']>;
  userConfirmReceiptTimeout?: Maybe<Scalars['DateTime']>;
  userHandleTimeout?: Maybe<Scalars['DateTime']>;
  reclaimedShoppingCredits?: Maybe<Scalars['Int']>;
  returnedShoppingCredits?: Maybe<Scalars['Int']>;
};

export type AfterSaleAuditInput = {
  afterSaleId: Scalars['ID'];
  state: AuditState;
  channelAddressId?: Maybe<Scalars['ID']>;
  massage?: Maybe<Scalars['String']>;
  logisticCode?: Maybe<Scalars['String']>;
  company?: Maybe<Scalars['String']>;
};

export type AfterSaleChannelAddress = {
  __typename?: 'AfterSaleChannelAddress';
  fullName?: Maybe<Scalars['String']>;
  company?: Maybe<Scalars['String']>;
  streetLine1?: Maybe<Scalars['String']>;
  streetLine2?: Maybe<Scalars['String']>;
  city?: Maybe<Scalars['String']>;
  province?: Maybe<Scalars['String']>;
  postalCode?: Maybe<Scalars['String']>;
  country?: Maybe<Scalars['String']>;
  countryCode?: Maybe<Scalars['String']>;
  phoneNumber?: Maybe<Scalars['String']>;
  district?: Maybe<Scalars['String']>;
};

export type AfterSaleFilterParameter = {
  id?: Maybe<IdOperators>;
  createdAt?: Maybe<DateOperators>;
  updatedAt?: Maybe<DateOperators>;
  type?: Maybe<StringOperators>;
  mode?: Maybe<StringOperators>;
  cause?: Maybe<StringOperators>;
  description?: Maybe<StringOperators>;
  price?: Maybe<NumberOperators>;
  code?: Maybe<StringOperators>;
  applyAt?: Maybe<DateOperators>;
  auditAt?: Maybe<DateOperators>;
  deliverAt?: Maybe<DateOperators>;
  cancelAt?: Maybe<DateOperators>;
  merchantDeliveryTime?: Maybe<DateOperators>;
  confirmReceiptAt?: Maybe<DateOperators>;
  state?: Maybe<StringOperators>;
  groundsRefusal?: Maybe<StringOperators>;
  auditTimeout?: Maybe<DateOperators>;
  logisticsTimeout?: Maybe<DateOperators>;
  merchantConfirmReceiptTimeout?: Maybe<DateOperators>;
  userConfirmReceiptTimeout?: Maybe<DateOperators>;
  userHandleTimeout?: Maybe<DateOperators>;
  reclaimedShoppingCredits?: Maybe<NumberOperators>;
  returnedShoppingCredits?: Maybe<NumberOperators>;
};

export type AfterSaleHistory = Node & {
  __typename?: 'AfterSaleHistory';
  id: Scalars['ID'];
  createdAt?: Maybe<Scalars['DateTime']>;
  updatedAt?: Maybe<Scalars['DateTime']>;
  message?: Maybe<Scalars['String']>;
  data?: Maybe<AfterSaleJson>;
  operatorType?: Maybe<OperatorType>;
  type?: Maybe<AfterSaleHistoryType>;
};

export enum AfterSaleHistoryType {
  Submit = 'submit',
  UpdateSubmit = 'updateSubmit',
  ShippingReturnAddress = 'shippingReturnAddress',
  FillOutReturnLogistics = 'fillOutReturnLogistics',
  UpdateReturnLogistics = 'updateReturnLogistics',
  ConfirmReceiptAndDelivery = 'confirmReceiptAndDelivery',
  AgreeToRefundOnly = 'agreeToRefundOnly',
  RefusalOfRefund = 'refusalOfRefund',
  ConfirmReceiptAndRefund = 'confirmReceiptAndRefund',
  CancelAfterSale = 'cancelAfterSale',
  Complete = 'complete'
}

export type AfterSaleInput = {
  id?: Maybe<Scalars['ID']>;
  orderId: Scalars['ID'];
  mode: AfterSaleMode;
  cause: Scalars['String'];
  description: Scalars['String'];
  afterSaleLines: Array<AfterSaleLineInput>;
  img?: Maybe<Array<Maybe<Scalars['String']>>>;
  price: Scalars['Int'];
};

export type AfterSaleJson = {
  __typename?: 'AfterSaleJson';
  type?: Maybe<AfterSaleType>;
  mode?: Maybe<AfterSaleMode>;
  cause?: Maybe<Scalars['String']>;
  description?: Maybe<Scalars['String']>;
  img?: Maybe<Array<Maybe<Scalars['String']>>>;
  price?: Maybe<Scalars['Int']>;
  code?: Maybe<Scalars['String']>;
  applyAt?: Maybe<Scalars['DateTime']>;
  auditAt?: Maybe<Scalars['DateTime']>;
  deliverAt?: Maybe<Scalars['DateTime']>;
  cancelAt?: Maybe<Scalars['DateTime']>;
  state?: Maybe<AfterSaleState>;
  groundsRefusal?: Maybe<Scalars['String']>;
  channelAddress?: Maybe<ChannelAddress>;
  reclaimedShoppingCredits?: Maybe<Scalars['Int']>;
  returnedShoppingCredits?: Maybe<Scalars['Int']>;
};

export type AfterSaleLine = Node & {
  __typename?: 'AfterSaleLine';
  id: Scalars['ID'];
  createdAt?: Maybe<Scalars['DateTime']>;
  updatedAt?: Maybe<Scalars['DateTime']>;
  quantity?: Maybe<Scalars['Int']>;
  price?: Maybe<Scalars['Int']>;
  afterSale?: Maybe<AfterSale>;
  orderLine?: Maybe<OrderLine>;
  isBlindBox?: Maybe<Scalars['Boolean']>;
  blindBoxPrice?: Maybe<Scalars['Int']>;
  reclaimedShoppingCredits?: Maybe<Scalars['Int']>;
  returnedShoppingCredits?: Maybe<Scalars['Int']>;
};

export type AfterSaleLineInput = {
  lineId: Scalars['ID'];
  quantity?: Maybe<Scalars['Int']>;
  price?: Maybe<Scalars['Int']>;
};

export type AfterSaleList = PaginatedList & {
  __typename?: 'AfterSaleList';
  items: Array<AfterSale>;
  totalItems: Scalars['Int'];
};

export type AfterSaleListOptions = {
  /** Skips the first n results, for use in pagination */
  skip?: Maybe<Scalars['Int']>;
  /** Takes n results, for use in pagination */
  take?: Maybe<Scalars['Int']>;
  /** Specifies which properties to sort the results by */
  sort?: Maybe<AfterSaleSortParameter>;
  /** Allows the results to be filtered */
  filter?: Maybe<AfterSaleFilterParameter>;
  /** Specifies whether multiple "filter" arguments should be combines with a logical AND or OR operation. Defaults to AND. */
  filterOperator?: Maybe<LogicalOperator>;
};

export type AfterSaleLogistics = Node & {
  __typename?: 'AfterSaleLogistics';
  id: Scalars['ID'];
  createdAt: Scalars['DateTime'];
  updatedAt: Scalars['DateTime'];
  order?: Maybe<Order>;
  logisticCode?: Maybe<Scalars['String']>;
  company?: Maybe<Scalars['String']>;
  type?: Maybe<LogisticsType>;
  state?: Maybe<LogisticState>;
  logisticInfo?: Maybe<LogisticInfo>;
};

export enum AfterSaleMode {
  RefundOnly = 'refundOnly',
  RefundForReturnedGoods = 'refundForReturnedGoods',
  ExchangeGoods = 'exchangeGoods'
}

export type AfterSaleSortParameter = {
  id?: Maybe<SortOrder>;
  createdAt?: Maybe<SortOrder>;
  updatedAt?: Maybe<SortOrder>;
  cause?: Maybe<SortOrder>;
  description?: Maybe<SortOrder>;
  price?: Maybe<SortOrder>;
  code?: Maybe<SortOrder>;
  applyAt?: Maybe<SortOrder>;
  auditAt?: Maybe<SortOrder>;
  deliverAt?: Maybe<SortOrder>;
  cancelAt?: Maybe<SortOrder>;
  merchantDeliveryTime?: Maybe<SortOrder>;
  confirmReceiptAt?: Maybe<SortOrder>;
  groundsRefusal?: Maybe<SortOrder>;
  auditTimeout?: Maybe<SortOrder>;
  logisticsTimeout?: Maybe<SortOrder>;
  merchantConfirmReceiptTimeout?: Maybe<SortOrder>;
  userConfirmReceiptTimeout?: Maybe<SortOrder>;
  userHandleTimeout?: Maybe<SortOrder>;
  reclaimedShoppingCredits?: Maybe<SortOrder>;
  returnedShoppingCredits?: Maybe<SortOrder>;
};

export enum AfterSaleState {
  Submit = 'submit',
  WaitingForDelivery = 'waitingForDelivery',
  GoodsToBeReceived = 'goodsToBeReceived',
  ConfirmReceiptAndDelivery = 'confirmReceiptAndDelivery',
  RefusalOfGoods = 'refusalOfGoods',
  RefusalOfRefund = 'refusalOfRefund',
  AgreeToRefund = 'agreeToRefund',
  SuccessfulRefund = 'successfulRefund',
  Complete = 'complete',
  Cancel = 'cancel'
}

export enum AfterSaleType {
  RefundOnSale = 'refundOnSale',
  RefundAfterSale = 'refundAfterSale',
  MerchantVoluntaryRefund = 'merchantVoluntaryRefund'
}

export enum AllottedType {
  LongTime = 'longTime',
  TemporalInterval = 'temporalInterval'
}

/** Returned when attempting to set the Customer for an Order when already logged in. */
export type AlreadyLoggedInError = ErrorResult & {
  __typename?: 'AlreadyLoggedInError';
  errorCode: ErrorCode;
  message: Scalars['String'];
};

export type Announcement = Node & {
  __typename?: 'Announcement';
  id: Scalars['ID'];
  createdAt?: Maybe<Scalars['DateTime']>;
  updatedAt?: Maybe<Scalars['DateTime']>;
  value?: Maybe<Scalars['String']>;
};

export type AnnouncementFilterParameter = {
  id?: Maybe<IdOperators>;
  createdAt?: Maybe<DateOperators>;
  updatedAt?: Maybe<DateOperators>;
  value?: Maybe<StringOperators>;
};

export type AnnouncementInput = {
  id?: Maybe<Scalars['ID']>;
  value?: Maybe<Scalars['String']>;
};

export type AnnouncementList = PaginatedList & {
  __typename?: 'AnnouncementList';
  items: Array<Announcement>;
  totalItems: Scalars['Int'];
};

export type AnnouncementListOptions = {
  /** Skips the first n results, for use in pagination */
  skip?: Maybe<Scalars['Int']>;
  /** Takes n results, for use in pagination */
  take?: Maybe<Scalars['Int']>;
  /** Specifies which properties to sort the results by */
  sort?: Maybe<AnnouncementSortParameter>;
  /** Allows the results to be filtered */
  filter?: Maybe<AnnouncementFilterParameter>;
  /** Specifies whether multiple "filter" arguments should be combines with a logical AND or OR operation. Defaults to AND. */
  filterOperator?: Maybe<LogicalOperator>;
};

export type AnnouncementSortParameter = {
  id?: Maybe<SortOrder>;
  createdAt?: Maybe<SortOrder>;
  updatedAt?: Maybe<SortOrder>;
  value?: Maybe<SortOrder>;
};

export type ApplicableProduct = {
  __typename?: 'ApplicableProduct';
  applicableType: ApplicableType;
  productIds?: Maybe<Array<Maybe<Scalars['ID']>>>;
};

export type ApplicableProductInput = {
  applicableType: ApplicableType;
  productIds?: Maybe<Array<Maybe<Scalars['ID']>>>;
};

export enum ApplicableType {
  All = 'all',
  AvailableGoods = 'availableGoods',
  UnusableGoods = 'unusableGoods'
}

export type ApplyCouponCodeResult = Order | CouponCodeExpiredError | CouponCodeInvalidError | CouponCodeLimitError;

export type ApplyDiscount = {
  __typename?: 'ApplyDiscount';
  order?: Maybe<Order>;
  removeDiscounts?: Maybe<Array<Maybe<PromotionType>>>;
};

export type Asset = Node & {
  __typename?: 'Asset';
  id: Scalars['ID'];
  createdAt: Scalars['DateTime'];
  updatedAt: Scalars['DateTime'];
  name: Scalars['String'];
  type: AssetType;
  fileSize: Scalars['Int'];
  mimeType: Scalars['String'];
  width: Scalars['Int'];
  height: Scalars['Int'];
  source: Scalars['String'];
  preview: Scalars['String'];
  focalPoint?: Maybe<Coordinate>;
  tags: Array<Tag>;
  customFields?: Maybe<AssetCustomFields>;
};

export type AssetCustomFields = {
  __typename?: 'AssetCustomFields';
  oldSource?: Maybe<Scalars['String']>;
  retries?: Maybe<Scalars['Int']>;
  isNeedHandle?: Maybe<Scalars['Boolean']>;
  isNeedPreprocess?: Maybe<Scalars['Boolean']>;
};

export type AssetList = PaginatedList & {
  __typename?: 'AssetList';
  items: Array<Asset>;
  totalItems: Scalars['Int'];
};

export enum AssetType {
  Image = 'IMAGE',
  Video = 'VIDEO',
  Binary = 'BINARY'
}

export type AssistGift = Node & {
  __typename?: 'AssistGift';
  id: Scalars['ID'];
  createdAt?: Maybe<Scalars['DateTime']>;
  updatedAt?: Maybe<Scalars['DateTime']>;
  blindBoxActivity?: Maybe<BlindBoxActivity>;
  blindBoxActivityId?: Maybe<Scalars['ID']>;
  giftName?: Maybe<Scalars['String']>;
  type?: Maybe<AssistGiftType>;
  giftPrice?: Maybe<Scalars['Int']>;
  giftImage?: Maybe<Scalars['String']>;
  coupon?: Maybe<Coupon>;
  targetId?: Maybe<Scalars['ID']>;
};

export type AssistGiftConfig = Node & {
  __typename?: 'AssistGiftConfig';
  id: Scalars['ID'];
  createdAt?: Maybe<Scalars['DateTime']>;
  updatedAt?: Maybe<Scalars['DateTime']>;
  blindBoxActivity?: Maybe<BlindBoxActivity>;
  blindBoxActivityId?: Maybe<Scalars['ID']>;
  isGiftForConvertedUser?: Maybe<Scalars['Boolean']>;
  isGiftForUnconvertedUser?: Maybe<Scalars['Boolean']>;
  convertedGift?: Maybe<AssistGift>;
  convertedGiftId?: Maybe<Scalars['ID']>;
  nonConvertedGiftLevels?: Maybe<Array<Maybe<NonConvertedGiftLevel>>>;
};

export type AssistGiftConfigFilterParameter = {
  id?: Maybe<IdOperators>;
  createdAt?: Maybe<DateOperators>;
  updatedAt?: Maybe<DateOperators>;
  blindBoxActivityId?: Maybe<IdOperators>;
  isGiftForConvertedUser?: Maybe<BooleanOperators>;
  isGiftForUnconvertedUser?: Maybe<BooleanOperators>;
  convertedGiftId?: Maybe<IdOperators>;
};

export type AssistGiftConfigInput = {
  id?: Maybe<Scalars['ID']>;
  blindBoxActivityId?: Maybe<Scalars['ID']>;
  isGiftForConvertedUser?: Maybe<Scalars['Boolean']>;
  isGiftForUnconvertedUser?: Maybe<Scalars['Boolean']>;
  convertedGiftId?: Maybe<Scalars['ID']>;
  targetId?: Maybe<Scalars['ID']>;
  assistGiftType?: Maybe<AssistGiftType>;
  giftImage?: Maybe<Scalars['String']>;
  nonConvertedGiftLevels: Array<NonConvertedGiftLevelInput>;
};

export type AssistGiftConfigList = PaginatedList & {
  __typename?: 'AssistGiftConfigList';
  items: Array<AssistGiftConfig>;
  totalItems: Scalars['Int'];
};

export type AssistGiftConfigListOptions = {
  /** Skips the first n results, for use in pagination */
  skip?: Maybe<Scalars['Int']>;
  /** Takes n results, for use in pagination */
  take?: Maybe<Scalars['Int']>;
  /** Specifies which properties to sort the results by */
  sort?: Maybe<AssistGiftConfigSortParameter>;
  /** Allows the results to be filtered */
  filter?: Maybe<AssistGiftConfigFilterParameter>;
  /** Specifies whether multiple "filter" arguments should be combines with a logical AND or OR operation. Defaults to AND. */
  filterOperator?: Maybe<LogicalOperator>;
};

export type AssistGiftConfigSortParameter = {
  id?: Maybe<SortOrder>;
  createdAt?: Maybe<SortOrder>;
  updatedAt?: Maybe<SortOrder>;
  blindBoxActivityId?: Maybe<SortOrder>;
  convertedGiftId?: Maybe<SortOrder>;
};

export type AssistGiftFilterParameter = {
  id?: Maybe<IdOperators>;
  createdAt?: Maybe<DateOperators>;
  updatedAt?: Maybe<DateOperators>;
  blindBoxActivityId?: Maybe<IdOperators>;
  giftName?: Maybe<StringOperators>;
  type?: Maybe<StringOperators>;
  giftPrice?: Maybe<NumberOperators>;
  giftImage?: Maybe<StringOperators>;
  targetId?: Maybe<IdOperators>;
};

export type AssistGiftInput = {
  id?: Maybe<Scalars['ID']>;
  blindBoxActivityId?: Maybe<Scalars['ID']>;
  giftName?: Maybe<Scalars['String']>;
  type?: Maybe<AssistGiftType>;
  targetId?: Maybe<Scalars['ID']>;
};

export type AssistGiftList = PaginatedList & {
  __typename?: 'AssistGiftList';
  items: Array<AssistGift>;
  totalItems: Scalars['Int'];
};

export type AssistGiftListOptions = {
  /** Skips the first n results, for use in pagination */
  skip?: Maybe<Scalars['Int']>;
  /** Takes n results, for use in pagination */
  take?: Maybe<Scalars['Int']>;
  /** Specifies which properties to sort the results by */
  sort?: Maybe<AssistGiftSortParameter>;
  /** Allows the results to be filtered */
  filter?: Maybe<AssistGiftFilterParameter>;
  /** Specifies whether multiple "filter" arguments should be combines with a logical AND or OR operation. Defaults to AND. */
  filterOperator?: Maybe<LogicalOperator>;
};

export type AssistGiftRecord = Node & {
  __typename?: 'AssistGiftRecord';
  id: Scalars['ID'];
  createdAt?: Maybe<Scalars['DateTime']>;
  updatedAt?: Maybe<Scalars['DateTime']>;
  assistRecord?: Maybe<AssistRecord>;
  assistRecordId?: Maybe<Scalars['ID']>;
  assistGift?: Maybe<AssistGift>;
  assistGiftId?: Maybe<Scalars['ID']>;
  customer?: Maybe<Customer>;
  customerId?: Maybe<Scalars['ID']>;
  isConvertedUser?: Maybe<Scalars['Boolean']>;
  currentAssistCount?: Maybe<Scalars['Int']>;
  receivedAt?: Maybe<Scalars['DateTime']>;
};

export type AssistGiftRecordList = PaginatedList & {
  __typename?: 'AssistGiftRecordList';
  items: Array<AssistGiftRecord>;
  totalItems: Scalars['Int'];
};

export type AssistGiftSortParameter = {
  id?: Maybe<SortOrder>;
  createdAt?: Maybe<SortOrder>;
  updatedAt?: Maybe<SortOrder>;
  blindBoxActivityId?: Maybe<SortOrder>;
  giftName?: Maybe<SortOrder>;
  giftPrice?: Maybe<SortOrder>;
  giftImage?: Maybe<SortOrder>;
  targetId?: Maybe<SortOrder>;
};

export enum AssistGiftType {
  Coupon = 'coupon'
}

export type AssistRecord = Node & {
  __typename?: 'AssistRecord';
  id: Scalars['ID'];
  createdAt?: Maybe<Scalars['DateTime']>;
  updatedAt?: Maybe<Scalars['DateTime']>;
  blindBoxBuy?: Maybe<BlindBoxBuy>;
  blindBoxBuyId?: Maybe<Scalars['ID']>;
  blindBox?: Maybe<BlindBox>;
  blindBoxId?: Maybe<Scalars['ID']>;
  blindBoxOpenRecord?: Maybe<BlindBoxOpenRecord>;
  blindBoxOpenRecordId?: Maybe<Scalars['ID']>;
  customer?: Maybe<Customer>;
  customerId?: Maybe<Scalars['ID']>;
  assistCustomer?: Maybe<Customer>;
  assistCustomerId?: Maybe<Scalars['ID']>;
  assistAt?: Maybe<Scalars['DateTime']>;
  isSuccess?: Maybe<Scalars['Boolean']>;
  failureMessage?: Maybe<Scalars['String']>;
};

export type AssistRecordList = PaginatedList & {
  __typename?: 'AssistRecordList';
  items: Array<AssistRecord>;
  totalItems: Scalars['Int'];
};

export type AssistStatusResponse = {
  __typename?: 'AssistStatusResponse';
  isReward?: Maybe<Scalars['Boolean']>;
  isConvertedUser?: Maybe<Scalars['Boolean']>;
  assistGiftRecord?: Maybe<AssistGiftRecord>;
};

export enum AuditState {
  Agree = 'agree',
  Refuse = 'refuse'
}

export type AuthenticationInput = {
  native?: Maybe<NativeAuthInput>;
};

export type AuthenticationMethod = Node & {
  __typename?: 'AuthenticationMethod';
  id: Scalars['ID'];
  createdAt: Scalars['DateTime'];
  updatedAt: Scalars['DateTime'];
  strategy: Scalars['String'];
};

export type AuthenticationResult = CurrentUser | InvalidCredentialsError | NotVerifiedError;

export type AvailableCoupon = {
  __typename?: 'AvailableCoupon';
  coupon?: Maybe<Coupon>;
  quantity?: Maybe<Scalars['Int']>;
};

export type Banner = Node & {
  __typename?: 'Banner';
  id: Scalars['ID'];
  image: Scalars['String'];
  redirect: Scalars['String'];
  redirectType: RedirectType;
};

export type BannerFilterParameter = {
  id?: Maybe<IdOperators>;
  image?: Maybe<StringOperators>;
  redirect?: Maybe<StringOperators>;
  redirectType?: Maybe<StringOperators>;
};

export type BannerInput = {
  image: Scalars['String'];
  redirect: Scalars['String'];
  redirectType: RedirectType;
};

export type BannerList = PaginatedList & {
  __typename?: 'BannerList';
  items: Array<Banner>;
  totalItems: Scalars['Int'];
};

export type BannerListOptions = {
  /** Skips the first n results, for use in pagination */
  skip?: Maybe<Scalars['Int']>;
  /** Takes n results, for use in pagination */
  take?: Maybe<Scalars['Int']>;
  /** Specifies which properties to sort the results by */
  sort?: Maybe<BannerSortParameter>;
  /** Allows the results to be filtered */
  filter?: Maybe<BannerFilterParameter>;
  /** Specifies whether multiple "filter" arguments should be combines with a logical AND or OR operation. Defaults to AND. */
  filterOperator?: Maybe<LogicalOperator>;
};

export type BannerSortParameter = {
  id?: Maybe<SortOrder>;
  image?: Maybe<SortOrder>;
  redirect?: Maybe<SortOrder>;
};

export type BlindBox = Node & {
  __typename?: 'BlindBox';
  id: Scalars['ID'];
  createdAt?: Maybe<Scalars['DateTime']>;
  updatedAt?: Maybe<Scalars['DateTime']>;
  wishItemId?: Maybe<Scalars['ID']>;
  price?: Maybe<Scalars['Int']>;
  name?: Maybe<Scalars['String']>;
  description?: Maybe<Scalars['String']>;
  img?: Maybe<Scalars['String']>;
  blindBoxItems?: Maybe<Array<Maybe<BlindBoxItem>>>;
  wishBlindBoxItem?: Maybe<BlindBoxItem>;
  remarks?: Maybe<Scalars['String']>;
};

export type BlindBoxActivity = Node & {
  __typename?: 'BlindBoxActivity';
  id: Scalars['ID'];
  createdAt?: Maybe<Scalars['DateTime']>;
  updatedAt?: Maybe<Scalars['DateTime']>;
  name?: Maybe<Scalars['String']>;
  remarks?: Maybe<Scalars['String']>;
  description?: Maybe<Scalars['String']>;
  startAt?: Maybe<Scalars['DateTime']>;
  endAt?: Maybe<Scalars['DateTime']>;
  enabled?: Maybe<Scalars['Boolean']>;
  statue?: Maybe<BlindBoxActivityStatus>;
  isFreeShipping?: Maybe<Scalars['Boolean']>;
  freeShippingThreshold?: Maybe<Scalars['Int']>;
  price?: Maybe<Scalars['Int']>;
  baseBlindBoxProbability?: Maybe<Scalars['Int']>;
  baseBlindBox?: Maybe<BlindBox>;
  baseBlindBoxId?: Maybe<Scalars['ID']>;
  blindBoxActivityBoxLinks?: Maybe<Array<Maybe<BlindBoxActivityBoxLink>>>;
  assistLimit?: Maybe<Scalars['Int']>;
  assistPagePoster?: Maybe<ComponentValue>;
  assistPosterType?: Maybe<ComponentType>;
  openBoxMode?: Maybe<OpenBoxMode>;
  stock?: Maybe<Scalars['Int']>;
  sold?: Maybe<Scalars['Int']>;
};

export type BlindBoxActivityBoxLink = Node & {
  __typename?: 'BlindBoxActivityBoxLink';
  id: Scalars['ID'];
  createdAt?: Maybe<Scalars['DateTime']>;
  updatedAt?: Maybe<Scalars['DateTime']>;
  blindBoxActivity?: Maybe<BlindBoxActivity>;
  blindBoxActivityId?: Maybe<Scalars['ID']>;
  blindBox?: Maybe<BlindBox>;
  blindBoxId?: Maybe<Scalars['ID']>;
  maxAssistLimit?: Maybe<Scalars['Int']>;
  productProbabilities?: Maybe<Array<Maybe<Array<Maybe<ProductProbability>>>>>;
  assistLimits?: Maybe<Array<Maybe<Scalars['Int']>>>;
};

export type BlindBoxActivityBoxLinkInput = {
  blindBoxId?: Maybe<Scalars['ID']>;
  maxAssistLimit?: Maybe<Scalars['Int']>;
  productProbabilities?: Maybe<Array<Maybe<Array<Maybe<ProductProbabilityInput>>>>>;
  assistLimits?: Maybe<Array<Maybe<Scalars['Int']>>>;
};

export type BlindBoxActivityBoxLinkList = PaginatedList & {
  __typename?: 'BlindBoxActivityBoxLinkList';
  items: Array<BlindBoxActivityBoxLink>;
  totalItems: Scalars['Int'];
};

export type BlindBoxActivityFilterParameter = {
  id?: Maybe<IdOperators>;
  createdAt?: Maybe<DateOperators>;
  updatedAt?: Maybe<DateOperators>;
  name?: Maybe<StringOperators>;
  remarks?: Maybe<StringOperators>;
  description?: Maybe<StringOperators>;
  startAt?: Maybe<DateOperators>;
  endAt?: Maybe<DateOperators>;
  enabled?: Maybe<BooleanOperators>;
  statue?: Maybe<StringOperators>;
  isFreeShipping?: Maybe<BooleanOperators>;
  freeShippingThreshold?: Maybe<NumberOperators>;
  price?: Maybe<NumberOperators>;
  baseBlindBoxProbability?: Maybe<NumberOperators>;
  baseBlindBoxId?: Maybe<IdOperators>;
  assistLimit?: Maybe<NumberOperators>;
  assistPosterType?: Maybe<StringOperators>;
  openBoxMode?: Maybe<StringOperators>;
  stock?: Maybe<NumberOperators>;
  sold?: Maybe<NumberOperators>;
};

export type BlindBoxActivityInput = {
  id?: Maybe<Scalars['ID']>;
  name?: Maybe<Scalars['String']>;
  description?: Maybe<Scalars['String']>;
  remarks?: Maybe<Scalars['String']>;
  startAt?: Maybe<Scalars['DateTime']>;
  endAt?: Maybe<Scalars['DateTime']>;
  isFreeShipping?: Maybe<Scalars['Boolean']>;
  freeShippingThreshold?: Maybe<Scalars['Int']>;
  price?: Maybe<Scalars['Int']>;
  baseBlindBoxProbability?: Maybe<Scalars['Int']>;
  baseBlindBoxId?: Maybe<Scalars['ID']>;
  blindBoxActivityBoxLinks: Array<BlindBoxActivityBoxLinkInput>;
  assistLimit?: Maybe<Scalars['Int']>;
  assistPagePoster?: Maybe<ComponentValueInput>;
  assistPosterType?: Maybe<ComponentType>;
  openBoxMode?: Maybe<OpenBoxMode>;
  stock?: Maybe<Scalars['Int']>;
};

export type BlindBoxActivityLimitConfig = Node & {
  __typename?: 'BlindBoxActivityLimitConfig';
  id: Scalars['ID'];
  createdAt?: Maybe<Scalars['DateTime']>;
  updatedAt?: Maybe<Scalars['DateTime']>;
  maxBoxPurchaseLimit?: Maybe<Scalars['Int']>;
  maxAssistLimit?: Maybe<Scalars['Int']>;
  maxRefundLimit?: Maybe<Scalars['Int']>;
  assistValidityPeriod?: Maybe<Scalars['Int']>;
  purchaseLimitPeriod?: Maybe<LimitType>;
  purchaseLimit?: Maybe<Scalars['Int']>;
  assistLimitPeriod?: Maybe<LimitType>;
  assistLimit?: Maybe<Scalars['Int']>;
  refundLimitPeriod?: Maybe<LimitType>;
  refundLimit?: Maybe<Scalars['Int']>;
};

export type BlindBoxActivityLimitConfigInput = {
  maxBoxPurchaseLimit?: Maybe<Scalars['Int']>;
  maxAssistLimit?: Maybe<Scalars['Int']>;
  maxRefundLimit?: Maybe<Scalars['Int']>;
  assistValidityPeriod?: Maybe<Scalars['Int']>;
  purchaseLimitPeriod?: Maybe<LimitType>;
  purchaseLimit?: Maybe<Scalars['Int']>;
  assistLimitPeriod?: Maybe<LimitType>;
  assistLimit?: Maybe<Scalars['Int']>;
  refundLimitPeriod?: Maybe<LimitType>;
  refundLimit?: Maybe<Scalars['Int']>;
};

export type BlindBoxActivityLimitConfigList = PaginatedList & {
  __typename?: 'BlindBoxActivityLimitConfigList';
  items: Array<BlindBoxActivityLimitConfig>;
  totalItems: Scalars['Int'];
};

export type BlindBoxActivityList = PaginatedList & {
  __typename?: 'BlindBoxActivityList';
  items: Array<BlindBoxActivity>;
  totalItems: Scalars['Int'];
};

export type BlindBoxActivityListOptions = {
  /** Skips the first n results, for use in pagination */
  skip?: Maybe<Scalars['Int']>;
  /** Takes n results, for use in pagination */
  take?: Maybe<Scalars['Int']>;
  /** Specifies which properties to sort the results by */
  sort?: Maybe<BlindBoxActivitySortParameter>;
  /** Allows the results to be filtered */
  filter?: Maybe<BlindBoxActivityFilterParameter>;
  /** Specifies whether multiple "filter" arguments should be combines with a logical AND or OR operation. Defaults to AND. */
  filterOperator?: Maybe<LogicalOperator>;
};

export type BlindBoxActivitySortParameter = {
  id?: Maybe<SortOrder>;
  createdAt?: Maybe<SortOrder>;
  updatedAt?: Maybe<SortOrder>;
  name?: Maybe<SortOrder>;
  remarks?: Maybe<SortOrder>;
  description?: Maybe<SortOrder>;
  startAt?: Maybe<SortOrder>;
  endAt?: Maybe<SortOrder>;
  freeShippingThreshold?: Maybe<SortOrder>;
  price?: Maybe<SortOrder>;
  baseBlindBoxProbability?: Maybe<SortOrder>;
  baseBlindBoxId?: Maybe<SortOrder>;
  assistLimit?: Maybe<SortOrder>;
  stock?: Maybe<SortOrder>;
  sold?: Maybe<SortOrder>;
};

export type BlindBoxActivityStatistics = {
  __typename?: 'BlindBoxActivityStatistics';
  blindBoxId?: Maybe<Scalars['ID']>;
  blindBox?: Maybe<BlindBox>;
  purchaseCustomerCount?: Maybe<Scalars['Int']>;
  openWishBlindBoxCustomerCount?: Maybe<Scalars['Int']>;
  openNotWishBlindBoxCustomerCount?: Maybe<Scalars['Int']>;
  shareAssistCustomerCount?: Maybe<Scalars['Int']>;
  assistOpenBlindBoxCustomerCount?: Maybe<Scalars['Int']>;
};

export enum BlindBoxActivityStatus {
  Normal = 'normal',
  Failure = 'failure',
  NotStarted = 'notStarted',
  HaveEnded = 'haveEnded'
}

export type BlindBoxBuy = Node & {
  __typename?: 'BlindBoxBuy';
  id: Scalars['ID'];
  createdAt?: Maybe<Scalars['DateTime']>;
  updatedAt?: Maybe<Scalars['DateTime']>;
  code?: Maybe<Scalars['String']>;
  customer?: Maybe<Customer>;
  customerId?: Maybe<Scalars['ID']>;
  price?: Maybe<Scalars['Int']>;
  wishBlindBoxItem?: Maybe<BlindBoxItem>;
  blindBoxOpenRecords?: Maybe<Array<Maybe<BlindBoxOpenRecord>>>;
  baseBlindBox?: Maybe<BlindBox>;
  baseBlindBoxId?: Maybe<Scalars['ID']>;
  wishBlindBox?: Maybe<BlindBox>;
  wishBlindBoxId?: Maybe<Scalars['ID']>;
  paymentMetadata?: Maybe<Scalars['JSON']>;
  status?: Maybe<BlindBoxBuyStatus>;
  refundReason?: Maybe<Scalars['String']>;
  payStatus?: Maybe<BlindBoxBuyPayStatus>;
  paymentAt?: Maybe<Scalars['DateTime']>;
  assistLimit?: Maybe<Scalars['Int']>;
  assistCount?: Maybe<Scalars['Int']>;
  assistExpireAt?: Maybe<Scalars['DateTime']>;
  blindBoxActivity?: Maybe<BlindBoxActivity>;
  blindBoxActivityId?: Maybe<Scalars['ID']>;
  distributor?: Maybe<Distributor>;
  distributorId?: Maybe<Scalars['ID']>;
  pickupOpenRecord?: Maybe<BlindBoxOpenRecord>;
  pickupOpenRecordId?: Maybe<Scalars['ID']>;
  pickupAt?: Maybe<Scalars['DateTime']>;
  blindBoxRefundRecord?: Maybe<BlindBoxRefundRecord>;
  canOpen?: Maybe<Scalars['Boolean']>;
  maxAssistCount?: Maybe<Scalars['Int']>;
  openBoxMode?: Maybe<OpenBoxMode>;
};

export type BlindBoxBuyFilterParameter = {
  id?: Maybe<IdOperators>;
  createdAt?: Maybe<DateOperators>;
  updatedAt?: Maybe<DateOperators>;
  code?: Maybe<StringOperators>;
  customerId?: Maybe<IdOperators>;
  price?: Maybe<NumberOperators>;
  baseBlindBoxId?: Maybe<IdOperators>;
  wishBlindBoxId?: Maybe<IdOperators>;
  status?: Maybe<StringOperators>;
  payStatus?: Maybe<StringOperators>;
  paymentAt?: Maybe<DateOperators>;
  assistLimit?: Maybe<NumberOperators>;
  assistCount?: Maybe<NumberOperators>;
  assistExpireAt?: Maybe<DateOperators>;
  blindBoxActivityId?: Maybe<IdOperators>;
  distributorId?: Maybe<IdOperators>;
  pickupOpenRecordId?: Maybe<IdOperators>;
  pickupAt?: Maybe<DateOperators>;
  canOpen?: Maybe<BooleanOperators>;
  maxAssistCount?: Maybe<NumberOperators>;
  openBoxMode?: Maybe<StringOperators>;
};

export type BlindBoxBuyList = PaginatedList & {
  __typename?: 'BlindBoxBuyList';
  items: Array<BlindBoxBuy>;
  totalItems: Scalars['Int'];
};

export type BlindBoxBuyListOptions = {
  /** Skips the first n results, for use in pagination */
  skip?: Maybe<Scalars['Int']>;
  /** Takes n results, for use in pagination */
  take?: Maybe<Scalars['Int']>;
  /** Specifies which properties to sort the results by */
  sort?: Maybe<BlindBoxBuySortParameter>;
  /** Allows the results to be filtered */
  filter?: Maybe<BlindBoxBuyFilterParameter>;
  /** Specifies whether multiple "filter" arguments should be combines with a logical AND or OR operation. Defaults to AND. */
  filterOperator?: Maybe<LogicalOperator>;
};

export enum BlindBoxBuyPayStatus {
  PendingPay = 'pendingPay',
  Paid = 'paid'
}

export type BlindBoxBuySortParameter = {
  id?: Maybe<SortOrder>;
  createdAt?: Maybe<SortOrder>;
  updatedAt?: Maybe<SortOrder>;
  code?: Maybe<SortOrder>;
  customerId?: Maybe<SortOrder>;
  price?: Maybe<SortOrder>;
  baseBlindBoxId?: Maybe<SortOrder>;
  wishBlindBoxId?: Maybe<SortOrder>;
  paymentAt?: Maybe<SortOrder>;
  assistLimit?: Maybe<SortOrder>;
  assistCount?: Maybe<SortOrder>;
  assistExpireAt?: Maybe<SortOrder>;
  blindBoxActivityId?: Maybe<SortOrder>;
  distributorId?: Maybe<SortOrder>;
  pickupOpenRecordId?: Maybe<SortOrder>;
  pickupAt?: Maybe<SortOrder>;
  maxAssistCount?: Maybe<SortOrder>;
};

export enum BlindBoxBuyStatus {
  PendingOpen = 'pendingOpen',
  Opened = 'opened',
  Assisting = 'assisting',
  Delivered = 'delivered',
  PendingDelivery = 'pendingDelivery',
  Refunded = 'refunded'
}

export type BlindBoxFilterParameter = {
  id?: Maybe<IdOperators>;
  createdAt?: Maybe<DateOperators>;
  updatedAt?: Maybe<DateOperators>;
  wishItemId?: Maybe<IdOperators>;
  price?: Maybe<NumberOperators>;
  name?: Maybe<StringOperators>;
  description?: Maybe<StringOperators>;
  img?: Maybe<StringOperators>;
  remarks?: Maybe<StringOperators>;
};

export type BlindBoxInput = {
  id?: Maybe<Scalars['ID']>;
  price?: Maybe<Scalars['Int']>;
  name?: Maybe<Scalars['String']>;
  description?: Maybe<Scalars['String']>;
  remarks?: Maybe<Scalars['String']>;
  img?: Maybe<Scalars['String']>;
  blindBoxItems: Array<BlindBoxItemInput>;
};

export type BlindBoxItem = Node & {
  __typename?: 'BlindBoxItem';
  id: Scalars['ID'];
  createdAt?: Maybe<Scalars['DateTime']>;
  updatedAt?: Maybe<Scalars['DateTime']>;
  blindBox?: Maybe<BlindBox>;
  blindBoxId?: Maybe<Scalars['ID']>;
  productVariant?: Maybe<ProductVariant>;
  targetId?: Maybe<Scalars['ID']>;
  type?: Maybe<BlindBoxItemType>;
  baseProbability?: Maybe<Scalars['Float']>;
  isWishItem?: Maybe<Scalars['Boolean']>;
  openBoxImageUrl?: Maybe<Scalars['String']>;
};

export type BlindBoxItemInput = {
  id?: Maybe<Scalars['ID']>;
  targetId?: Maybe<Scalars['ID']>;
  type?: Maybe<BlindBoxItemType>;
  baseProbability?: Maybe<Scalars['Float']>;
  isWishItem?: Maybe<Scalars['Boolean']>;
  openBoxImageUrl?: Maybe<Scalars['String']>;
};

export type BlindBoxItemList = PaginatedList & {
  __typename?: 'BlindBoxItemList';
  items: Array<BlindBoxItem>;
  totalItems: Scalars['Int'];
};

export enum BlindBoxItemType {
  Product = 'product'
}

export type BlindBoxList = PaginatedList & {
  __typename?: 'BlindBoxList';
  items: Array<BlindBox>;
  totalItems: Scalars['Int'];
};

export type BlindBoxListOptions = {
  /** Skips the first n results, for use in pagination */
  skip?: Maybe<Scalars['Int']>;
  /** Takes n results, for use in pagination */
  take?: Maybe<Scalars['Int']>;
  /** Specifies which properties to sort the results by */
  sort?: Maybe<BlindBoxSortParameter>;
  /** Allows the results to be filtered */
  filter?: Maybe<BlindBoxFilterParameter>;
  /** Specifies whether multiple "filter" arguments should be combines with a logical AND or OR operation. Defaults to AND. */
  filterOperator?: Maybe<LogicalOperator>;
};

export type BlindBoxOpenRecord = Node & {
  __typename?: 'BlindBoxOpenRecord';
  id: Scalars['ID'];
  createdAt?: Maybe<Scalars['DateTime']>;
  updatedAt?: Maybe<Scalars['DateTime']>;
  blindBoxBuy?: Maybe<BlindBoxBuy>;
  blindBoxBuyId?: Maybe<Scalars['ID']>;
  blindBox?: Maybe<BlindBox>;
  blindBoxId?: Maybe<Scalars['ID']>;
  customer?: Maybe<Customer>;
  customerId?: Maybe<Scalars['ID']>;
  blindBoxItem?: Maybe<BlindBoxItem>;
  resultId?: Maybe<Scalars['ID']>;
  isWishedItem?: Maybe<Scalars['Boolean']>;
  openedAt?: Maybe<Scalars['DateTime']>;
  assistCount?: Maybe<Scalars['Int']>;
  currentAllProbability?: Maybe<Scalars['JSON']>;
  isFreeShipping?: Maybe<Scalars['Boolean']>;
  wishBlindBox?: Maybe<BlindBox>;
};

export type BlindBoxOpenRecordList = PaginatedList & {
  __typename?: 'BlindBoxOpenRecordList';
  items: Array<BlindBoxOpenRecord>;
  totalItems: Scalars['Int'];
};

export type BlindBoxOrderRecord = Node & {
  __typename?: 'BlindBoxOrderRecord';
  id: Scalars['ID'];
  createdAt?: Maybe<Scalars['DateTime']>;
  updatedAt?: Maybe<Scalars['DateTime']>;
  blindBoxBuy?: Maybe<BlindBoxBuy>;
  blindBoxBuyId?: Maybe<Scalars['ID']>;
  orderLine?: Maybe<OrderLine>;
  orderLineId?: Maybe<Scalars['ID']>;
};

export type BlindBoxOrderRecordList = PaginatedList & {
  __typename?: 'BlindBoxOrderRecordList';
  items: Array<BlindBoxOrderRecord>;
  totalItems: Scalars['Int'];
};

export type BlindBoxPaymentInput = {
  method: Scalars['String'];
  metadata: Scalars['JSON'];
};

export type BlindBoxPreviewProduct = {
  __typename?: 'BlindBoxPreviewProduct';
  wishBlindBox?: Maybe<BlindBox>;
  baseBlindBox?: Maybe<BlindBox>;
};

export type BlindBoxRefundRecord = Node & {
  __typename?: 'BlindBoxRefundRecord';
  id: Scalars['ID'];
  createdAt?: Maybe<Scalars['DateTime']>;
  updatedAt?: Maybe<Scalars['DateTime']>;
  customer?: Maybe<Customer>;
  customerId?: Maybe<Scalars['ID']>;
  refundReason?: Maybe<Scalars['String']>;
  blindBoxBuy?: Maybe<BlindBoxBuy>;
  blindBoxBuyId?: Maybe<Scalars['ID']>;
  refundAmount?: Maybe<Scalars['Int']>;
  refundAt?: Maybe<Scalars['DateTime']>;
};

export type BlindBoxSortParameter = {
  id?: Maybe<SortOrder>;
  createdAt?: Maybe<SortOrder>;
  updatedAt?: Maybe<SortOrder>;
  wishItemId?: Maybe<SortOrder>;
  price?: Maybe<SortOrder>;
  name?: Maybe<SortOrder>;
  description?: Maybe<SortOrder>;
  img?: Maybe<SortOrder>;
  remarks?: Maybe<SortOrder>;
};

export type BlindBoxTotalStatistics = {
  __typename?: 'BlindBoxTotalStatistics';
  assistCustomerCount?: Maybe<Scalars['Int']>;
  assistCount?: Maybe<Scalars['Int']>;
  assistCouponCustomerCount?: Maybe<Scalars['Int']>;
  couponPayStatistics?: Maybe<Scalars['Int']>;
  couponPayAmount?: Maybe<Scalars['Float']>;
  payConversionRate?: Maybe<Scalars['Float']>;
  assistNewCustomerCount?: Maybe<Scalars['Int']>;
  assistNewCustomerCouponCount?: Maybe<Scalars['Int']>;
  assistNewCustomerPayCount?: Maybe<Scalars['Int']>;
  assistNewCustomerPayConversionRate?: Maybe<Scalars['Float']>;
  assistNotPayCustomerFirstPayCount?: Maybe<Scalars['Int']>;
  assistPayCustomerFirstPayCount?: Maybe<Scalars['Int']>;
  blindBoxActivityVisit?: Maybe<Scalars['Int']>;
  blindBoxActivityPayCustomerCount?: Maybe<Scalars['Int']>;
  blindBoxPayConversionRate?: Maybe<Scalars['Float']>;
  blindBoxActivityStatistics?: Maybe<Array<Maybe<BlindBoxActivityStatistics>>>;
  couponStatistics?: Maybe<Array<Maybe<CouponStatistics>>>;
};

export type BooleanCustomFieldConfig = CustomField & {
  __typename?: 'BooleanCustomFieldConfig';
  name: Scalars['String'];
  type: Scalars['String'];
  list: Scalars['Boolean'];
  label?: Maybe<Array<LocalizedString>>;
  description?: Maybe<Array<LocalizedString>>;
  readonly?: Maybe<Scalars['Boolean']>;
  internal?: Maybe<Scalars['Boolean']>;
  nullable?: Maybe<Scalars['Boolean']>;
  ui?: Maybe<Scalars['JSON']>;
};

/** Operators for filtering on a list of Boolean fields */
export type BooleanListOperators = {
  inList: Scalars['Boolean'];
};

/** Operators for filtering on a Boolean field */
export type BooleanOperators = {
  eq?: Maybe<Scalars['Boolean']>;
  isNull?: Maybe<Scalars['Boolean']>;
};

export enum CardReturnMethod {
  CardRetreatOnly = 'CardRetreatOnly',
  ReturnCardRefund = 'ReturnCardRefund'
}

export type CartActivityCountdown = {
  __typename?: 'CartActivityCountdown';
  activityCountdown?: Maybe<ActivityCountdown>;
  activityTag?: Maybe<Scalars['String']>;
};

export type Channel = Node & {
  __typename?: 'Channel';
  id: Scalars['ID'];
  createdAt: Scalars['DateTime'];
  updatedAt: Scalars['DateTime'];
  code: Scalars['String'];
  token: Scalars['String'];
  defaultTaxZone?: Maybe<Zone>;
  defaultShippingZone?: Maybe<Zone>;
  defaultLanguageCode: LanguageCode;
  availableLanguageCodes?: Maybe<Array<LanguageCode>>;
  /** @deprecated Use defaultCurrencyCode instead */
  currencyCode: CurrencyCode;
  defaultCurrencyCode: CurrencyCode;
  availableCurrencyCodes: Array<CurrencyCode>;
  /** Not yet used - will be implemented in a future release. */
  trackInventory?: Maybe<Scalars['Boolean']>;
  /** Not yet used - will be implemented in a future release. */
  outOfStockThreshold?: Maybe<Scalars['Int']>;
  pricesIncludeTax: Scalars['Boolean'];
  seller?: Maybe<Seller>;
  customFields?: Maybe<ChannelCustomFields>;
};

export type ChannelAddress = Node & {
  __typename?: 'ChannelAddress';
  id: Scalars['ID'];
  createdAt: Scalars['DateTime'];
  updatedAt: Scalars['DateTime'];
  fullName?: Maybe<Scalars['String']>;
  company?: Maybe<Scalars['String']>;
  streetLine1: Scalars['String'];
  streetLine2?: Maybe<Scalars['String']>;
  city?: Maybe<Scalars['String']>;
  province?: Maybe<Scalars['String']>;
  postalCode?: Maybe<Scalars['String']>;
  country: Country;
  phoneNumber?: Maybe<Scalars['String']>;
  defaultShippingAddress?: Maybe<Scalars['Boolean']>;
  defaultBillingAddress?: Maybe<Scalars['Boolean']>;
  district?: Maybe<Scalars['String']>;
};

export type ChannelAddressFilterParameter = {
  id?: Maybe<IdOperators>;
  createdAt?: Maybe<DateOperators>;
  updatedAt?: Maybe<DateOperators>;
  fullName?: Maybe<StringOperators>;
  company?: Maybe<StringOperators>;
  streetLine1?: Maybe<StringOperators>;
  streetLine2?: Maybe<StringOperators>;
  city?: Maybe<StringOperators>;
  province?: Maybe<StringOperators>;
  postalCode?: Maybe<StringOperators>;
  phoneNumber?: Maybe<StringOperators>;
  defaultShippingAddress?: Maybe<BooleanOperators>;
  defaultBillingAddress?: Maybe<BooleanOperators>;
  district?: Maybe<StringOperators>;
};

export type ChannelAddressList = PaginatedList & {
  __typename?: 'ChannelAddressList';
  items: Array<ChannelAddress>;
  totalItems: Scalars['Int'];
};

export type ChannelAddressListOptions = {
  /** Skips the first n results, for use in pagination */
  skip?: Maybe<Scalars['Int']>;
  /** Takes n results, for use in pagination */
  take?: Maybe<Scalars['Int']>;
  /** Specifies which properties to sort the results by */
  sort?: Maybe<ChannelAddressSortParameter>;
  /** Allows the results to be filtered */
  filter?: Maybe<ChannelAddressFilterParameter>;
  /** Specifies whether multiple "filter" arguments should be combines with a logical AND or OR operation. Defaults to AND. */
  filterOperator?: Maybe<LogicalOperator>;
};

export type ChannelAddressSortParameter = {
  id?: Maybe<SortOrder>;
  createdAt?: Maybe<SortOrder>;
  updatedAt?: Maybe<SortOrder>;
  fullName?: Maybe<SortOrder>;
  company?: Maybe<SortOrder>;
  streetLine1?: Maybe<SortOrder>;
  streetLine2?: Maybe<SortOrder>;
  city?: Maybe<SortOrder>;
  province?: Maybe<SortOrder>;
  postalCode?: Maybe<SortOrder>;
  phoneNumber?: Maybe<SortOrder>;
  district?: Maybe<SortOrder>;
};

export type ChannelCustomFields = {
  __typename?: 'ChannelCustomFields';
  amount?: Maybe<Scalars['Int']>;
  points?: Maybe<Scalars['Int']>;
  isShowNavigation?: Maybe<Scalars['Boolean']>;
  isShowCategory?: Maybe<Scalars['Boolean']>;
  isShowCart?: Maybe<Scalars['Boolean']>;
  isShowMember?: Maybe<Scalars['Boolean']>;
  isShowMine?: Maybe<Scalars['Boolean']>;
  userAgreement?: Maybe<Scalars['String']>;
  privacyPolicy?: Maybe<Scalars['String']>;
  retroactivePeriod?: Maybe<Scalars['Int']>;
  matomoSiteId?: Maybe<Scalars['String']>;
  shopName?: Maybe<Scalars['String']>;
  shopLogo?: Maybe<Scalars['String']>;
  updateShippingAddressLimitTime?: Maybe<Scalars['Int']>;
};

export type CheckExclusionResult = {
  __typename?: 'CheckExclusionResult';
  canCheckout?: Maybe<Scalars['Boolean']>;
  reason?: Maybe<Scalars['String']>;
};

export type CheckinConfig = Node & {
  __typename?: 'CheckinConfig';
  id: Scalars['ID'];
  createdAt?: Maybe<Scalars['DateTime']>;
  updatedAt?: Maybe<Scalars['DateTime']>;
  rewardType: RewardType;
  checkinCycle?: Maybe<CheckinCycle>;
  content?: Maybe<Scalars['String']>;
  checkinPrizeDays?: Maybe<Array<Maybe<CheckinPrizeDay>>>;
};

export type CheckinConfigInput = {
  rewardType: RewardType;
  content?: Maybe<Scalars['String']>;
  checkinCycle?: Maybe<CheckinCycleInput>;
  checkinPrizeDays: Array<Maybe<CheckinPrizeDayInput>>;
};

export type CheckinCycle = Node & {
  __typename?: 'CheckinCycle';
  id: Scalars['ID'];
  createdAt?: Maybe<Scalars['DateTime']>;
  updatedAt?: Maybe<Scalars['DateTime']>;
  rewardType: RewardType;
  startDate?: Maybe<Scalars['Date']>;
  endDate?: Maybe<Scalars['Date']>;
  cycleDuration?: Maybe<Scalars['Int']>;
  autoGenerateNextCycle?: Maybe<Scalars['Boolean']>;
  isActive?: Maybe<Scalars['Boolean']>;
};

export type CheckinCycleInput = {
  rewardType: RewardType;
  startDate?: Maybe<Scalars['Date']>;
  endDate?: Maybe<Scalars['Date']>;
  cycleDuration?: Maybe<Scalars['Int']>;
  autoGenerateNextCycle?: Maybe<Scalars['Boolean']>;
};

export type CheckinPrize = Node & {
  __typename?: 'CheckinPrize';
  id: Scalars['ID'];
  createdAt?: Maybe<Scalars['DateTime']>;
  updatedAt?: Maybe<Scalars['DateTime']>;
  checkinPrizeDay?: Maybe<CheckinPrizeDay>;
  checkinPrizeDayId?: Maybe<Scalars['ID']>;
  prizeType: PrizeType;
  coupon?: Maybe<Coupon>;
  targetId?: Maybe<Scalars['ID']>;
  quantity?: Maybe<Scalars['Int']>;
  version?: Maybe<Scalars['Int']>;
};

export type CheckinPrizeDay = Node & {
  __typename?: 'CheckinPrizeDay';
  id: Scalars['ID'];
  createdAt?: Maybe<Scalars['DateTime']>;
  updatedAt?: Maybe<Scalars['DateTime']>;
  checkinConfig?: Maybe<CheckinConfig>;
  checkinConfigId?: Maybe<Scalars['ID']>;
  checkinType: CheckinType;
  checkinPrizes?: Maybe<Array<Maybe<CheckinPrize>>>;
  limit?: Maybe<Scalars['Int']>;
  days?: Maybe<Scalars['Int']>;
  version?: Maybe<Scalars['Int']>;
};

export type CheckinPrizeDayInput = {
  checkinType: CheckinType;
  limit?: Maybe<Scalars['Int']>;
  days?: Maybe<Scalars['Int']>;
  checkinPrizes: Array<Maybe<CheckinPrizeInput>>;
};

export type CheckinPrizeInput = {
  prizeType: PrizeType;
  couponId?: Maybe<Scalars['ID']>;
  quantity?: Maybe<Scalars['Int']>;
};

export type CheckinRewardsConfig = {
  __typename?: 'CheckinRewardsConfig';
  content?: Maybe<Scalars['String']>;
  dailyPrizeDay?: Maybe<CheckinPrizeDay>;
};

export enum CheckinType {
  Daily = 'daily',
  Consecutive = 'consecutive'
}

export enum CloseReasonType {
  Normal = 'normal',
  MemberCardRefund = 'memberCardRefund'
}

export type Collection = Node & {
  __typename?: 'Collection';
  id: Scalars['ID'];
  createdAt: Scalars['DateTime'];
  updatedAt: Scalars['DateTime'];
  languageCode?: Maybe<LanguageCode>;
  name: Scalars['String'];
  slug: Scalars['String'];
  breadcrumbs: Array<CollectionBreadcrumb>;
  position: Scalars['Int'];
  description: Scalars['String'];
  featuredAsset?: Maybe<Asset>;
  assets: Array<Asset>;
  parent?: Maybe<Collection>;
  parentId: Scalars['ID'];
  children?: Maybe<Array<Collection>>;
  filters: Array<ConfigurableOperation>;
  translations: Array<CollectionTranslation>;
  productVariants: ProductVariantList;
  customFields?: Maybe<Scalars['JSON']>;
};


export type CollectionProductVariantsArgs = {
  options?: Maybe<ProductVariantListOptions>;
};

export type CollectionBreadcrumb = {
  __typename?: 'CollectionBreadcrumb';
  id: Scalars['ID'];
  name: Scalars['String'];
  slug: Scalars['String'];
};

export type CollectionFilterParameter = {
  id?: Maybe<IdOperators>;
  createdAt?: Maybe<DateOperators>;
  updatedAt?: Maybe<DateOperators>;
  languageCode?: Maybe<StringOperators>;
  name?: Maybe<StringOperators>;
  slug?: Maybe<StringOperators>;
  position?: Maybe<NumberOperators>;
  description?: Maybe<StringOperators>;
  parentId?: Maybe<IdOperators>;
};

export type CollectionList = PaginatedList & {
  __typename?: 'CollectionList';
  items: Array<Collection>;
  totalItems: Scalars['Int'];
};

export type CollectionListOptions = {
  topLevelOnly?: Maybe<Scalars['Boolean']>;
  /** Skips the first n results, for use in pagination */
  skip?: Maybe<Scalars['Int']>;
  /** Takes n results, for use in pagination */
  take?: Maybe<Scalars['Int']>;
  /** Specifies which properties to sort the results by */
  sort?: Maybe<CollectionSortParameter>;
  /** Allows the results to be filtered */
  filter?: Maybe<CollectionFilterParameter>;
  /** Specifies whether multiple "filter" arguments should be combines with a logical AND or OR operation. Defaults to AND. */
  filterOperator?: Maybe<LogicalOperator>;
};

/**
 * Which Collections are present in the products returned
 * by the search, and in what quantity.
 */
export type CollectionResult = {
  __typename?: 'CollectionResult';
  collection: Collection;
  count: Scalars['Int'];
};

export type CollectionSortParameter = {
  id?: Maybe<SortOrder>;
  createdAt?: Maybe<SortOrder>;
  updatedAt?: Maybe<SortOrder>;
  name?: Maybe<SortOrder>;
  slug?: Maybe<SortOrder>;
  position?: Maybe<SortOrder>;
  description?: Maybe<SortOrder>;
  parentId?: Maybe<SortOrder>;
};

export type CollectionTranslation = {
  __typename?: 'CollectionTranslation';
  id: Scalars['ID'];
  createdAt: Scalars['DateTime'];
  updatedAt: Scalars['DateTime'];
  languageCode: LanguageCode;
  name: Scalars['String'];
  slug: Scalars['String'];
  description: Scalars['String'];
};

export type CompActivity = Node & {
  __typename?: 'CompActivity';
  id: Scalars['ID'];
  name: Scalars['String'];
  minNumberOfPeriods: Scalars['Int'];
  state: ActivityState;
  activityAllotted?: Maybe<ActivityAllotted>;
  minActivityFee: Scalars['Int'];
  regulation?: Maybe<Scalars['String']>;
  amountRatio: Scalars['Int'];
  handler: Scalars['String'];
  channels?: Maybe<Array<Maybe<Channel>>>;
};

export type CompActivityInput = {
  name: Scalars['String'];
  minNumberOfPeriods: Scalars['Int'];
  state: ActivityState;
  activityAllotted: ActivityAllottedInput;
  minActivityFee: Scalars['Int'];
  regulation: Scalars['String'];
  amountRatio: Scalars['Int'];
  handler: Scalars['String'];
};

export type CompActivityList = PaginatedList & {
  __typename?: 'CompActivityList';
  items: Array<CompActivity>;
  totalItems: Scalars['Int'];
};

export type CompLogistics = {
  __typename?: 'CompLogistics';
  logisticCode?: Maybe<Scalars['String']>;
  state?: Maybe<LogisticState>;
  logisticInfo?: Maybe<LogisticInfo>;
  company?: Maybe<Scalars['String']>;
  compRecord?: Maybe<CompRecord>;
};

export type CompRecord = Node & {
  __typename?: 'CompRecord';
  id: Scalars['ID'];
  createdAt?: Maybe<Scalars['DateTime']>;
  updatedAt?: Maybe<Scalars['DateTime']>;
  channels?: Maybe<Array<Maybe<Channel>>>;
  shippingAddress?: Maybe<OrderAddress>;
  shippingInfo?: Maybe<ShippingInfo>;
  name?: Maybe<Scalars['String']>;
  description?: Maybe<Scalars['String']>;
  shouldShipping?: Maybe<Scalars['Boolean']>;
  handler?: Maybe<Scalars['String']>;
  img?: Maybe<Scalars['String']>;
  state?: Maybe<CompState>;
  price?: Maybe<Scalars['Int']>;
  customer?: Maybe<Customer>;
  subscription?: Maybe<Subscription>;
  openTime?: Maybe<Scalars['DateTime']>;
  pickTime?: Maybe<Scalars['DateTime']>;
  order?: Maybe<Order>;
  type?: Maybe<Scalars['String']>;
  compLogistics?: Maybe<CompLogistics>;
};

export type CompRecordFilterParameter = {
  id?: Maybe<IdOperators>;
  createdAt?: Maybe<DateOperators>;
  updatedAt?: Maybe<DateOperators>;
  name?: Maybe<StringOperators>;
  description?: Maybe<StringOperators>;
  shouldShipping?: Maybe<BooleanOperators>;
  handler?: Maybe<StringOperators>;
  img?: Maybe<StringOperators>;
  state?: Maybe<StringOperators>;
  price?: Maybe<NumberOperators>;
  openTime?: Maybe<DateOperators>;
  pickTime?: Maybe<DateOperators>;
  type?: Maybe<StringOperators>;
};

export type CompRecordList = PaginatedList & {
  __typename?: 'CompRecordList';
  items: Array<CompRecord>;
  totalItems: Scalars['Int'];
};

export type CompRecordListOptions = {
  /** Skips the first n results, for use in pagination */
  skip?: Maybe<Scalars['Int']>;
  /** Takes n results, for use in pagination */
  take?: Maybe<Scalars['Int']>;
  /** Specifies which properties to sort the results by */
  sort?: Maybe<CompRecordSortParameter>;
  /** Allows the results to be filtered */
  filter?: Maybe<CompRecordFilterParameter>;
  /** Specifies whether multiple "filter" arguments should be combines with a logical AND or OR operation. Defaults to AND. */
  filterOperator?: Maybe<LogicalOperator>;
};

export type CompRecordSortParameter = {
  id?: Maybe<SortOrder>;
  createdAt?: Maybe<SortOrder>;
  updatedAt?: Maybe<SortOrder>;
  name?: Maybe<SortOrder>;
  description?: Maybe<SortOrder>;
  handler?: Maybe<SortOrder>;
  img?: Maybe<SortOrder>;
  price?: Maybe<SortOrder>;
  openTime?: Maybe<SortOrder>;
  pickTime?: Maybe<SortOrder>;
  type?: Maybe<SortOrder>;
};

export enum CompState {
  Created = 'created',
  Draw = 'draw',
  PendingShipment = 'pendingShipment',
  Completed = 'completed'
}

export type ComplimentaryCouponObjectInput = {
  couponId: Scalars['ID'];
  quantity?: Maybe<Scalars['Int']>;
  complimentaryType: ComplimentaryType;
  membershipPlanIds?: Maybe<Array<Scalars['ID']>>;
};

export enum ComplimentaryType {
  All = 'all',
  Member = 'member'
}

export type Component = Node & {
  __typename?: 'Component';
  id: Scalars['ID'];
  createdAt?: Maybe<Scalars['DateTime']>;
  updatedAt?: Maybe<Scalars['DateTime']>;
  type: ComponentType;
  data: ComponentValue;
  sort: Scalars['Int'];
  remark?: Maybe<Scalars['String']>;
  enable: Scalars['Boolean'];
};

export type ComponentInput = {
  id?: Maybe<Scalars['ID']>;
  type: ComponentType;
  data: ComponentValueInput;
  sort: Scalars['Int'];
  remark?: Maybe<Scalars['String']>;
  enable: Scalars['Boolean'];
};

export enum ComponentType {
  Banner = 'banner',
  Picture = 'picture',
  Product = 'product',
  ProductItems = 'productItems',
  Video = 'video',
  Category = 'category',
  Checkin = 'checkin',
  Countdown = 'countdown',
  ShoppingCredits = 'shoppingCredits',
  CustomerLevel = 'customerLevel',
  Coupon = 'coupon',
  RollingCarousel = 'rollingCarousel',
  ProductCarousel = 'productCarousel',
  ShoppingCreditsConversion = 'shoppingCreditsConversion'
}

export enum CarouselType {
  Pictures = 'pictures',
  ProductItems = 'productItems'
}

export type ComponentValue = {
  __typename?: 'ComponentValue';
  layoutType?: Maybe<LayoutType>;
  pictures?: Maybe<Array<Maybe<PictureResource>>>;
  productGroup?: Maybe<ProductGroup>;
  productItems?: Maybe<ProductItems>;
  productCategoryList?: Maybe<Array<Maybe<ProductCategory>>>;
  backGroundColor?: Maybe<Scalars['String']>;
  backGroundImage?: Maybe<PictureResource>;
  fontColor?: Maybe<Scalars['String']>;
  borderColor?: Maybe<Scalars['String']>;
  borderInsideColor?: Maybe<Scalars['String']>;
  countdownEndTime?: Maybe<Scalars['DateTime']>;
  description?: Maybe<Scalars['String']>;
  targetId?: Maybe<Scalars['ID']>;
  carouselType?: Maybe<CarouselType>;
  placeHolder?: Maybe<Scalars['String']>;
};

export type ComponentValueInput = {
  layoutType?: Maybe<LayoutType>;
  pictures?: Maybe<Array<Maybe<PictureResourceInput>>>;
  productGroup?: Maybe<ProductGroupInput>;
  productItems?: Maybe<ProductItemsInput>;
  productCategoryList?: Maybe<Array<Maybe<ProductCategoryInput>>>;
  backGroundColor?: Maybe<Scalars['String']>;
  backGroundImage?: Maybe<PictureResourceInput>;
  fontColor?: Maybe<Scalars['String']>;
  borderColor?: Maybe<Scalars['String']>;
  borderInsideColor?: Maybe<Scalars['String']>;
  countdownEndTime?: Maybe<Scalars['DateTime']>;
  description?: Maybe<Scalars['String']>;
  targetId?: Maybe<Scalars['ID']>;
  carouselType?: Maybe<CarouselType>;
  placeHolder?: Maybe<Scalars['String']>;
};

export type ConfigArg = {
  __typename?: 'ConfigArg';
  name: Scalars['String'];
  value: Scalars['String'];
};

export type ConfigArgDefinition = {
  __typename?: 'ConfigArgDefinition';
  name: Scalars['String'];
  type: Scalars['String'];
  list: Scalars['Boolean'];
  required: Scalars['Boolean'];
  defaultValue?: Maybe<Scalars['JSON']>;
  label?: Maybe<Scalars['String']>;
  description?: Maybe<Scalars['String']>;
  ui?: Maybe<Scalars['JSON']>;
};

export type ConfigArgInput = {
  name: Scalars['String'];
  /** A JSON stringified representation of the actual value */
  value: Scalars['String'];
};

export type ConfigurableOperation = {
  __typename?: 'ConfigurableOperation';
  code: Scalars['String'];
  args: Array<ConfigArg>;
};

export type ConfigurableOperationDefinition = {
  __typename?: 'ConfigurableOperationDefinition';
  code: Scalars['String'];
  args: Array<ConfigArgDefinition>;
  description: Scalars['String'];
};

export type ConfigurableOperationInput = {
  code: Scalars['String'];
  arguments: Array<ConfigArgInput>;
};

export type ConsecutiveResult = {
  __typename?: 'ConsecutiveResult';
  consecutiveDays?: Maybe<Scalars['Int']>;
  isCheckedIn?: Maybe<Scalars['Boolean']>;
};

export type ConversionAnalysis = {
  __typename?: 'ConversionAnalysis';
  blindBoxVisitor?: Maybe<Scalars['Int']>;
  blindBoxView?: Maybe<Scalars['Int']>;
  forumVisitor?: Maybe<Scalars['Int']>;
  forumView?: Maybe<Scalars['Int']>;
  visitorsCount?: Maybe<Scalars['Int']>;
  pageViews?: Maybe<Scalars['Int']>;
  visitorsCountYesterdayNumber?: Maybe<Scalars['Int']>;
  pageViewsYesterday?: Maybe<Scalars['Int']>;
  proportionOfVisitors?: Maybe<Scalars['Float']>;
  visitorsCountCompareYesterday?: Maybe<Scalars['Float']>;
  proportionOfPageViews?: Maybe<Scalars['Float']>;
  pageViewsCompareYesterday?: Maybe<Scalars['Float']>;
  salesToday?: Maybe<Scalars['Float']>;
  salesYesterday?: Maybe<Scalars['Float']>;
  proportionOfSales?: Maybe<Scalars['Float']>;
  salesCompareYesterday?: Maybe<Scalars['Float']>;
  refundAmountToday?: Maybe<Scalars['Float']>;
  refundAmountYesterday?: Maybe<Scalars['Float']>;
  refundAmountCompareYesterday?: Maybe<Scalars['Float']>;
  salesVolumeToday?: Maybe<Scalars['Float']>;
  salesVolumeYesterday?: Maybe<Scalars['Float']>;
  salesVolumeCompareYesterday?: Maybe<Scalars['Float']>;
  customerUnitPriceToday?: Maybe<Scalars['Float']>;
  customerUnitPriceYesterday?: Maybe<Scalars['Float']>;
  customerUnitPriceCompareYesterday?: Maybe<Scalars['Float']>;
  orderNumberPaidToday?: Maybe<Scalars['Int']>;
  ordersPaidYesterday?: Maybe<Scalars['Int']>;
  proportionOfOrders?: Maybe<Scalars['Float']>;
  orderNumberPaidCompareYesterday?: Maybe<Scalars['Float']>;
  conversionRateOfVisitsToPaymentsToday?: Maybe<Scalars['Float']>;
  conversionRateOfVisitsToPaymentsYesterday?: Maybe<Scalars['Float']>;
  conversionRateOfVisitsToPaymentsCompareYesterday?: Maybe<Scalars['Float']>;
  newCardNumberToday?: Maybe<Scalars['Int']>;
  newCardNumberYesterday?: Maybe<Scalars['Int']>;
  newCardNumberCompareYesterday?: Maybe<Scalars['Float']>;
  cumulativeCardholders?: Maybe<Scalars['Int']>;
  cumulativeCardholdersYesterday?: Maybe<Scalars['Int']>;
  cumulativeCardholdersCompareYesterday?: Maybe<Scalars['Float']>;
  membershipCardPaymentFrequency?: Maybe<Scalars['Int']>;
  membershipCardPaymentFrequencyYesterday?: Maybe<Scalars['Int']>;
  membershipCardPaymentFrequencyCompareYesterday?: Maybe<Scalars['Float']>;
  membershipCardPaymentOrderNumber?: Maybe<Scalars['Int']>;
  membershipCardPaymentOrderNumberYesterday?: Maybe<Scalars['Int']>;
  membershipCardPaymentOrderNumberCompareYesterday?: Maybe<Scalars['Float']>;
  membershipCardPaymentAmount?: Maybe<Scalars['Float']>;
  membershipCardPaymentAmountYesterday?: Maybe<Scalars['Float']>;
  membershipCardPaymentAmountCompareYesterday?: Maybe<Scalars['Float']>;
  membershipCardPaymentAmountCompareSales?: Maybe<Scalars['Float']>;
};

export type Coordinate = {
  __typename?: 'Coordinate';
  x: Scalars['Float'];
  y: Scalars['Float'];
};

export type Country = Region & Node & {
  __typename?: 'Country';
  id: Scalars['ID'];
  createdAt: Scalars['DateTime'];
  updatedAt: Scalars['DateTime'];
  languageCode: LanguageCode;
  code: Scalars['String'];
  type: Scalars['String'];
  name: Scalars['String'];
  enabled: Scalars['Boolean'];
  parent?: Maybe<Region>;
  parentId?: Maybe<Scalars['ID']>;
  translations: Array<RegionTranslation>;
  customFields?: Maybe<Scalars['JSON']>;
};

export type CountryList = PaginatedList & {
  __typename?: 'CountryList';
  items: Array<Country>;
  totalItems: Scalars['Int'];
};

export type Coupon = Node & {
  __typename?: 'Coupon';
  id: Scalars['ID'];
  createdAt: Scalars['DateTime'];
  updatedAt: Scalars['DateTime'];
  name: Scalars['String'];
  remarks?: Maybe<Scalars['String']>;
  state?: Maybe<CouponState>;
  enable?: Maybe<Scalars['Boolean']>;
  type?: Maybe<CouponType>;
  preferentialContent?: Maybe<PreferentialContent>;
  validityPeriod?: Maybe<ValidityPeriod>;
  totalQuantity?: Maybe<Scalars['Int']>;
  applicableProduct?: Maybe<ApplicableProduct>;
  exchangeApplicableProduct?: Maybe<ApplicableProduct>;
  claimRestriction?: Maybe<Scalars['Int']>;
  whetherRestrictUsers?: Maybe<Scalars['Boolean']>;
  memberPlanIds?: Maybe<Array<Maybe<Scalars['ID']>>>;
  introduce?: Maybe<Scalars['String']>;
  receivedNumber?: Maybe<Scalars['Float']>;
  residualNumber?: Maybe<Scalars['Float']>;
  haveBeenUsedNumber?: Maybe<Scalars['Float']>;
  totalOrderAmount?: Maybe<Scalars['Float']>;
  customerUnitPrice?: Maybe<Scalars['Float']>;
  promotion?: Maybe<Promotion>;
  activityContent?: Maybe<Array<Maybe<Scalars['String']>>>;
  activityTime?: Maybe<Scalars['String']>;
  statisticsData?: Maybe<StatisticsData>;
  isShareable?: Maybe<Scalars['Boolean']>;
};

export type CouponBundle = Node & {
  __typename?: 'CouponBundle';
  id: Scalars['ID'];
  createdAt: Scalars['DateTime'];
  updatedAt: Scalars['DateTime'];
  name: Scalars['String'];
  remarks?: Maybe<Scalars['String']>;
  totalQuantity?: Maybe<Scalars['Int']>;
  receivedQuantity?: Maybe<Scalars['Int']>;
  state?: Maybe<CouponBundleState>;
  invalidCondition?: Maybe<InvalidCondition>;
  introduce?: Maybe<Scalars['String']>;
  smallProgramQRCodeLink?: Maybe<Scalars['String']>;
  couponBundleItems?: Maybe<Array<Maybe<CouponBundleItem>>>;
  availableCoupon?: Maybe<Array<Maybe<AvailableCoupon>>>;
};

export type CouponBundleFilterParameter = {
  id?: Maybe<IdOperators>;
  createdAt?: Maybe<DateOperators>;
  updatedAt?: Maybe<DateOperators>;
  name?: Maybe<StringOperators>;
  remarks?: Maybe<StringOperators>;
  totalQuantity?: Maybe<NumberOperators>;
  receivedQuantity?: Maybe<NumberOperators>;
  state?: Maybe<StringOperators>;
  invalidCondition?: Maybe<StringOperators>;
  introduce?: Maybe<StringOperators>;
  smallProgramQRCodeLink?: Maybe<StringOperators>;
};

export type CouponBundleInput = {
  id?: Maybe<Scalars['ID']>;
  name: Scalars['String'];
  remarks?: Maybe<Scalars['String']>;
  totalQuantity?: Maybe<Scalars['Int']>;
  introduce?: Maybe<Scalars['String']>;
  invalidCondition: InvalidCondition;
  couponBundleItems?: Maybe<Array<Maybe<CouponBundleItemInput>>>;
};

export type CouponBundleItem = Node & {
  __typename?: 'CouponBundleItem';
  id: Scalars['ID'];
  createdAt: Scalars['DateTime'];
  updatedAt: Scalars['DateTime'];
  couponBundleId: Scalars['ID'];
  couponId: Scalars['ID'];
  coupon?: Maybe<Coupon>;
  couponBundle?: Maybe<CouponBundle>;
  quantity?: Maybe<Scalars['Int']>;
};

export type CouponBundleItemInput = {
  couponId: Scalars['ID'];
  quantity: Scalars['Int'];
};

export type CouponBundleList = PaginatedList & {
  __typename?: 'CouponBundleList';
  items: Array<CouponBundle>;
  totalItems: Scalars['Int'];
};

export type CouponBundleListOptions = {
  /** Skips the first n results, for use in pagination */
  skip?: Maybe<Scalars['Int']>;
  /** Takes n results, for use in pagination */
  take?: Maybe<Scalars['Int']>;
  /** Specifies which properties to sort the results by */
  sort?: Maybe<CouponBundleSortParameter>;
  /** Allows the results to be filtered */
  filter?: Maybe<CouponBundleFilterParameter>;
  /** Specifies whether multiple "filter" arguments should be combines with a logical AND or OR operation. Defaults to AND. */
  filterOperator?: Maybe<LogicalOperator>;
};

export type CouponBundleSortParameter = {
  id?: Maybe<SortOrder>;
  createdAt?: Maybe<SortOrder>;
  updatedAt?: Maybe<SortOrder>;
  name?: Maybe<SortOrder>;
  remarks?: Maybe<SortOrder>;
  totalQuantity?: Maybe<SortOrder>;
  receivedQuantity?: Maybe<SortOrder>;
  introduce?: Maybe<SortOrder>;
  smallProgramQRCodeLink?: Maybe<SortOrder>;
};

export enum CouponBundleState {
  Normal = 'normal',
  Failure = 'failure'
}

/** Returned if the provided coupon code is invalid */
export type CouponCodeExpiredError = ErrorResult & {
  __typename?: 'CouponCodeExpiredError';
  errorCode: ErrorCode;
  message: Scalars['String'];
  couponCode: Scalars['String'];
};

/** Returned if the provided coupon code is invalid */
export type CouponCodeInvalidError = ErrorResult & {
  __typename?: 'CouponCodeInvalidError';
  errorCode: ErrorCode;
  message: Scalars['String'];
  couponCode: Scalars['String'];
};

/** Returned if the provided coupon code is invalid */
export type CouponCodeLimitError = ErrorResult & {
  __typename?: 'CouponCodeLimitError';
  errorCode: ErrorCode;
  message: Scalars['String'];
  couponCode: Scalars['String'];
  limit: Scalars['Int'];
};

export type CouponDataStatistic = {
  __typename?: 'CouponDataStatistic';
  couponAverage?: Maybe<Scalars['Int']>;
  totalPayment?: Maybe<Scalars['Int']>;
  totalOrders?: Maybe<Scalars['Int']>;
  customerCount?: Maybe<Scalars['Int']>;
  totalDiscountAmount?: Maybe<Scalars['Int']>;
  averageOrderValue?: Maybe<Scalars['Int']>;
  newCustomerCount?: Maybe<Scalars['Int']>;
  oldCustomerCount?: Maybe<Scalars['Int']>;
};

export type CouponFilterParameter = {
  id?: Maybe<IdOperators>;
  createdAt?: Maybe<DateOperators>;
  updatedAt?: Maybe<DateOperators>;
  name?: Maybe<StringOperators>;
  remarks?: Maybe<StringOperators>;
  state?: Maybe<StringOperators>;
  enable?: Maybe<BooleanOperators>;
  type?: Maybe<StringOperators>;
  totalQuantity?: Maybe<NumberOperators>;
  claimRestriction?: Maybe<NumberOperators>;
  whetherRestrictUsers?: Maybe<BooleanOperators>;
  introduce?: Maybe<StringOperators>;
  receivedNumber?: Maybe<NumberOperators>;
  residualNumber?: Maybe<NumberOperators>;
  haveBeenUsedNumber?: Maybe<NumberOperators>;
  totalOrderAmount?: Maybe<NumberOperators>;
  customerUnitPrice?: Maybe<NumberOperators>;
  activityTime?: Maybe<StringOperators>;
  isShareable?: Maybe<BooleanOperators>;
};

export type CouponHold = {
  __typename?: 'CouponHold';
  coupon?: Maybe<Coupon>;
  usableCoupons?: Maybe<Array<Maybe<UserCoupon>>>;
  expiredCoupons?: Maybe<Array<Maybe<UserCoupon>>>;
  usedCoupons?: Maybe<Array<Maybe<UserCoupon>>>;
  notStartedCoupons?: Maybe<Array<Maybe<UserCoupon>>>;
  residualNumber?: Maybe<Scalars['Int']>;
};

export type CouponInput = {
  id?: Maybe<Scalars['ID']>;
  name: Scalars['String'];
  remarks: Scalars['String'];
  type: CouponType;
  preferentialContent: PreferentialContentInput;
  validityPeriod: ValidityPeriodInput;
  totalQuantity: Scalars['Int'];
  applicableProduct: ApplicableProductInput;
  exchangeApplicableProduct?: Maybe<ApplicableProductInput>;
  claimRestriction: Scalars['Int'];
  whetherRestrictUsers: Scalars['Boolean'];
  memberPlanIds?: Maybe<Array<Maybe<Scalars['ID']>>>;
  introduce: Scalars['String'];
  isShareable?: Maybe<Scalars['Boolean']>;
};

export type CouponList = PaginatedList & {
  __typename?: 'CouponList';
  items: Array<Coupon>;
  totalItems: Scalars['Int'];
};

export type CouponListOptions = {
  /** Skips the first n results, for use in pagination */
  skip?: Maybe<Scalars['Int']>;
  /** Takes n results, for use in pagination */
  take?: Maybe<Scalars['Int']>;
  /** Specifies which properties to sort the results by */
  sort?: Maybe<CouponSortParameter>;
  /** Allows the results to be filtered */
  filter?: Maybe<CouponFilterParameter>;
  /** Specifies whether multiple "filter" arguments should be combines with a logical AND or OR operation. Defaults to AND. */
  filterOperator?: Maybe<LogicalOperator>;
};

export type CouponSortParameter = {
  id?: Maybe<SortOrder>;
  createdAt?: Maybe<SortOrder>;
  updatedAt?: Maybe<SortOrder>;
  name?: Maybe<SortOrder>;
  remarks?: Maybe<SortOrder>;
  totalQuantity?: Maybe<SortOrder>;
  claimRestriction?: Maybe<SortOrder>;
  introduce?: Maybe<SortOrder>;
  receivedNumber?: Maybe<SortOrder>;
  residualNumber?: Maybe<SortOrder>;
  haveBeenUsedNumber?: Maybe<SortOrder>;
  totalOrderAmount?: Maybe<SortOrder>;
  customerUnitPrice?: Maybe<SortOrder>;
  activityTime?: Maybe<SortOrder>;
};

export enum CouponState {
  Normal = 'normal',
  Failure = 'failure',
  NotStarted = 'notStarted',
  HaveEnded = 'haveEnded'
}

export type CouponStatistics = {
  __typename?: 'CouponStatistics';
  couponId?: Maybe<Scalars['ID']>;
  couponName?: Maybe<Scalars['String']>;
  couponCustomerCount?: Maybe<Scalars['Int']>;
  couponPayCustomerCount?: Maybe<Scalars['Int']>;
  payConversionRate?: Maybe<Scalars['Float']>;
};

export enum CouponType {
  FullSubtraction = 'fullSubtraction',
  Discount = 'discount',
  Exchange = 'exchange'
}

export type CouponUsability = {
  __typename?: 'CouponUsability';
  availableCoupons?: Maybe<Array<Maybe<UserCoupon>>>;
  couponsAreNotAvailable?: Maybe<Array<Maybe<UserCoupon>>>;
  lackOfExchangeGoodsCoupons?: Maybe<Array<Maybe<UserCoupon>>>;
};

export type CreateAddressCustomFieldsInput = {
  district?: Maybe<Scalars['String']>;
};

export type CreateAddressInput = {
  fullName?: Maybe<Scalars['String']>;
  company?: Maybe<Scalars['String']>;
  streetLine1: Scalars['String'];
  streetLine2?: Maybe<Scalars['String']>;
  city?: Maybe<Scalars['String']>;
  province?: Maybe<Scalars['String']>;
  postalCode?: Maybe<Scalars['String']>;
  countryCode: Scalars['String'];
  phoneNumber?: Maybe<Scalars['String']>;
  defaultShippingAddress?: Maybe<Scalars['Boolean']>;
  defaultBillingAddress?: Maybe<Scalars['Boolean']>;
  customFields?: Maybe<CreateAddressCustomFieldsInput>;
};

export type CreateCustomerCustomFieldsInput = {
  distributorId?: Maybe<Scalars['ID']>;
  points?: Maybe<Scalars['Int']>;
  isModified?: Maybe<Scalars['Boolean']>;
  isSettleCommission?: Maybe<Scalars['Boolean']>;
  headPortrait?: Maybe<Scalars['String']>;
  gender?: Maybe<Scalars['String']>;
  dateBirth?: Maybe<Scalars['DateTime']>;
  wechatCode?: Maybe<Scalars['String']>;
  area?: Maybe<Scalars['String']>;
};

export type CreateCustomerInput = {
  title?: Maybe<Scalars['String']>;
  firstName: Scalars['String'];
  lastName: Scalars['String'];
  phoneNumber?: Maybe<Scalars['String']>;
  emailAddress: Scalars['String'];
  customFields?: Maybe<CreateCustomerCustomFieldsInput>;
};

export type CreateReviewProductInput = {
  productId: Scalars['ID'];
  orderId: Scalars['ID'];
  orderLineId: Scalars['ID'];
  title: Scalars['String'];
  description: Scalars['String'];
  stars: Scalars['Int'];
  reviewImgs?: Maybe<Array<Scalars['String']>>;
  customerNameIsPublic: Scalars['Boolean'];
};

export type CreateSubscriptionReviewInput = {
  subscriptionId: Scalars['ID'];
  title: Scalars['String'];
  description: Scalars['String'];
  stars: Scalars['Int'];
  reviewImgs?: Maybe<Array<Scalars['String']>>;
  customerNameIsPublic: Scalars['Boolean'];
};

/**
 * @description
 * ISO 4217 currency code
 *
 * @docsCategory common
 */
export enum CurrencyCode {
  /** United Arab Emirates dirham */
  Aed = 'AED',
  /** Afghan afghani */
  Afn = 'AFN',
  /** Albanian lek */
  All = 'ALL',
  /** Armenian dram */
  Amd = 'AMD',
  /** Netherlands Antillean guilder */
  Ang = 'ANG',
  /** Angolan kwanza */
  Aoa = 'AOA',
  /** Argentine peso */
  Ars = 'ARS',
  /** Australian dollar */
  Aud = 'AUD',
  /** Aruban florin */
  Awg = 'AWG',
  /** Azerbaijani manat */
  Azn = 'AZN',
  /** Bosnia and Herzegovina convertible mark */
  Bam = 'BAM',
  /** Barbados dollar */
  Bbd = 'BBD',
  /** Bangladeshi taka */
  Bdt = 'BDT',
  /** Bulgarian lev */
  Bgn = 'BGN',
  /** Bahraini dinar */
  Bhd = 'BHD',
  /** Burundian franc */
  Bif = 'BIF',
  /** Bermudian dollar */
  Bmd = 'BMD',
  /** Brunei dollar */
  Bnd = 'BND',
  /** Boliviano */
  Bob = 'BOB',
  /** Brazilian real */
  Brl = 'BRL',
  /** Bahamian dollar */
  Bsd = 'BSD',
  /** Bhutanese ngultrum */
  Btn = 'BTN',
  /** Botswana pula */
  Bwp = 'BWP',
  /** Belarusian ruble */
  Byn = 'BYN',
  /** Belize dollar */
  Bzd = 'BZD',
  /** Canadian dollar */
  Cad = 'CAD',
  /** Congolese franc */
  Cdf = 'CDF',
  /** Swiss franc */
  Chf = 'CHF',
  /** Chilean peso */
  Clp = 'CLP',
  /** Renminbi (Chinese) yuan */
  Cny = 'CNY',
  /** Colombian peso */
  Cop = 'COP',
  /** Costa Rican colon */
  Crc = 'CRC',
  /** Cuban convertible peso */
  Cuc = 'CUC',
  /** Cuban peso */
  Cup = 'CUP',
  /** Cape Verde escudo */
  Cve = 'CVE',
  /** Czech koruna */
  Czk = 'CZK',
  /** Djiboutian franc */
  Djf = 'DJF',
  /** Danish krone */
  Dkk = 'DKK',
  /** Dominican peso */
  Dop = 'DOP',
  /** Algerian dinar */
  Dzd = 'DZD',
  /** Egyptian pound */
  Egp = 'EGP',
  /** Eritrean nakfa */
  Ern = 'ERN',
  /** Ethiopian birr */
  Etb = 'ETB',
  /** Euro */
  Eur = 'EUR',
  /** Fiji dollar */
  Fjd = 'FJD',
  /** Falkland Islands pound */
  Fkp = 'FKP',
  /** Pound sterling */
  Gbp = 'GBP',
  /** Georgian lari */
  Gel = 'GEL',
  /** Ghanaian cedi */
  Ghs = 'GHS',
  /** Gibraltar pound */
  Gip = 'GIP',
  /** Gambian dalasi */
  Gmd = 'GMD',
  /** Guinean franc */
  Gnf = 'GNF',
  /** Guatemalan quetzal */
  Gtq = 'GTQ',
  /** Guyanese dollar */
  Gyd = 'GYD',
  /** Hong Kong dollar */
  Hkd = 'HKD',
  /** Honduran lempira */
  Hnl = 'HNL',
  /** Croatian kuna */
  Hrk = 'HRK',
  /** Haitian gourde */
  Htg = 'HTG',
  /** Hungarian forint */
  Huf = 'HUF',
  /** Indonesian rupiah */
  Idr = 'IDR',
  /** Israeli new shekel */
  Ils = 'ILS',
  /** Indian rupee */
  Inr = 'INR',
  /** Iraqi dinar */
  Iqd = 'IQD',
  /** Iranian rial */
  Irr = 'IRR',
  /** Icelandic króna */
  Isk = 'ISK',
  /** Jamaican dollar */
  Jmd = 'JMD',
  /** Jordanian dinar */
  Jod = 'JOD',
  /** Japanese yen */
  Jpy = 'JPY',
  /** Kenyan shilling */
  Kes = 'KES',
  /** Kyrgyzstani som */
  Kgs = 'KGS',
  /** Cambodian riel */
  Khr = 'KHR',
  /** Comoro franc */
  Kmf = 'KMF',
  /** North Korean won */
  Kpw = 'KPW',
  /** South Korean won */
  Krw = 'KRW',
  /** Kuwaiti dinar */
  Kwd = 'KWD',
  /** Cayman Islands dollar */
  Kyd = 'KYD',
  /** Kazakhstani tenge */
  Kzt = 'KZT',
  /** Lao kip */
  Lak = 'LAK',
  /** Lebanese pound */
  Lbp = 'LBP',
  /** Sri Lankan rupee */
  Lkr = 'LKR',
  /** Liberian dollar */
  Lrd = 'LRD',
  /** Lesotho loti */
  Lsl = 'LSL',
  /** Libyan dinar */
  Lyd = 'LYD',
  /** Moroccan dirham */
  Mad = 'MAD',
  /** Moldovan leu */
  Mdl = 'MDL',
  /** Malagasy ariary */
  Mga = 'MGA',
  /** Macedonian denar */
  Mkd = 'MKD',
  /** Myanmar kyat */
  Mmk = 'MMK',
  /** Mongolian tögrög */
  Mnt = 'MNT',
  /** Macanese pataca */
  Mop = 'MOP',
  /** Mauritanian ouguiya */
  Mru = 'MRU',
  /** Mauritian rupee */
  Mur = 'MUR',
  /** Maldivian rufiyaa */
  Mvr = 'MVR',
  /** Malawian kwacha */
  Mwk = 'MWK',
  /** Mexican peso */
  Mxn = 'MXN',
  /** Malaysian ringgit */
  Myr = 'MYR',
  /** Mozambican metical */
  Mzn = 'MZN',
  /** Namibian dollar */
  Nad = 'NAD',
  /** Nigerian naira */
  Ngn = 'NGN',
  /** Nicaraguan córdoba */
  Nio = 'NIO',
  /** Norwegian krone */
  Nok = 'NOK',
  /** Nepalese rupee */
  Npr = 'NPR',
  /** New Zealand dollar */
  Nzd = 'NZD',
  /** Omani rial */
  Omr = 'OMR',
  /** Panamanian balboa */
  Pab = 'PAB',
  /** Peruvian sol */
  Pen = 'PEN',
  /** Papua New Guinean kina */
  Pgk = 'PGK',
  /** Philippine peso */
  Php = 'PHP',
  /** Pakistani rupee */
  Pkr = 'PKR',
  /** Polish złoty */
  Pln = 'PLN',
  /** Paraguayan guaraní */
  Pyg = 'PYG',
  /** Qatari riyal */
  Qar = 'QAR',
  /** Romanian leu */
  Ron = 'RON',
  /** Serbian dinar */
  Rsd = 'RSD',
  /** Russian ruble */
  Rub = 'RUB',
  /** Rwandan franc */
  Rwf = 'RWF',
  /** Saudi riyal */
  Sar = 'SAR',
  /** Solomon Islands dollar */
  Sbd = 'SBD',
  /** Seychelles rupee */
  Scr = 'SCR',
  /** Sudanese pound */
  Sdg = 'SDG',
  /** Swedish krona/kronor */
  Sek = 'SEK',
  /** Singapore dollar */
  Sgd = 'SGD',
  /** Saint Helena pound */
  Shp = 'SHP',
  /** Sierra Leonean leone */
  Sll = 'SLL',
  /** Somali shilling */
  Sos = 'SOS',
  /** Surinamese dollar */
  Srd = 'SRD',
  /** South Sudanese pound */
  Ssp = 'SSP',
  /** São Tomé and Príncipe dobra */
  Stn = 'STN',
  /** Salvadoran colón */
  Svc = 'SVC',
  /** Syrian pound */
  Syp = 'SYP',
  /** Swazi lilangeni */
  Szl = 'SZL',
  /** Thai baht */
  Thb = 'THB',
  /** Tajikistani somoni */
  Tjs = 'TJS',
  /** Turkmenistan manat */
  Tmt = 'TMT',
  /** Tunisian dinar */
  Tnd = 'TND',
  /** Tongan paʻanga */
  Top = 'TOP',
  /** Turkish lira */
  Try = 'TRY',
  /** Trinidad and Tobago dollar */
  Ttd = 'TTD',
  /** New Taiwan dollar */
  Twd = 'TWD',
  /** Tanzanian shilling */
  Tzs = 'TZS',
  /** Ukrainian hryvnia */
  Uah = 'UAH',
  /** Ugandan shilling */
  Ugx = 'UGX',
  /** United States dollar */
  Usd = 'USD',
  /** Uruguayan peso */
  Uyu = 'UYU',
  /** Uzbekistan som */
  Uzs = 'UZS',
  /** Venezuelan bolívar soberano */
  Ves = 'VES',
  /** Vietnamese đồng */
  Vnd = 'VND',
  /** Vanuatu vatu */
  Vuv = 'VUV',
  /** Samoan tala */
  Wst = 'WST',
  /** CFA franc BEAC */
  Xaf = 'XAF',
  /** East Caribbean dollar */
  Xcd = 'XCD',
  /** CFA franc BCEAO */
  Xof = 'XOF',
  /** CFP franc (franc Pacifique) */
  Xpf = 'XPF',
  /** Yemeni rial */
  Yer = 'YER',
  /** South African rand */
  Zar = 'ZAR',
  /** Zambian kwacha */
  Zmw = 'ZMW',
  /** Zimbabwean dollar */
  Zwl = 'ZWL'
}

export type CurrentUser = {
  __typename?: 'CurrentUser';
  id: Scalars['ID'];
  identifier: Scalars['String'];
  channels: Array<CurrentUserChannel>;
};

export type CurrentUserChannel = {
  __typename?: 'CurrentUserChannel';
  id: Scalars['ID'];
  token: Scalars['String'];
  code: Scalars['String'];
  permissions: Array<Permission>;
};

export type CustomField = {
  name: Scalars['String'];
  type: Scalars['String'];
  list: Scalars['Boolean'];
  label?: Maybe<Array<LocalizedString>>;
  description?: Maybe<Array<LocalizedString>>;
  readonly?: Maybe<Scalars['Boolean']>;
  internal?: Maybe<Scalars['Boolean']>;
  nullable?: Maybe<Scalars['Boolean']>;
  ui?: Maybe<Scalars['JSON']>;
};

export type CustomFieldConfig = StringCustomFieldConfig | LocaleStringCustomFieldConfig | IntCustomFieldConfig | FloatCustomFieldConfig | BooleanCustomFieldConfig | DateTimeCustomFieldConfig | RelationCustomFieldConfig | TextCustomFieldConfig | LocaleTextCustomFieldConfig;

export type CustomPage = Node & {
  __typename?: 'CustomPage';
  id: Scalars['ID'];
  createdAt?: Maybe<Scalars['DateTime']>;
  updatedAt?: Maybe<Scalars['DateTime']>;
  type: PageType;
  title: Scalars['String'];
  showNotice: Scalars['Boolean'];
  showSearch: Scalars['Boolean'];
  showPopup?: Maybe<Scalars['Boolean']>;
  showCustomerService?: Maybe<Scalars['Boolean']>;
  showShoppingCart?: Maybe<Scalars['Boolean']>;
  popup?: Maybe<Popup>;
  shareTitle?: Maybe<Scalars['String']>;
  shareImg?: Maybe<Scalars['String']>;
  shareDescription?: Maybe<Scalars['String']>;
  enable: Scalars['Boolean'];
  components?: Maybe<Array<Component>>;
  channels?: Maybe<Array<Maybe<Channel>>>;
  timingAt?: Maybe<Scalars['DateTime']>;
  remark?: Maybe<Scalars['String']>;
  modifiedContent?: Maybe<Scalars['JSON']>;
};

export type CustomPageFilterParameter = {
  id?: Maybe<IdOperators>;
  createdAt?: Maybe<DateOperators>;
  updatedAt?: Maybe<DateOperators>;
  type?: Maybe<StringOperators>;
  title?: Maybe<StringOperators>;
  showNotice?: Maybe<BooleanOperators>;
  showSearch?: Maybe<BooleanOperators>;
  showPopup?: Maybe<BooleanOperators>;
  showCustomerService?: Maybe<BooleanOperators>;
  showShoppingCart?: Maybe<BooleanOperators>;
  shareTitle?: Maybe<StringOperators>;
  shareImg?: Maybe<StringOperators>;
  shareDescription?: Maybe<StringOperators>;
  enable?: Maybe<BooleanOperators>;
  timingAt?: Maybe<DateOperators>;
  remark?: Maybe<StringOperators>;
};

export type CustomPageInput = {
  id?: Maybe<Scalars['ID']>;
  title: Scalars['String'];
  type: PageType;
  showNotice: Scalars['Boolean'];
  showSearch: Scalars['Boolean'];
  showPopup?: Maybe<Scalars['Boolean']>;
  showCustomerService?: Maybe<Scalars['Boolean']>;
  showShoppingCart?: Maybe<Scalars['Boolean']>;
  popup?: Maybe<PopupInput>;
  shareTitle?: Maybe<Scalars['String']>;
  shareImg?: Maybe<Scalars['String']>;
  shareDescription?: Maybe<Scalars['String']>;
  enable: Scalars['Boolean'];
  timingAt?: Maybe<Scalars['DateTime']>;
  components: Array<ComponentInput>;
  remark?: Maybe<Scalars['String']>;
};

export type CustomPageList = PaginatedList & {
  __typename?: 'CustomPageList';
  items: Array<CustomPage>;
  totalItems: Scalars['Int'];
};

export type CustomPageListOptions = {
  /** Skips the first n results, for use in pagination */
  skip?: Maybe<Scalars['Int']>;
  /** Takes n results, for use in pagination */
  take?: Maybe<Scalars['Int']>;
  /** Specifies which properties to sort the results by */
  sort?: Maybe<CustomPageSortParameter>;
  /** Allows the results to be filtered */
  filter?: Maybe<CustomPageFilterParameter>;
  /** Specifies whether multiple "filter" arguments should be combines with a logical AND or OR operation. Defaults to AND. */
  filterOperator?: Maybe<LogicalOperator>;
};

export type CustomPageSortParameter = {
  id?: Maybe<SortOrder>;
  createdAt?: Maybe<SortOrder>;
  updatedAt?: Maybe<SortOrder>;
  title?: Maybe<SortOrder>;
  shareTitle?: Maybe<SortOrder>;
  shareImg?: Maybe<SortOrder>;
  shareDescription?: Maybe<SortOrder>;
  timingAt?: Maybe<SortOrder>;
  remark?: Maybe<SortOrder>;
};

export type CustomPageStatistics = {
  __typename?: 'CustomPageStatistics';
  customPageId: Scalars['ID'];
  customPageTitle: Scalars['String'];
  pageViews?: Maybe<Scalars['Int']>;
  visitorsCount?: Maybe<Scalars['Int']>;
  clickCount?: Maybe<Scalars['Int']>;
  clickPeopleCount?: Maybe<Scalars['Int']>;
  clickRate?: Maybe<Scalars['Float']>;
  jumpLossRate?: Maybe<Scalars['Float']>;
  orderAmount?: Maybe<Scalars['Float']>;
  orderPeopleCount?: Maybe<Scalars['Int']>;
  orderConversionRate?: Maybe<Scalars['Float']>;
  paymentAmount?: Maybe<Scalars['Float']>;
  paymentPeopleCount?: Maybe<Scalars['Int']>;
  paymentConversionRate?: Maybe<Scalars['Float']>;
};

export type CustomPageStatisticsList = {
  __typename?: 'CustomPageStatisticsList';
  items?: Maybe<Array<Maybe<CustomPageStatistics>>>;
  totalItems?: Maybe<Scalars['Int']>;
};

export type Customer = Node & {
  __typename?: 'Customer';
  id: Scalars['ID'];
  createdAt: Scalars['DateTime'];
  updatedAt: Scalars['DateTime'];
  title?: Maybe<Scalars['String']>;
  firstName: Scalars['String'];
  lastName: Scalars['String'];
  phoneNumber?: Maybe<Scalars['String']>;
  emailAddress: Scalars['String'];
  addresses?: Maybe<Array<Address>>;
  orders: OrderList;
  user?: Maybe<User>;
  favorites: FavoriteList;
  isMember?: Maybe<Scalars['Boolean']>;
  membershipPlanName?: Maybe<Scalars['String']>;
  isDistributor?: Maybe<Scalars['Boolean']>;
  availablePoints?: Maybe<Scalars['Int']>;
  distributor?: Maybe<Distributor>;
  customFields?: Maybe<CustomerCustomFields>;
};


export type CustomerOrdersArgs = {
  options?: Maybe<OrderListOptions>;
};


export type CustomerFavoritesArgs = {
  options?: Maybe<FavoriteListOptions>;
  productNameFilter?: Maybe<Scalars['String']>;
};

export type CustomerAssistLimit = {
  __typename?: 'CustomerAssistLimit';
  assistLimitPeriod?: Maybe<LimitType>;
  assistLimit?: Maybe<Scalars['Int']>;
  remainingCount?: Maybe<Scalars['Int']>;
  limitPeriodCount?: Maybe<Scalars['Int']>;
};

export type CustomerBlindBoxLimit = {
  __typename?: 'CustomerBlindBoxLimit';
  purchaseLimitPeriod?: Maybe<LimitType>;
  purchaseLimit?: Maybe<Scalars['Int']>;
  remainingCount?: Maybe<Scalars['Int']>;
  limitPeriodCount?: Maybe<Scalars['Int']>;
};

export type CustomerCheckinRecords = Node & {
  __typename?: 'CustomerCheckinRecords';
  id: Scalars['ID'];
  createdAt?: Maybe<Scalars['DateTime']>;
  updatedAt?: Maybe<Scalars['DateTime']>;
  customer?: Maybe<Customer>;
  checkinDate?: Maybe<Scalars['DateTime']>;
  checkinCycle?: Maybe<CheckinCycle>;
};

export type CustomerCheckinRecordsInput = {
  year?: Maybe<Scalars['Int']>;
  month?: Maybe<Scalars['Int']>;
};

export type CustomerCheckinResult = {
  __typename?: 'CustomerCheckinResult';
  success?: Maybe<Scalars['Boolean']>;
  message?: Maybe<Scalars['String']>;
  totalPoints?: Maybe<Scalars['Int']>;
  couponCount?: Maybe<Scalars['Int']>;
  couponPracticalCount?: Maybe<Scalars['Int']>;
};

export type CustomerConsecutiveCheckin = Node & {
  __typename?: 'CustomerConsecutiveCheckin';
  id: Scalars['ID'];
  createdAt?: Maybe<Scalars['DateTime']>;
  updatedAt?: Maybe<Scalars['DateTime']>;
  customer?: Maybe<Customer>;
  consecutiveDays?: Maybe<Scalars['Int']>;
  checkinCycle?: Maybe<CheckinCycle>;
  lastCheckinDate?: Maybe<Scalars['DateTime']>;
};

export type CustomerCustomFields = {
  __typename?: 'CustomerCustomFields';
  distributor?: Maybe<Distributor>;
  points?: Maybe<Scalars['Int']>;
  isModified?: Maybe<Scalars['Boolean']>;
  isSettleCommission?: Maybe<Scalars['Boolean']>;
  headPortrait?: Maybe<Scalars['String']>;
  gender?: Maybe<Scalars['String']>;
  dateBirth?: Maybe<Scalars['DateTime']>;
  wechatCode?: Maybe<Scalars['String']>;
  area?: Maybe<Scalars['String']>;
};

export type CustomerFilterParameter = {
  id?: Maybe<IdOperators>;
  createdAt?: Maybe<DateOperators>;
  updatedAt?: Maybe<DateOperators>;
  title?: Maybe<StringOperators>;
  firstName?: Maybe<StringOperators>;
  lastName?: Maybe<StringOperators>;
  phoneNumber?: Maybe<StringOperators>;
  emailAddress?: Maybe<StringOperators>;
  isMember?: Maybe<BooleanOperators>;
  membershipPlanName?: Maybe<StringOperators>;
  isDistributor?: Maybe<BooleanOperators>;
  availablePoints?: Maybe<NumberOperators>;
  points?: Maybe<NumberOperators>;
  isModified?: Maybe<BooleanOperators>;
  isSettleCommission?: Maybe<BooleanOperators>;
  headPortrait?: Maybe<StringOperators>;
  gender?: Maybe<StringOperators>;
  dateBirth?: Maybe<DateOperators>;
  wechatCode?: Maybe<StringOperators>;
  area?: Maybe<StringOperators>;
};

export type CustomerGroup = Node & {
  __typename?: 'CustomerGroup';
  id: Scalars['ID'];
  createdAt: Scalars['DateTime'];
  updatedAt: Scalars['DateTime'];
  name: Scalars['String'];
  customers: CustomerList;
  customFields?: Maybe<Scalars['JSON']>;
};


export type CustomerGroupCustomersArgs = {
  options?: Maybe<CustomerListOptions>;
};

export type CustomerList = PaginatedList & {
  __typename?: 'CustomerList';
  items: Array<Customer>;
  totalItems: Scalars['Int'];
};

export type CustomerListOptions = {
  /** Skips the first n results, for use in pagination */
  skip?: Maybe<Scalars['Int']>;
  /** Takes n results, for use in pagination */
  take?: Maybe<Scalars['Int']>;
  /** Specifies which properties to sort the results by */
  sort?: Maybe<CustomerSortParameter>;
  /** Allows the results to be filtered */
  filter?: Maybe<CustomerFilterParameter>;
  /** Specifies whether multiple "filter" arguments should be combines with a logical AND or OR operation. Defaults to AND. */
  filterOperator?: Maybe<LogicalOperator>;
};

export type CustomerPrizeRecords = Node & {
  __typename?: 'CustomerPrizeRecords';
  id: Scalars['ID'];
  createdAt?: Maybe<Scalars['DateTime']>;
  updatedAt?: Maybe<Scalars['DateTime']>;
  customer?: Maybe<Customer>;
  prizeType: PrizeType;
  rewardType: RewardType;
  consecutiveDays?: Maybe<Scalars['Int']>;
  receivedAt?: Maybe<Scalars['DateTime']>;
  checkinCycle?: Maybe<CheckinCycle>;
  checkinPrizeDay?: Maybe<CheckinPrizeDay>;
  checkinPrizes?: Maybe<Array<Maybe<CheckinPrize>>>;
  customerCheckinRecords?: Maybe<CustomerCheckinRecords>;
};

export type CustomerPrizeResult = {
  __typename?: 'CustomerPrizeResult';
  eligiblePrizes?: Maybe<Array<Maybe<CheckinPrizeDay>>>;
  receivedPrizeRecords?: Maybe<Array<Maybe<CustomerPrizeRecords>>>;
};

export type CustomerReferralSource = Node & {
  __typename?: 'CustomerReferralSource';
  id: Scalars['ID'];
  createdAt?: Maybe<Scalars['DateTime']>;
  updatedAt?: Maybe<Scalars['DateTime']>;
  customerId?: Maybe<Scalars['ID']>;
  sourceType?: Maybe<CustomerReferralSourceType>;
  sourceValue?: Maybe<Scalars['String']>;
  additionalInfo?: Maybe<Scalars['String']>;
  channels?: Maybe<Array<Maybe<Channel>>>;
};

export type CustomerReferralSourceInput = {
  sourceType: CustomerReferralSourceType;
  sourceValue?: Maybe<Scalars['String']>;
  additionalInfo?: Maybe<Scalars['String']>;
};

export enum CustomerReferralSourceType {
  Wechat = 'wechat'
}

export type CustomerSortParameter = {
  id?: Maybe<SortOrder>;
  createdAt?: Maybe<SortOrder>;
  updatedAt?: Maybe<SortOrder>;
  title?: Maybe<SortOrder>;
  firstName?: Maybe<SortOrder>;
  lastName?: Maybe<SortOrder>;
  phoneNumber?: Maybe<SortOrder>;
  emailAddress?: Maybe<SortOrder>;
  membershipPlanName?: Maybe<SortOrder>;
  availablePoints?: Maybe<SortOrder>;
  distributor?: Maybe<SortOrder>;
  points?: Maybe<SortOrder>;
  isModified?: Maybe<SortOrder>;
  isSettleCommission?: Maybe<SortOrder>;
  headPortrait?: Maybe<SortOrder>;
  gender?: Maybe<SortOrder>;
  dateBirth?: Maybe<SortOrder>;
  wechatCode?: Maybe<SortOrder>;
  area?: Maybe<SortOrder>;
};

export type CustomerSource = Node & {
  __typename?: 'CustomerSource';
  id: Scalars['ID'];
  createdAt?: Maybe<Scalars['DateTime']>;
  updatedAt?: Maybe<Scalars['DateTime']>;
  customerId?: Maybe<Scalars['ID']>;
  sourceType?: Maybe<CustomerSourceType>;
  sourceValue?: Maybe<Scalars['String']>;
};

export type CustomerSourceInput = {
  sourceType: CustomerSourceType;
  sourceValue?: Maybe<Scalars['String']>;
};

export enum CustomerSourceType {
  BlindBoxAssist = 'blindBoxAssist',
  BlindBoxActivity = 'blindBoxActivity'
}

export type CustomerStatistic = {
  __typename?: 'CustomerStatistic';
  allCustomer?: Maybe<TransactionCustomerStatistic>;
  newCustomer?: Maybe<TransactionCustomerStatistic>;
  oldCustomer?: Maybe<TransactionCustomerStatistic>;
};

export enum CustomerType {
  AllCustomer = 'allCustomer',
  EffectiveCustomer = 'effectiveCustomer',
  OrderCustomer = 'orderCustomer',
  InvalidCustomer = 'invalidCustomer'
}

export type DataStatistics = {
  __typename?: 'DataStatistics';
  salesPrice?: Maybe<Scalars['Float']>;
  refundAmount?: Maybe<Scalars['Float']>;
  salesVolume?: Maybe<Scalars['Float']>;
  orderNumberToBeShipped?: Maybe<Scalars['Int']>;
  numberOfPendingRefunds?: Maybe<Scalars['Int']>;
  salesThisMonth?: Maybe<Scalars['Float']>;
  orderNumberPaidToday?: Maybe<Scalars['Int']>;
  ordersPaidYesterday?: Maybe<Scalars['Int']>;
  proportionOfOrders?: Maybe<Scalars['Float']>;
  orderNumberPaidCompareYesterday?: Maybe<Scalars['Float']>;
  salesToday?: Maybe<Scalars['Float']>;
  salesYesterday?: Maybe<Scalars['Float']>;
  proportionOfSales?: Maybe<Scalars['Float']>;
  salesCompareYesterday?: Maybe<Scalars['Float']>;
  numberOfPayersToday?: Maybe<Scalars['Int']>;
  numberOfPayersYesterday?: Maybe<Scalars['Int']>;
  proportionOfPayers?: Maybe<Scalars['Float']>;
  visitorsCount?: Maybe<Scalars['Int']>;
  pageViews?: Maybe<Scalars['Int']>;
  visitorsCountYesterdayNumber?: Maybe<Scalars['Int']>;
  pageViewsYesterday?: Maybe<Scalars['Int']>;
  proportionOfVisitors?: Maybe<Scalars['Float']>;
  visitorsCountCompareYesterday?: Maybe<Scalars['Float']>;
  proportionOfPageViews?: Maybe<Scalars['Float']>;
  pageViewsCompareYesterday?: Maybe<Scalars['Float']>;
  refundAmountToday?: Maybe<Scalars['Float']>;
  refundAmountYesterday?: Maybe<Scalars['Float']>;
  refundAmountCompareYesterday?: Maybe<Scalars['Float']>;
  salesVolumeToday?: Maybe<Scalars['Float']>;
  salesVolumeYesterday?: Maybe<Scalars['Float']>;
  salesVolumeCompareYesterday?: Maybe<Scalars['Float']>;
  customerUnitPriceToday?: Maybe<Scalars['Float']>;
  customerUnitPriceYesterday?: Maybe<Scalars['Float']>;
  customerUnitPriceCompareYesterday?: Maybe<Scalars['Float']>;
  conversionRateOfVisitsToPaymentsToday?: Maybe<Scalars['Float']>;
  conversionRateOfVisitsToPaymentsYesterday?: Maybe<Scalars['Float']>;
  conversionRateOfVisitsToPaymentsCompareYesterday?: Maybe<Scalars['Float']>;
  newCardNumberToday?: Maybe<Scalars['Int']>;
  newCardNumberYesterday?: Maybe<Scalars['Int']>;
  newCardNumberCompareYesterday?: Maybe<Scalars['Float']>;
  cumulativeCardholders?: Maybe<Scalars['Int']>;
  cumulativeCardholdersYesterday?: Maybe<Scalars['Int']>;
  cumulativeCardholdersCompareYesterday?: Maybe<Scalars['Float']>;
  membershipCardPaymentFrequency?: Maybe<Scalars['Int']>;
  membershipCardPaymentFrequencyYesterday?: Maybe<Scalars['Int']>;
  membershipCardPaymentFrequencyCompareYesterday?: Maybe<Scalars['Float']>;
  membershipCardPaymentOrderNumber?: Maybe<Scalars['Int']>;
  membershipCardPaymentOrderNumberYesterday?: Maybe<Scalars['Int']>;
  membershipCardPaymentOrderNumberCompareYesterday?: Maybe<Scalars['Float']>;
  membershipCardPaymentAmount?: Maybe<Scalars['Float']>;
  membershipCardPaymentAmountYesterday?: Maybe<Scalars['Float']>;
  membershipCardPaymentAmountCompareYesterday?: Maybe<Scalars['Float']>;
  membershipCardPaymentAmountCompareSales?: Maybe<Scalars['Float']>;
};


/** Operators for filtering on a list of Date fields */
export type DateListOperators = {
  inList: Scalars['DateTime'];
};

/** Operators for filtering on a DateTime field */
export type DateOperators = {
  eq?: Maybe<Scalars['DateTime']>;
  before?: Maybe<Scalars['DateTime']>;
  after?: Maybe<Scalars['DateTime']>;
  between?: Maybe<DateRange>;
  isNull?: Maybe<Scalars['Boolean']>;
};

export type DateRange = {
  start: Scalars['DateTime'];
  end: Scalars['DateTime'];
};


/**
 * Expects the same validation formats as the `<input type="datetime-local">` HTML element.
 * See https://developer.mozilla.org/en-US/docs/Web/HTML/Element/input/datetime-local#Additional_attributes
 */
export type DateTimeCustomFieldConfig = CustomField & {
  __typename?: 'DateTimeCustomFieldConfig';
  name: Scalars['String'];
  type: Scalars['String'];
  list: Scalars['Boolean'];
  label?: Maybe<Array<LocalizedString>>;
  description?: Maybe<Array<LocalizedString>>;
  readonly?: Maybe<Scalars['Boolean']>;
  internal?: Maybe<Scalars['Boolean']>;
  nullable?: Maybe<Scalars['Boolean']>;
  min?: Maybe<Scalars['String']>;
  max?: Maybe<Scalars['String']>;
  step?: Maybe<Scalars['Int']>;
  ui?: Maybe<Scalars['JSON']>;
};

export enum DateTimeType {
  Year = 'year',
  Month = 'month',
  Day = 'day'
}

export type DeletionResponse = {
  __typename?: 'DeletionResponse';
  result: DeletionResult;
  message?: Maybe<Scalars['String']>;
};

export enum DeletionResult {
  /** The entity was successfully deleted */
  Deleted = 'DELETED',
  /** Deletion did not take place, reason given in message */
  NotDeleted = 'NOT_DELETED'
}

export enum Direction {
  Up = 'up',
  Down = 'down'
}

export type Discount = {
  __typename?: 'Discount';
  adjustmentSource: Scalars['String'];
  type: AdjustmentType;
  description: Scalars['String'];
  amount: Scalars['Money'];
  amountWithTax: Scalars['Money'];
};

export type DiscountActivity = Node & {
  __typename?: 'DiscountActivity';
  id: Scalars['ID'];
  createdAt: Scalars['DateTime'];
  updatedAt: Scalars['DateTime'];
  displayName: Scalars['String'];
  name: Scalars['String'];
  remarks?: Maybe<Scalars['String']>;
  status?: Maybe<ActivityStatus>;
  startTime?: Maybe<Scalars['DateTime']>;
  endTime?: Maybe<Scalars['DateTime']>;
  introduce?: Maybe<Scalars['String']>;
  minimum?: Maybe<Scalars['Int']>;
  discount: Scalars['Int'];
  productIds?: Maybe<Array<Maybe<Scalars['ID']>>>;
  stackingDiscountSwitch?: Maybe<Scalars['Boolean']>;
  stackingPromotionTypes?: Maybe<Array<Maybe<PromotionType>>>;
  smallProgramQRCodeLink?: Maybe<Scalars['String']>;
  activityContent?: Maybe<Array<Maybe<Scalars['String']>>>;
  activitySynopsis?: Maybe<Scalars['String']>;
  activitySuperposition?: Maybe<Scalars['String']>;
  promotion?: Maybe<Promotion>;
  statisticsData?: Maybe<StatisticsData>;
  whetherRestrictUsers?: Maybe<Scalars['Boolean']>;
  groupType?: Maybe<GroupType>;
  memberPlanIds?: Maybe<Array<Maybe<Scalars['ID']>>>;
};

export type DiscountActivityFilterParameter = {
  id?: Maybe<IdOperators>;
  createdAt?: Maybe<DateOperators>;
  updatedAt?: Maybe<DateOperators>;
  displayName?: Maybe<StringOperators>;
  name?: Maybe<StringOperators>;
  remarks?: Maybe<StringOperators>;
  status?: Maybe<StringOperators>;
  startTime?: Maybe<DateOperators>;
  endTime?: Maybe<DateOperators>;
  introduce?: Maybe<StringOperators>;
  minimum?: Maybe<NumberOperators>;
  discount?: Maybe<NumberOperators>;
  stackingDiscountSwitch?: Maybe<BooleanOperators>;
  smallProgramQRCodeLink?: Maybe<StringOperators>;
  activitySynopsis?: Maybe<StringOperators>;
  activitySuperposition?: Maybe<StringOperators>;
  whetherRestrictUsers?: Maybe<BooleanOperators>;
  groupType?: Maybe<StringOperators>;
};

export type DiscountActivityInput = {
  id?: Maybe<Scalars['ID']>;
  name: Scalars['String'];
  displayName: Scalars['String'];
  remarks?: Maybe<Scalars['String']>;
  startTime: Scalars['DateTime'];
  endTime: Scalars['DateTime'];
  introduce: Scalars['String'];
  minimum: Scalars['Int'];
  discount: Scalars['Int'];
  productIds: Array<Scalars['ID']>;
  stackingDiscountSwitch: Scalars['Boolean'];
  stackingPromotionTypes?: Maybe<Array<Maybe<PromotionType>>>;
  whetherRestrictUsers?: Maybe<Scalars['Boolean']>;
  groupType?: Maybe<GroupType>;
  memberPlanIds?: Maybe<Array<Maybe<Scalars['ID']>>>;
};

export type DiscountActivityList = PaginatedList & {
  __typename?: 'DiscountActivityList';
  items: Array<DiscountActivity>;
  totalItems: Scalars['Int'];
};

export type DiscountActivityListOptions = {
  /** Skips the first n results, for use in pagination */
  skip?: Maybe<Scalars['Int']>;
  /** Takes n results, for use in pagination */
  take?: Maybe<Scalars['Int']>;
  /** Specifies which properties to sort the results by */
  sort?: Maybe<DiscountActivitySortParameter>;
  /** Allows the results to be filtered */
  filter?: Maybe<DiscountActivityFilterParameter>;
  /** Specifies whether multiple "filter" arguments should be combines with a logical AND or OR operation. Defaults to AND. */
  filterOperator?: Maybe<LogicalOperator>;
};

export type DiscountActivitySortParameter = {
  id?: Maybe<SortOrder>;
  createdAt?: Maybe<SortOrder>;
  updatedAt?: Maybe<SortOrder>;
  displayName?: Maybe<SortOrder>;
  name?: Maybe<SortOrder>;
  remarks?: Maybe<SortOrder>;
  startTime?: Maybe<SortOrder>;
  endTime?: Maybe<SortOrder>;
  introduce?: Maybe<SortOrder>;
  minimum?: Maybe<SortOrder>;
  discount?: Maybe<SortOrder>;
  smallProgramQRCodeLink?: Maybe<SortOrder>;
  activitySynopsis?: Maybe<SortOrder>;
  activitySuperposition?: Maybe<SortOrder>;
};

export type DiscountByType = {
  __typename?: 'DiscountByType';
  type?: Maybe<Scalars['String']>;
  discountAmount?: Maybe<Scalars['Int']>;
};

export type DiscountDetail = {
  __typename?: 'DiscountDetail';
  promInstanceId?: Maybe<Scalars['ID']>;
  type?: Maybe<Scalars['String']>;
  superimposeType?: Maybe<SuperimposeType>;
  superimposeTypes?: Maybe<Array<Maybe<Scalars['String']>>>;
  discountCount?: Maybe<Scalars['Int']>;
  discountAmount?: Maybe<Scalars['Int']>;
};

export enum DiscountType {
  FixedAmount = 'fixedAmount',
  FixedPercent = 'fixedPercent',
  NoDiscount = 'noDiscount'
}

export type DiscountValue = {
  __typename?: 'DiscountValue';
  discountType: DiscountType;
  discount: Scalars['Int'];
};

export type DiscountValueInput = {
  discountType: DiscountType;
  discount: Scalars['Int'];
};

export type DiscountsActivities = {
  __typename?: 'DiscountsActivities';
  discount?: Maybe<Discount>;
  activities?: Maybe<DiscountActivity>;
};

export type DiscountsCoupon = {
  __typename?: 'DiscountsCoupon';
  discount?: Maybe<Discount>;
  activities?: Maybe<UserCoupon>;
};

export type DiscountsFullPresent = {
  __typename?: 'DiscountsFullPresent';
  discount?: Maybe<Discount>;
  activities?: Maybe<FullDiscountPresent>;
};

export type DiscountsMember = {
  __typename?: 'DiscountsMember';
  discount?: Maybe<Discount>;
};

export type DiscountsPurchasePremium = {
  __typename?: 'DiscountsPurchasePremium';
  discount?: Maybe<Discount>;
  activities?: Maybe<PurchasePremium>;
};

export type Distributor = Node & {
  __typename?: 'Distributor';
  id: Scalars['ID'];
  createdAt?: Maybe<Scalars['DateTime']>;
  updatedAt?: Maybe<Scalars['DateTime']>;
  name?: Maybe<Scalars['String']>;
  phone?: Maybe<Scalars['String']>;
  customer?: Maybe<Customer>;
  distributorOrders?: Maybe<Array<Maybe<DistributorOrder>>>;
  distributorGroup?: Maybe<DistributorGroup>;
  orderTotal?: Maybe<Scalars['Int']>;
  customerCount?: Maybe<Scalars['Int']>;
  effectiveCustomerNum?: Maybe<Scalars['Int']>;
  distributorGroupId?: Maybe<Scalars['ID']>;
  distributorGroupName?: Maybe<Scalars['String']>;
  effectiveOrderNum?: Maybe<Scalars['Int']>;
  accumulationAmount?: Maybe<Scalars['Int']>;
  orderTotalAmount?: Maybe<Scalars['Int']>;
  detailTime?: Maybe<Scalars['DateTime']>;
  presentGroupId?: Maybe<Scalars['ID']>;
};

export type DistributorBinding = Node & {
  __typename?: 'DistributorBinding';
  id: Scalars['ID'];
  createdAt?: Maybe<Scalars['DateTime']>;
  updatedAt?: Maybe<Scalars['DateTime']>;
  distributor?: Maybe<Distributor>;
  distributorGroup?: Maybe<DistributorGroup>;
  customer?: Maybe<Customer>;
  orderTotal?: Maybe<Scalars['Int']>;
  orderTotalAmount?: Maybe<Scalars['Int']>;
  lastOrderTime?: Maybe<Scalars['DateTime']>;
  perCustomerTransaction?: Maybe<Scalars['Int']>;
  distributorCustomerRemark?: Maybe<Scalars['String']>;
};

export type DistributorBindingFilterParameter = {
  id?: Maybe<IdOperators>;
  createdAt?: Maybe<DateOperators>;
  updatedAt?: Maybe<DateOperators>;
  orderTotal?: Maybe<NumberOperators>;
  orderTotalAmount?: Maybe<NumberOperators>;
  lastOrderTime?: Maybe<DateOperators>;
  perCustomerTransaction?: Maybe<NumberOperators>;
  distributorCustomerRemark?: Maybe<StringOperators>;
};

export type DistributorBindingList = PaginatedList & {
  __typename?: 'DistributorBindingList';
  items: Array<DistributorBinding>;
  totalItems: Scalars['Int'];
};

export type DistributorBindingListOptions = {
  /** Skips the first n results, for use in pagination */
  skip?: Maybe<Scalars['Int']>;
  /** Takes n results, for use in pagination */
  take?: Maybe<Scalars['Int']>;
  /** Specifies which properties to sort the results by */
  sort?: Maybe<DistributorBindingSortParameter>;
  /** Allows the results to be filtered */
  filter?: Maybe<DistributorBindingFilterParameter>;
  /** Specifies whether multiple "filter" arguments should be combines with a logical AND or OR operation. Defaults to AND. */
  filterOperator?: Maybe<LogicalOperator>;
};

export type DistributorBindingMember = {
  __typename?: 'DistributorBindingMember';
  memberId?: Maybe<Scalars['ID']>;
  renewalCount?: Maybe<Scalars['Int']>;
  membershipPlanName?: Maybe<Scalars['String']>;
  customerName?: Maybe<Scalars['String']>;
  customerPhone?: Maybe<Scalars['String']>;
  customerHeadPortrait?: Maybe<Scalars['String']>;
  maturityAt?: Maybe<Scalars['DateTime']>;
  state?: Maybe<MemberStateInput>;
  maturityType?: Maybe<ValidityPeriodType>;
  isModified?: Maybe<Scalars['Boolean']>;
};

export type DistributorBindingMemberList = {
  __typename?: 'DistributorBindingMemberList';
  items: Array<DistributorBindingMember>;
  totalItems: Scalars['Int'];
};

export type DistributorBindingSortParameter = {
  id?: Maybe<SortOrder>;
  createdAt?: Maybe<SortOrder>;
  updatedAt?: Maybe<SortOrder>;
  orderTotal?: Maybe<SortOrder>;
  orderTotalAmount?: Maybe<SortOrder>;
  lastOrderTime?: Maybe<SortOrder>;
  perCustomerTransaction?: Maybe<SortOrder>;
  distributorCustomerRemark?: Maybe<SortOrder>;
};

export type DistributorCenterData = {
  __typename?: 'DistributorCenterData';
  todayDistributor?: Maybe<DistributorStatistics>;
  totalDistributor?: Maybe<DistributorStatistics>;
  distributor?: Maybe<Distributor>;
};

export type DistributorFilterParameter = {
  id?: Maybe<IdOperators>;
  createdAt?: Maybe<DateOperators>;
  updatedAt?: Maybe<DateOperators>;
  name?: Maybe<StringOperators>;
  phone?: Maybe<StringOperators>;
  orderTotal?: Maybe<NumberOperators>;
  customerCount?: Maybe<NumberOperators>;
  effectiveCustomerNum?: Maybe<NumberOperators>;
  distributorGroupId?: Maybe<IdOperators>;
  distributorGroupName?: Maybe<StringOperators>;
  effectiveOrderNum?: Maybe<NumberOperators>;
  accumulationAmount?: Maybe<NumberOperators>;
  orderTotalAmount?: Maybe<NumberOperators>;
  detailTime?: Maybe<DateOperators>;
  presentGroupId?: Maybe<IdOperators>;
};

export type DistributorGroup = Node & {
  __typename?: 'DistributorGroup';
  id: Scalars['ID'];
  createdAt?: Maybe<Scalars['DateTime']>;
  updatedAt?: Maybe<Scalars['DateTime']>;
  name?: Maybe<Scalars['String']>;
  orderTotal?: Maybe<Scalars['Int']>;
  customerCount?: Maybe<Scalars['Int']>;
  effectiveCustomerNum?: Maybe<Scalars['Int']>;
  effectiveOrderNum?: Maybe<Scalars['Int']>;
  accumulationAmount?: Maybe<Scalars['Int']>;
  detailTime?: Maybe<Scalars['DateTime']>;
  distributors?: Maybe<Array<Maybe<Distributor>>>;
};

export type DistributorGroupFilterParameter = {
  id?: Maybe<IdOperators>;
  createdAt?: Maybe<DateOperators>;
  updatedAt?: Maybe<DateOperators>;
  name?: Maybe<StringOperators>;
  orderTotal?: Maybe<NumberOperators>;
  customerCount?: Maybe<NumberOperators>;
  effectiveCustomerNum?: Maybe<NumberOperators>;
  effectiveOrderNum?: Maybe<NumberOperators>;
  accumulationAmount?: Maybe<NumberOperators>;
  detailTime?: Maybe<DateOperators>;
};

export type DistributorGroupInput = {
  id?: Maybe<Scalars['ID']>;
  name: Scalars['String'];
};

export type DistributorGroupList = PaginatedList & {
  __typename?: 'DistributorGroupList';
  items: Array<DistributorGroup>;
  totalItems: Scalars['Int'];
};

export type DistributorGroupListOptions = {
  /** Skips the first n results, for use in pagination */
  skip?: Maybe<Scalars['Int']>;
  /** Takes n results, for use in pagination */
  take?: Maybe<Scalars['Int']>;
  /** Specifies which properties to sort the results by */
  sort?: Maybe<DistributorGroupSortParameter>;
  /** Allows the results to be filtered */
  filter?: Maybe<DistributorGroupFilterParameter>;
  /** Specifies whether multiple "filter" arguments should be combines with a logical AND or OR operation. Defaults to AND. */
  filterOperator?: Maybe<LogicalOperator>;
};

export type DistributorGroupSortParameter = {
  id?: Maybe<SortOrder>;
  createdAt?: Maybe<SortOrder>;
  updatedAt?: Maybe<SortOrder>;
  name?: Maybe<SortOrder>;
  orderTotal?: Maybe<SortOrder>;
  customerCount?: Maybe<SortOrder>;
  effectiveCustomerNum?: Maybe<SortOrder>;
  effectiveOrderNum?: Maybe<SortOrder>;
  accumulationAmount?: Maybe<SortOrder>;
  detailTime?: Maybe<SortOrder>;
};

export type DistributorInput = {
  id?: Maybe<Scalars['ID']>;
  phone: Scalars['String'];
  name?: Maybe<Scalars['String']>;
  distributorGroupId: Scalars['ID'];
};

export type DistributorList = PaginatedList & {
  __typename?: 'DistributorList';
  items: Array<Distributor>;
  totalItems: Scalars['Int'];
};

export type DistributorListOptions = {
  /** Skips the first n results, for use in pagination */
  skip?: Maybe<Scalars['Int']>;
  /** Takes n results, for use in pagination */
  take?: Maybe<Scalars['Int']>;
  /** Specifies which properties to sort the results by */
  sort?: Maybe<DistributorSortParameter>;
  /** Allows the results to be filtered */
  filter?: Maybe<DistributorFilterParameter>;
  /** Specifies whether multiple "filter" arguments should be combines with a logical AND or OR operation. Defaults to AND. */
  filterOperator?: Maybe<LogicalOperator>;
};

export type DistributorOrder = Node & {
  __typename?: 'DistributorOrder';
  id: Scalars['ID'];
  createdAt?: Maybe<Scalars['DateTime']>;
  updatedAt?: Maybe<Scalars['DateTime']>;
  distributor?: Maybe<Distributor>;
  customer?: Maybe<Customer>;
  distributorGroup?: Maybe<DistributorGroup>;
  order?: Maybe<Order>;
};

export type DistributorOrderFilterParameter = {
  id?: Maybe<IdOperators>;
  createdAt?: Maybe<DateOperators>;
  updatedAt?: Maybe<DateOperators>;
};

export type DistributorOrderList = PaginatedList & {
  __typename?: 'DistributorOrderList';
  items: Array<DistributorOrder>;
  totalItems: Scalars['Int'];
};

export type DistributorOrderListOptions = {
  /** Skips the first n results, for use in pagination */
  skip?: Maybe<Scalars['Int']>;
  /** Takes n results, for use in pagination */
  take?: Maybe<Scalars['Int']>;
  /** Specifies which properties to sort the results by */
  sort?: Maybe<DistributorOrderSortParameter>;
  /** Allows the results to be filtered */
  filter?: Maybe<DistributorOrderFilterParameter>;
  /** Specifies whether multiple "filter" arguments should be combines with a logical AND or OR operation. Defaults to AND. */
  filterOperator?: Maybe<LogicalOperator>;
};

export type DistributorOrderSortParameter = {
  id?: Maybe<SortOrder>;
  createdAt?: Maybe<SortOrder>;
  updatedAt?: Maybe<SortOrder>;
};

export type DistributorProductRecord = Node & {
  __typename?: 'DistributorProductRecord';
  id: Scalars['ID'];
  createdAt?: Maybe<Scalars['DateTime']>;
  updatedAt?: Maybe<Scalars['DateTime']>;
  distributor?: Maybe<Distributor>;
  product?: Maybe<Product>;
  productStatistics?: Maybe<ProductStatistics>;
};

export type DistributorProductRecordFilterParameter = {
  id?: Maybe<IdOperators>;
  createdAt?: Maybe<DateOperators>;
  updatedAt?: Maybe<DateOperators>;
};

export type DistributorProductRecordList = PaginatedList & {
  __typename?: 'DistributorProductRecordList';
  items: Array<DistributorProductRecord>;
  totalItems: Scalars['Int'];
};

export type DistributorProductRecordListOptions = {
  /** Skips the first n results, for use in pagination */
  skip?: Maybe<Scalars['Int']>;
  /** Takes n results, for use in pagination */
  take?: Maybe<Scalars['Int']>;
  /** Specifies which properties to sort the results by */
  sort?: Maybe<DistributorProductRecordSortParameter>;
  /** Allows the results to be filtered */
  filter?: Maybe<DistributorProductRecordFilterParameter>;
  /** Specifies whether multiple "filter" arguments should be combines with a logical AND or OR operation. Defaults to AND. */
  filterOperator?: Maybe<LogicalOperator>;
};

export type DistributorProductRecordSortParameter = {
  id?: Maybe<SortOrder>;
  createdAt?: Maybe<SortOrder>;
  updatedAt?: Maybe<SortOrder>;
};

export type DistributorSharingInput = {
  type: SharingType;
  path: Scalars['String'];
  productId?: Maybe<Scalars['ID']>;
  shareType: ShareType;
  shareValue?: Maybe<Scalars['String']>;
  distributorId?: Maybe<Scalars['ID']>;
};

export type DistributorSortParameter = {
  id?: Maybe<SortOrder>;
  createdAt?: Maybe<SortOrder>;
  updatedAt?: Maybe<SortOrder>;
  name?: Maybe<SortOrder>;
  phone?: Maybe<SortOrder>;
  orderTotal?: Maybe<SortOrder>;
  customerCount?: Maybe<SortOrder>;
  effectiveCustomerNum?: Maybe<SortOrder>;
  distributorGroupId?: Maybe<SortOrder>;
  distributorGroupName?: Maybe<SortOrder>;
  effectiveOrderNum?: Maybe<SortOrder>;
  accumulationAmount?: Maybe<SortOrder>;
  orderTotalAmount?: Maybe<SortOrder>;
  detailTime?: Maybe<SortOrder>;
  presentGroupId?: Maybe<SortOrder>;
};

export type DistributorStatistics = {
  __typename?: 'DistributorStatistics';
  effectiveOrderNum?: Maybe<Scalars['Int']>;
  orderTotal?: Maybe<Scalars['Int']>;
  accumulationAmount?: Maybe<Scalars['Int']>;
  orderTotalAmount?: Maybe<Scalars['Int']>;
  distributorId?: Maybe<Scalars['ID']>;
  distributorGroupId?: Maybe<Scalars['ID']>;
  effectiveCustomerNum?: Maybe<Scalars['Int']>;
  customerCount?: Maybe<Scalars['Int']>;
  memberCustomerTotalCount?: Maybe<Scalars['Int']>;
  memberCustomerEffectiveCount?: Maybe<Scalars['Int']>;
};

/** Returned when attempting to create a Customer with an email address already registered to an existing User. */
export type EmailAddressConflictError = ErrorResult & {
  __typename?: 'EmailAddressConflictError';
  errorCode: ErrorCode;
  message: Scalars['String'];
};

export enum ErrorCode {
  UnknownError = 'UNKNOWN_ERROR',
  NativeAuthStrategyError = 'NATIVE_AUTH_STRATEGY_ERROR',
  InvalidCredentialsError = 'INVALID_CREDENTIALS_ERROR',
  OrderStateTransitionError = 'ORDER_STATE_TRANSITION_ERROR',
  EmailAddressConflictError = 'EMAIL_ADDRESS_CONFLICT_ERROR',
  GuestCheckoutError = 'GUEST_CHECKOUT_ERROR',
  OrderLimitError = 'ORDER_LIMIT_ERROR',
  NegativeQuantityError = 'NEGATIVE_QUANTITY_ERROR',
  InsufficientStockError = 'INSUFFICIENT_STOCK_ERROR',
  CouponCodeInvalidError = 'COUPON_CODE_INVALID_ERROR',
  CouponCodeExpiredError = 'COUPON_CODE_EXPIRED_ERROR',
  CouponCodeLimitError = 'COUPON_CODE_LIMIT_ERROR',
  OrderModificationError = 'ORDER_MODIFICATION_ERROR',
  IneligibleShippingMethodError = 'INELIGIBLE_SHIPPING_METHOD_ERROR',
  NoActiveOrderError = 'NO_ACTIVE_ORDER_ERROR',
  OrderPaymentStateError = 'ORDER_PAYMENT_STATE_ERROR',
  IneligiblePaymentMethodError = 'INELIGIBLE_PAYMENT_METHOD_ERROR',
  PaymentFailedError = 'PAYMENT_FAILED_ERROR',
  PaymentDeclinedError = 'PAYMENT_DECLINED_ERROR',
  AlreadyLoggedInError = 'ALREADY_LOGGED_IN_ERROR',
  MissingPasswordError = 'MISSING_PASSWORD_ERROR',
  PasswordValidationError = 'PASSWORD_VALIDATION_ERROR',
  PasswordAlreadySetError = 'PASSWORD_ALREADY_SET_ERROR',
  VerificationTokenInvalidError = 'VERIFICATION_TOKEN_INVALID_ERROR',
  VerificationTokenExpiredError = 'VERIFICATION_TOKEN_EXPIRED_ERROR',
  IdentifierChangeTokenInvalidError = 'IDENTIFIER_CHANGE_TOKEN_INVALID_ERROR',
  IdentifierChangeTokenExpiredError = 'IDENTIFIER_CHANGE_TOKEN_EXPIRED_ERROR',
  PasswordResetTokenInvalidError = 'PASSWORD_RESET_TOKEN_INVALID_ERROR',
  PasswordResetTokenExpiredError = 'PASSWORD_RESET_TOKEN_EXPIRED_ERROR',
  NotVerifiedError = 'NOT_VERIFIED_ERROR'
}

export type ErrorLogs = Node & {
  __typename?: 'ErrorLogs';
  id: Scalars['ID'];
  createdAt?: Maybe<Scalars['DateTime']>;
  updatedAt?: Maybe<Scalars['DateTime']>;
  activeUserId?: Maybe<Scalars['ID']>;
  errorType?: Maybe<Scalars['String']>;
  errorDetail?: Maybe<Scalars['JSON']>;
};

export type ErrorResult = {
  errorCode: ErrorCode;
  message: Scalars['String'];
};

export enum ExchangeConditionType {
  UnifiedExchangeRatio = 'unifiedExchangeRatio',
  CustomExchangeRatio = 'customExchangeRatio'
}

export type ExclusionGroup = Node & {
  __typename?: 'ExclusionGroup';
  id: Scalars['ID'];
  createdAt?: Maybe<Scalars['DateTime']>;
  updatedAt?: Maybe<Scalars['DateTime']>;
  name?: Maybe<Scalars['String']>;
  remarks?: Maybe<Scalars['String']>;
  type?: Maybe<ProductExclusionGroupType>;
  exclusionProducts?: Maybe<Array<Maybe<ExclusionProduct>>>;
};

export type ExclusionGroupFilterParameter = {
  id?: Maybe<IdOperators>;
  createdAt?: Maybe<DateOperators>;
  updatedAt?: Maybe<DateOperators>;
  name?: Maybe<StringOperators>;
  remarks?: Maybe<StringOperators>;
  type?: Maybe<StringOperators>;
};

export type ExclusionGroupInput = {
  id?: Maybe<Scalars['ID']>;
  name: Scalars['String'];
  remarks?: Maybe<Scalars['String']>;
  type: ProductExclusionGroupType;
  exclusionProducts?: Maybe<Array<Maybe<ExclusionProductInput>>>;
};

export type ExclusionGroupList = PaginatedList & {
  __typename?: 'ExclusionGroupList';
  items: Array<ExclusionGroup>;
  totalItems: Scalars['Int'];
};

export type ExclusionGroupListOptions = {
  /** Skips the first n results, for use in pagination */
  skip?: Maybe<Scalars['Int']>;
  /** Takes n results, for use in pagination */
  take?: Maybe<Scalars['Int']>;
  /** Specifies which properties to sort the results by */
  sort?: Maybe<ExclusionGroupSortParameter>;
  /** Allows the results to be filtered */
  filter?: Maybe<ExclusionGroupFilterParameter>;
  /** Specifies whether multiple "filter" arguments should be combines with a logical AND or OR operation. Defaults to AND. */
  filterOperator?: Maybe<LogicalOperator>;
};

export type ExclusionGroupSortParameter = {
  id?: Maybe<SortOrder>;
  createdAt?: Maybe<SortOrder>;
  updatedAt?: Maybe<SortOrder>;
  name?: Maybe<SortOrder>;
  remarks?: Maybe<SortOrder>;
};

export type ExclusionProduct = Node & {
  __typename?: 'ExclusionProduct';
  id: Scalars['ID'];
  createdAt?: Maybe<Scalars['DateTime']>;
  updatedAt?: Maybe<Scalars['DateTime']>;
  productId?: Maybe<Scalars['ID']>;
  exclusionGroupId?: Maybe<Scalars['ID']>;
  product?: Maybe<Product>;
  skuIds?: Maybe<Array<Maybe<Scalars['ID']>>>;
  exclusionGroup?: Maybe<ExclusionGroup>;
};

export type ExclusionProductInput = {
  productId: Scalars['ID'];
  skuIds?: Maybe<Array<Maybe<Scalars['ID']>>>;
};

export type ExternalLoginMutationArgs = {
  jsCode?: Maybe<Scalars['String']>;
  nickname?: Maybe<Scalars['String']>;
  profilePictureUrl?: Maybe<Scalars['String']>;
  md5Str?: Maybe<Scalars['String']>;
  sourceCode?: Maybe<Scalars['String']>;
  distributionId?: Maybe<Scalars['String']>;
};

export enum ExtraState {
  AfterSale = 'afterSale'
}

export type Facet = Node & {
  __typename?: 'Facet';
  id: Scalars['ID'];
  createdAt: Scalars['DateTime'];
  updatedAt: Scalars['DateTime'];
  languageCode: LanguageCode;
  name: Scalars['String'];
  code: Scalars['String'];
  values: Array<FacetValue>;
  translations: Array<FacetTranslation>;
  customFields?: Maybe<Scalars['JSON']>;
};

export type FacetFilterParameter = {
  id?: Maybe<IdOperators>;
  createdAt?: Maybe<DateOperators>;
  updatedAt?: Maybe<DateOperators>;
  languageCode?: Maybe<StringOperators>;
  name?: Maybe<StringOperators>;
  code?: Maybe<StringOperators>;
};

export type FacetList = PaginatedList & {
  __typename?: 'FacetList';
  items: Array<Facet>;
  totalItems: Scalars['Int'];
};

export type FacetListOptions = {
  /** Skips the first n results, for use in pagination */
  skip?: Maybe<Scalars['Int']>;
  /** Takes n results, for use in pagination */
  take?: Maybe<Scalars['Int']>;
  /** Specifies which properties to sort the results by */
  sort?: Maybe<FacetSortParameter>;
  /** Allows the results to be filtered */
  filter?: Maybe<FacetFilterParameter>;
  /** Specifies whether multiple "filter" arguments should be combines with a logical AND or OR operation. Defaults to AND. */
  filterOperator?: Maybe<LogicalOperator>;
};

export type FacetSortParameter = {
  id?: Maybe<SortOrder>;
  createdAt?: Maybe<SortOrder>;
  updatedAt?: Maybe<SortOrder>;
  name?: Maybe<SortOrder>;
  code?: Maybe<SortOrder>;
};

export type FacetTranslation = {
  __typename?: 'FacetTranslation';
  id: Scalars['ID'];
  createdAt: Scalars['DateTime'];
  updatedAt: Scalars['DateTime'];
  languageCode: LanguageCode;
  name: Scalars['String'];
};

export type FacetValue = Node & {
  __typename?: 'FacetValue';
  id: Scalars['ID'];
  createdAt: Scalars['DateTime'];
  updatedAt: Scalars['DateTime'];
  languageCode: LanguageCode;
  facet: Facet;
  name: Scalars['String'];
  code: Scalars['String'];
  translations: Array<FacetValueTranslation>;
  customFields?: Maybe<Scalars['JSON']>;
};

/**
 * Used to construct boolean expressions for filtering search results
 * by FacetValue ID. Examples:
 *
 * * ID=1 OR ID=2: `{ facetValueFilters: [{ or: [1,2] }] }`
 * * ID=1 AND ID=2: `{ facetValueFilters: [{ and: 1 }, { and: 2 }] }`
 * * ID=1 AND (ID=2 OR ID=3): `{ facetValueFilters: [{ and: 1 }, { or: [2,3] }] }`
 */
export type FacetValueFilterInput = {
  and?: Maybe<Scalars['ID']>;
  or?: Maybe<Array<Scalars['ID']>>;
};

/**
 * Which FacetValues are present in the products returned
 * by the search, and in what quantity.
 */
export type FacetValueResult = {
  __typename?: 'FacetValueResult';
  facetValue: FacetValue;
  count: Scalars['Int'];
};

export type FacetValueTranslation = {
  __typename?: 'FacetValueTranslation';
  id: Scalars['ID'];
  createdAt: Scalars['DateTime'];
  updatedAt: Scalars['DateTime'];
  languageCode: LanguageCode;
  name: Scalars['String'];
};

export type Favorite = Node & {
  __typename?: 'Favorite';
  id: Scalars['ID'];
  createdAt: Scalars['DateTime'];
  updatedAt: Scalars['DateTime'];
  product?: Maybe<Product>;
  customer: Customer;
};

export type FavoriteFilterParameter = {
  id?: Maybe<IdOperators>;
  createdAt?: Maybe<DateOperators>;
  updatedAt?: Maybe<DateOperators>;
};

export type FavoriteList = PaginatedList & {
  __typename?: 'FavoriteList';
  items: Array<Favorite>;
  totalItems: Scalars['Int'];
};

export type FavoriteListOptions = {
  /** Skips the first n results, for use in pagination */
  skip?: Maybe<Scalars['Int']>;
  /** Takes n results, for use in pagination */
  take?: Maybe<Scalars['Int']>;
  /** Specifies which properties to sort the results by */
  sort?: Maybe<FavoriteSortParameter>;
  /** Allows the results to be filtered */
  filter?: Maybe<FavoriteFilterParameter>;
  /** Specifies whether multiple "filter" arguments should be combines with a logical AND or OR operation. Defaults to AND. */
  filterOperator?: Maybe<LogicalOperator>;
};

export type FavoriteSortParameter = {
  id?: Maybe<SortOrder>;
  createdAt?: Maybe<SortOrder>;
  updatedAt?: Maybe<SortOrder>;
};

export type FileInput = {
  file: Scalars['Upload'];
};

export type FirstCustomerBenefit = Node & {
  __typename?: 'FirstCustomerBenefit';
  id: Scalars['ID'];
  createdAt?: Maybe<Scalars['DateTime']>;
  updatedAt?: Maybe<Scalars['DateTime']>;
  deletedAt?: Maybe<Scalars['DateTime']>;
  name?: Maybe<Scalars['String']>;
  remarks?: Maybe<Scalars['String']>;
  state?: Maybe<CouponBundleState>;
  invalidCondition?: Maybe<InvalidCondition>;
  introduce?: Maybe<Scalars['String']>;
  firstCustomerBenefitItems?: Maybe<Array<Maybe<FirstCustomerBenefitItem>>>;
  availableCoupon?: Maybe<Array<Maybe<AvailableCoupon>>>;
};

export type FirstCustomerBenefitFilterParameter = {
  id?: Maybe<IdOperators>;
  createdAt?: Maybe<DateOperators>;
  updatedAt?: Maybe<DateOperators>;
  deletedAt?: Maybe<DateOperators>;
  name?: Maybe<StringOperators>;
  remarks?: Maybe<StringOperators>;
  state?: Maybe<StringOperators>;
  invalidCondition?: Maybe<StringOperators>;
  introduce?: Maybe<StringOperators>;
};

export type FirstCustomerBenefitInput = {
  id?: Maybe<Scalars['ID']>;
  name: Scalars['String'];
  remarks?: Maybe<Scalars['String']>;
  invalidCondition: InvalidCondition;
  introduce?: Maybe<Scalars['String']>;
  firstCustomerBenefitItems?: Maybe<Array<Maybe<FirstCustomerBenefitItemInput>>>;
};

export type FirstCustomerBenefitItem = Node & {
  __typename?: 'FirstCustomerBenefitItem';
  id: Scalars['ID'];
  createdAt?: Maybe<Scalars['DateTime']>;
  updatedAt?: Maybe<Scalars['DateTime']>;
  firstCustomerBenefitId: Scalars['ID'];
  couponId: Scalars['ID'];
  coupon?: Maybe<Coupon>;
  quantity?: Maybe<Scalars['Int']>;
};

export type FirstCustomerBenefitItemInput = {
  couponId: Scalars['ID'];
  quantity: Scalars['Int'];
};

export type FirstCustomerBenefitList = PaginatedList & {
  __typename?: 'FirstCustomerBenefitList';
  items: Array<FirstCustomerBenefit>;
  totalItems: Scalars['Int'];
};

export type FirstCustomerBenefitListOptions = {
  /** Skips the first n results, for use in pagination */
  skip?: Maybe<Scalars['Int']>;
  /** Takes n results, for use in pagination */
  take?: Maybe<Scalars['Int']>;
  /** Specifies which properties to sort the results by */
  sort?: Maybe<FirstCustomerBenefitSortParameter>;
  /** Allows the results to be filtered */
  filter?: Maybe<FirstCustomerBenefitFilterParameter>;
  /** Specifies whether multiple "filter" arguments should be combines with a logical AND or OR operation. Defaults to AND. */
  filterOperator?: Maybe<LogicalOperator>;
};

export type FirstCustomerBenefitSortParameter = {
  id?: Maybe<SortOrder>;
  createdAt?: Maybe<SortOrder>;
  updatedAt?: Maybe<SortOrder>;
  deletedAt?: Maybe<SortOrder>;
  name?: Maybe<SortOrder>;
  remarks?: Maybe<SortOrder>;
  introduce?: Maybe<SortOrder>;
};

export type FloatCustomFieldConfig = CustomField & {
  __typename?: 'FloatCustomFieldConfig';
  name: Scalars['String'];
  type: Scalars['String'];
  list: Scalars['Boolean'];
  label?: Maybe<Array<LocalizedString>>;
  description?: Maybe<Array<LocalizedString>>;
  readonly?: Maybe<Scalars['Boolean']>;
  internal?: Maybe<Scalars['Boolean']>;
  nullable?: Maybe<Scalars['Boolean']>;
  min?: Maybe<Scalars['Float']>;
  max?: Maybe<Scalars['Float']>;
  step?: Maybe<Scalars['Float']>;
  ui?: Maybe<Scalars['JSON']>;
};

export type FloatingWindow = Node & {
  __typename?: 'FloatingWindow';
  id: Scalars['ID'];
  createdAt: Scalars['DateTime'];
  updatedAt: Scalars['DateTime'];
  deletedAt?: Maybe<Scalars['DateTime']>;
  isOpen: Scalars['Boolean'];
  width?: Maybe<Scalars['Int']>;
  height?: Maybe<Scalars['Int']>;
  isShowAfterReceived: Scalars['Boolean'];
  floatingWindowImage?: Maybe<Scalars['String']>;
  floatingWindowImageReceived?: Maybe<Scalars['String']>;
  jumpType: JumpType;
  jumpValue?: Maybe<Scalars['String']>;
};

export type FloatingWindowInput = {
  id?: Maybe<Scalars['ID']>;
  isOpen: Scalars['Boolean'];
  isShowAfterReceived: Scalars['Boolean'];
  width?: Maybe<Scalars['Int']>;
  height?: Maybe<Scalars['Int']>;
  floatingWindowImage?: Maybe<Scalars['String']>;
  floatingWindowImageReceived?: Maybe<Scalars['String']>;
  jumpType: JumpType;
  jumpValue?: Maybe<Scalars['String']>;
};

export type ForumActivity = Node & {
  __typename?: 'ForumActivity';
  id: Scalars['ID'];
  createdAt: Scalars['DateTime'];
  updatedAt: Scalars['DateTime'];
  deletedAt?: Maybe<Scalars['DateTime']>;
  name: Scalars['String'];
  remark?: Maybe<Scalars['String']>;
  description?: Maybe<Scalars['String']>;
  smallProgramQRCodeLink?: Maybe<Scalars['String']>;
  status: ForumActivityStatus;
  startTime?: Maybe<Scalars['DateTime']>;
  endTime?: Maybe<Scalars['DateTime']>;
  forumTag?: Maybe<ForumTag>;
  forumTagId?: Maybe<Scalars['ID']>;
  postCount?: Maybe<Scalars['Int']>;
  replyCount?: Maybe<Scalars['Int']>;
  maxUpvoteCount?: Maybe<Scalars['Int']>;
  tagHash?: Maybe<Scalars['String']>;
};

export type ForumActivityFilterParameter = {
  id?: Maybe<IdOperators>;
  createdAt?: Maybe<DateOperators>;
  updatedAt?: Maybe<DateOperators>;
  deletedAt?: Maybe<DateOperators>;
  name?: Maybe<StringOperators>;
  remark?: Maybe<StringOperators>;
  description?: Maybe<StringOperators>;
  smallProgramQRCodeLink?: Maybe<StringOperators>;
  status?: Maybe<StringOperators>;
  startTime?: Maybe<DateOperators>;
  endTime?: Maybe<DateOperators>;
  forumTagId?: Maybe<IdOperators>;
  postCount?: Maybe<NumberOperators>;
  replyCount?: Maybe<NumberOperators>;
  maxUpvoteCount?: Maybe<NumberOperators>;
  tagHash?: Maybe<StringOperators>;
};

export type ForumActivityInput = {
  id?: Maybe<Scalars['ID']>;
  name: Scalars['String'];
  remark?: Maybe<Scalars['String']>;
  description?: Maybe<Scalars['String']>;
  startTime: Scalars['DateTime'];
  endTime: Scalars['DateTime'];
  forumTagId: Scalars['ID'];
};

export type ForumActivityList = PaginatedList & {
  __typename?: 'ForumActivityList';
  items: Array<ForumActivity>;
  totalItems: Scalars['Int'];
};

export type ForumActivityListOptions = {
  /** Skips the first n results, for use in pagination */
  skip?: Maybe<Scalars['Int']>;
  /** Takes n results, for use in pagination */
  take?: Maybe<Scalars['Int']>;
  /** Specifies which properties to sort the results by */
  sort?: Maybe<ForumActivitySortParameter>;
  /** Allows the results to be filtered */
  filter?: Maybe<ForumActivityFilterParameter>;
  /** Specifies whether multiple "filter" arguments should be combines with a logical AND or OR operation. Defaults to AND. */
  filterOperator?: Maybe<LogicalOperator>;
};

export type ForumActivitySortParameter = {
  id?: Maybe<SortOrder>;
  createdAt?: Maybe<SortOrder>;
  updatedAt?: Maybe<SortOrder>;
  deletedAt?: Maybe<SortOrder>;
  name?: Maybe<SortOrder>;
  remark?: Maybe<SortOrder>;
  description?: Maybe<SortOrder>;
  smallProgramQRCodeLink?: Maybe<SortOrder>;
  startTime?: Maybe<SortOrder>;
  endTime?: Maybe<SortOrder>;
  forumTagId?: Maybe<SortOrder>;
  postCount?: Maybe<SortOrder>;
  replyCount?: Maybe<SortOrder>;
  maxUpvoteCount?: Maybe<SortOrder>;
  tagHash?: Maybe<SortOrder>;
};

export enum ForumActivityStatus {
  NotStarted = 'notStarted',
  Normal = 'normal',
  HaveEnded = 'haveEnded',
  Failure = 'failure'
}

export type ForumCustomer = Node & {
  __typename?: 'ForumCustomer';
  id: Scalars['ID'];
  createdAt: Scalars['DateTime'];
  updatedAt: Scalars['DateTime'];
  deletedAt?: Maybe<Scalars['DateTime']>;
  name: Scalars['String'];
  email?: Maybe<Scalars['String']>;
  forumUid?: Maybe<Scalars['String']>;
  headPortrait?: Maybe<Scalars['String']>;
  phone?: Maybe<Scalars['String']>;
  createType?: Maybe<ForumCustomerCreateType>;
  customer?: Maybe<Customer>;
  customerId?: Maybe<Scalars['ID']>;
};

export enum ForumCustomerCreateType {
  Admin = 'admin',
  User = 'user'
}

export type ForumCustomerFilterParameter = {
  id?: Maybe<IdOperators>;
  createdAt?: Maybe<DateOperators>;
  updatedAt?: Maybe<DateOperators>;
  deletedAt?: Maybe<DateOperators>;
  name?: Maybe<StringOperators>;
  email?: Maybe<StringOperators>;
  forumUid?: Maybe<StringOperators>;
  headPortrait?: Maybe<StringOperators>;
  phone?: Maybe<StringOperators>;
  createType?: Maybe<StringOperators>;
  customerId?: Maybe<IdOperators>;
};

export type ForumCustomerInput = {
  id?: Maybe<Scalars['ID']>;
  name: Scalars['String'];
  headPortrait?: Maybe<Scalars['String']>;
  phone?: Maybe<Scalars['String']>;
};

export type ForumCustomerList = PaginatedList & {
  __typename?: 'ForumCustomerList';
  items: Array<ForumCustomer>;
  totalItems: Scalars['Int'];
};

export type ForumCustomerListOptions = {
  /** Skips the first n results, for use in pagination */
  skip?: Maybe<Scalars['Int']>;
  /** Takes n results, for use in pagination */
  take?: Maybe<Scalars['Int']>;
  /** Specifies which properties to sort the results by */
  sort?: Maybe<ForumCustomerSortParameter>;
  /** Allows the results to be filtered */
  filter?: Maybe<ForumCustomerFilterParameter>;
  /** Specifies whether multiple "filter" arguments should be combines with a logical AND or OR operation. Defaults to AND. */
  filterOperator?: Maybe<LogicalOperator>;
};

export type ForumCustomerSortParameter = {
  id?: Maybe<SortOrder>;
  createdAt?: Maybe<SortOrder>;
  updatedAt?: Maybe<SortOrder>;
  deletedAt?: Maybe<SortOrder>;
  name?: Maybe<SortOrder>;
  email?: Maybe<SortOrder>;
  forumUid?: Maybe<SortOrder>;
  headPortrait?: Maybe<SortOrder>;
  phone?: Maybe<SortOrder>;
  customerId?: Maybe<SortOrder>;
};

export type ForumNotification = {
  __typename?: 'ForumNotification';
  bodyLong?: Maybe<Scalars['String']>;
  bodyShort?: Maybe<Scalars['String']>;
  datetime?: Maybe<Scalars['String']>;
  datetimeISO?: Maybe<Scalars['String']>;
  from?: Maybe<Scalars['Int']>;
  image?: Maybe<Scalars['String']>;
  importance?: Maybe<Scalars['Int']>;
  mergeId?: Maybe<Scalars['String']>;
  nid?: Maybe<Scalars['String']>;
  path?: Maybe<Scalars['String']>;
  pid?: Maybe<Scalars['Int']>;
  read?: Maybe<Scalars['Boolean']>;
  readClass?: Maybe<Scalars['String']>;
  tid?: Maybe<Scalars['Int']>;
  topicTitle?: Maybe<Scalars['String']>;
  type?: Maybe<Scalars['String']>;
  user?: Maybe<ForumUser>;
  forumCustomer?: Maybe<ForumCustomer>;
  reviewId?: Maybe<Scalars['Int']>;
  forumPostId?: Maybe<Scalars['Int']>;
};

export type ForumNotificationList = {
  __typename?: 'ForumNotificationList';
  items?: Maybe<Array<Maybe<ForumNotification>>>;
  totalItems?: Maybe<Scalars['Int']>;
};

export type ForumNotificationListOptions = {
  skip?: Maybe<Scalars['Int']>;
  take?: Maybe<Scalars['Int']>;
};

export type ForumPost = Node & {
  __typename?: 'ForumPost';
  id: Scalars['ID'];
  createdAt?: Maybe<Scalars['DateTime']>;
  updatedAt?: Maybe<Scalars['DateTime']>;
  deletedAt?: Maybe<Scalars['DateTime']>;
  type?: Maybe<ForumPostType>;
  title: Scalars['String'];
  content?: Maybe<Scalars['String']>;
  images?: Maybe<Array<Maybe<Scalars['String']>>>;
  mainImage?: Maybe<Scalars['String']>;
  forumWishPost?: Maybe<ForumPost>;
  status?: Maybe<ForumPostStatus>;
  releaseTime?: Maybe<Scalars['DateTime']>;
  auditTime?: Maybe<Scalars['DateTime']>;
  refuseReason?: Maybe<Scalars['String']>;
  forumCustomer?: Maybe<ForumCustomer>;
  forumCustomerId?: Maybe<Scalars['ID']>;
  forumTags?: Maybe<Array<Maybe<ForumTag>>>;
  forumActivities?: Maybe<Array<Maybe<ForumActivity>>>;
  products?: Maybe<Array<Maybe<Product>>>;
  tid?: Maybe<Scalars['Int']>;
  isWish?: Maybe<Scalars['Boolean']>;
  downVotes?: Maybe<Scalars['Int']>;
  upVotes?: Maybe<Scalars['Int']>;
  votes?: Maybe<Scalars['Int']>;
  viewCount?: Maybe<Scalars['Int']>;
  mainPid?: Maybe<Scalars['Int']>;
  postCount?: Maybe<Scalars['Int']>;
  tags?: Maybe<Array<Maybe<Scalars['String']>>>;
  upVoted?: Maybe<Scalars['Boolean']>;
  nodeBBPostId?: Maybe<Scalars['Int']>;
  nodeBBTopicId?: Maybe<Scalars['Int']>;
  uid?: Maybe<Scalars['Int']>;
  shareCount?: Maybe<Scalars['Int']>;
  shareTitle?: Maybe<Scalars['String']>;
  shareImg?: Maybe<Scalars['String']>;
  visitorsCount?: Maybe<Scalars['Int']>;
  pageViews?: Maybe<Scalars['Int']>;
  pinned?: Maybe<Scalars['Boolean']>;
};

export type ForumPostAuditInput = {
  forumPostId: Scalars['ID'];
  status: ForumPostAuditStatus;
  refuseReason?: Maybe<Scalars['String']>;
};

export enum ForumPostAuditStatus {
  Pass = 'pass',
  Refuse = 'refuse'
}

export type ForumPostFilterParameter = {
  id?: Maybe<IdOperators>;
  createdAt?: Maybe<DateOperators>;
  updatedAt?: Maybe<DateOperators>;
  deletedAt?: Maybe<DateOperators>;
  type?: Maybe<StringOperators>;
  title?: Maybe<StringOperators>;
  content?: Maybe<StringOperators>;
  mainImage?: Maybe<StringOperators>;
  status?: Maybe<StringOperators>;
  releaseTime?: Maybe<DateOperators>;
  auditTime?: Maybe<DateOperators>;
  refuseReason?: Maybe<StringOperators>;
  forumCustomerId?: Maybe<IdOperators>;
  tid?: Maybe<NumberOperators>;
  isWish?: Maybe<BooleanOperators>;
  downVotes?: Maybe<NumberOperators>;
  upVotes?: Maybe<NumberOperators>;
  votes?: Maybe<NumberOperators>;
  viewCount?: Maybe<NumberOperators>;
  mainPid?: Maybe<NumberOperators>;
  postCount?: Maybe<NumberOperators>;
  upVoted?: Maybe<BooleanOperators>;
  nodeBBPostId?: Maybe<NumberOperators>;
  nodeBBTopicId?: Maybe<NumberOperators>;
  uid?: Maybe<NumberOperators>;
  shareCount?: Maybe<NumberOperators>;
  shareTitle?: Maybe<StringOperators>;
  shareImg?: Maybe<StringOperators>;
  visitorsCount?: Maybe<NumberOperators>;
  pageViews?: Maybe<NumberOperators>;
  pinned?: Maybe<BooleanOperators>;
};

export enum ForumPostHotDateType {
  Daily = 'daily',
  Weekly = 'weekly',
  Monthly = 'monthly',
  Alltime = 'alltime'
}

export type ForumPostInput = {
  id?: Maybe<Scalars['ID']>;
  title: Scalars['String'];
  content: Scalars['String'];
  images?: Maybe<Array<Maybe<Scalars['String']>>>;
  mainImage?: Maybe<Scalars['String']>;
  forumCustomerId?: Maybe<Scalars['ID']>;
  forumTagIds?: Maybe<Array<Maybe<Scalars['ID']>>>;
  productIds?: Maybe<Array<Maybe<Scalars['ID']>>>;
  wishPostId?: Maybe<Scalars['ID']>;
  type: ForumPostType;
  isPublish?: Maybe<Scalars['Boolean']>;
  shareTitle?: Maybe<Scalars['String']>;
  shareImg?: Maybe<Scalars['String']>;
};

export type ForumPostList = PaginatedList & {
  __typename?: 'ForumPostList';
  items: Array<ForumPost>;
  totalItems: Scalars['Int'];
};

export type ForumPostListOptions = {
  /** Skips the first n results, for use in pagination */
  skip?: Maybe<Scalars['Int']>;
  /** Takes n results, for use in pagination */
  take?: Maybe<Scalars['Int']>;
  /** Specifies which properties to sort the results by */
  sort?: Maybe<ForumPostSortParameter>;
  /** Allows the results to be filtered */
  filter?: Maybe<ForumPostFilterParameter>;
  /** Specifies whether multiple "filter" arguments should be combines with a logical AND or OR operation. Defaults to AND. */
  filterOperator?: Maybe<LogicalOperator>;
};

export type ForumPostSortParameter = {
  id?: Maybe<SortOrder>;
  createdAt?: Maybe<SortOrder>;
  updatedAt?: Maybe<SortOrder>;
  deletedAt?: Maybe<SortOrder>;
  title?: Maybe<SortOrder>;
  content?: Maybe<SortOrder>;
  mainImage?: Maybe<SortOrder>;
  releaseTime?: Maybe<SortOrder>;
  auditTime?: Maybe<SortOrder>;
  refuseReason?: Maybe<SortOrder>;
  forumCustomerId?: Maybe<SortOrder>;
  tid?: Maybe<SortOrder>;
  downVotes?: Maybe<SortOrder>;
  upVotes?: Maybe<SortOrder>;
  votes?: Maybe<SortOrder>;
  viewCount?: Maybe<SortOrder>;
  mainPid?: Maybe<SortOrder>;
  postCount?: Maybe<SortOrder>;
  nodeBBPostId?: Maybe<SortOrder>;
  nodeBBTopicId?: Maybe<SortOrder>;
  uid?: Maybe<SortOrder>;
  shareCount?: Maybe<SortOrder>;
  shareTitle?: Maybe<SortOrder>;
  shareImg?: Maybe<SortOrder>;
  visitorsCount?: Maybe<SortOrder>;
  pageViews?: Maybe<SortOrder>;
};

export enum ForumPostStatus {
  Draft = 'draft',
  Pending = 'pending',
  Published = 'published',
  Refused = 'refused'
}

export enum ForumPostType {
  Long = 'long',
  Short = 'short'
}

export type ForumReplies = {
  __typename?: 'ForumReplies';
  uid?: Maybe<Scalars['Int']>;
  pid?: Maybe<Scalars['Int']>;
  tid?: Maybe<Scalars['Int']>;
  votes?: Maybe<Scalars['Int']>;
  upVotes?: Maybe<Scalars['Int']>;
  downVotes?: Maybe<Scalars['Int']>;
  user?: Maybe<ForumUser>;
  upVoted?: Maybe<Scalars['Boolean']>;
  downVoted?: Maybe<Scalars['Boolean']>;
  repliesContent?: Maybe<Scalars['String']>;
  repliesTime?: Maybe<Scalars['DateTime']>;
  toPid?: Maybe<Scalars['Int']>;
  parent?: Maybe<ForumRepliesParentUser>;
  repliesCount?: Maybe<Scalars['Int']>;
};

export type ForumRepliesParentUser = {
  __typename?: 'ForumRepliesParentUser';
  displayname?: Maybe<Scalars['String']>;
  username?: Maybe<Scalars['String']>;
};

export type ForumReview = Node & {
  __typename?: 'ForumReview';
  id: Scalars['ID'];
  createdAt?: Maybe<Scalars['DateTime']>;
  updatedAt?: Maybe<Scalars['DateTime']>;
  deletedAt?: Maybe<Scalars['DateTime']>;
  content?: Maybe<Scalars['String']>;
  images?: Maybe<Array<Maybe<Scalars['String']>>>;
  status?: Maybe<ForumReviewStatus>;
  auditTime?: Maybe<Scalars['DateTime']>;
  refuseReason?: Maybe<Scalars['String']>;
  forumPost?: Maybe<ForumPost>;
  forumPostId?: Maybe<Scalars['String']>;
  postId?: Maybe<Scalars['String']>;
  toPostId?: Maybe<Scalars['String']>;
  topicId?: Maybe<Scalars['String']>;
  forumCustomer?: Maybe<ForumCustomer>;
  parentForumCustomer?: Maybe<ForumCustomer>;
  upVotes?: Maybe<Scalars['Int']>;
  reviewTime?: Maybe<Scalars['DateTime']>;
  level?: Maybe<Scalars['Int']>;
  reviewCount?: Maybe<Scalars['Int']>;
  upVoted?: Maybe<Scalars['Boolean']>;
  reviewUid?: Maybe<Scalars['String']>;
  parentPostId?: Maybe<Scalars['Int']>;
};

export type ForumReviewAuditInput = {
  forumReviewId: Scalars['ID'];
  status: ForumPostAuditStatus;
  refuseReason?: Maybe<Scalars['String']>;
};

export type ForumReviewFilterParameter = {
  id?: Maybe<IdOperators>;
  createdAt?: Maybe<DateOperators>;
  updatedAt?: Maybe<DateOperators>;
  deletedAt?: Maybe<DateOperators>;
  content?: Maybe<StringOperators>;
  status?: Maybe<StringOperators>;
  auditTime?: Maybe<DateOperators>;
  refuseReason?: Maybe<StringOperators>;
  forumPostId?: Maybe<StringOperators>;
  postId?: Maybe<StringOperators>;
  toPostId?: Maybe<StringOperators>;
  topicId?: Maybe<StringOperators>;
  upVotes?: Maybe<NumberOperators>;
  reviewTime?: Maybe<DateOperators>;
  level?: Maybe<NumberOperators>;
  reviewCount?: Maybe<NumberOperators>;
  upVoted?: Maybe<BooleanOperators>;
  reviewUid?: Maybe<StringOperators>;
  parentPostId?: Maybe<NumberOperators>;
};

export type ForumReviewList = PaginatedList & {
  __typename?: 'ForumReviewList';
  items: Array<ForumReview>;
  totalItems: Scalars['Int'];
};

export type ForumReviewListOptions = {
  /** Skips the first n results, for use in pagination */
  skip?: Maybe<Scalars['Int']>;
  /** Takes n results, for use in pagination */
  take?: Maybe<Scalars['Int']>;
  /** Specifies which properties to sort the results by */
  sort?: Maybe<ForumReviewSortParameter>;
  /** Allows the results to be filtered */
  filter?: Maybe<ForumReviewFilterParameter>;
  /** Specifies whether multiple "filter" arguments should be combines with a logical AND or OR operation. Defaults to AND. */
  filterOperator?: Maybe<LogicalOperator>;
};

export type ForumReviewSortParameter = {
  id?: Maybe<SortOrder>;
  createdAt?: Maybe<SortOrder>;
  updatedAt?: Maybe<SortOrder>;
  deletedAt?: Maybe<SortOrder>;
  content?: Maybe<SortOrder>;
  auditTime?: Maybe<SortOrder>;
  refuseReason?: Maybe<SortOrder>;
  forumPostId?: Maybe<SortOrder>;
  postId?: Maybe<SortOrder>;
  toPostId?: Maybe<SortOrder>;
  topicId?: Maybe<SortOrder>;
  upVotes?: Maybe<SortOrder>;
  reviewTime?: Maybe<SortOrder>;
  level?: Maybe<SortOrder>;
  reviewCount?: Maybe<SortOrder>;
  reviewUid?: Maybe<SortOrder>;
  parentPostId?: Maybe<SortOrder>;
};

export enum ForumReviewStatus {
  Pending = 'pending',
  Pass = 'pass',
  Refuse = 'refuse'
}

export type ForumTag = Node & {
  __typename?: 'ForumTag';
  id: Scalars['ID'];
  createdAt: Scalars['DateTime'];
  updatedAt: Scalars['DateTime'];
  deletedAt?: Maybe<Scalars['DateTime']>;
  name: Scalars['String'];
  remark?: Maybe<Scalars['String']>;
  description?: Maybe<Scalars['String']>;
  image?: Maybe<Scalars['String']>;
  smallProgramQRCodeLink?: Maybe<Scalars['String']>;
  status: ForumTagStatus;
  postCount?: Maybe<Scalars['Int']>;
  replyCount?: Maybe<Scalars['Int']>;
  maxUpvoteCount?: Maybe<Scalars['Int']>;
  forumTagActivity?: Maybe<ForumActivity>;
  sort?: Maybe<Scalars['Int']>;
  tagHash?: Maybe<Scalars['String']>;
  shareTitle?: Maybe<Scalars['String']>;
  shareImg?: Maybe<Scalars['String']>;
  visitorsCount?: Maybe<Scalars['Int']>;
  pageViews?: Maybe<Scalars['Int']>;
};

export type ForumTagFilterParameter = {
  id?: Maybe<IdOperators>;
  createdAt?: Maybe<DateOperators>;
  updatedAt?: Maybe<DateOperators>;
  deletedAt?: Maybe<DateOperators>;
  name?: Maybe<StringOperators>;
  remark?: Maybe<StringOperators>;
  description?: Maybe<StringOperators>;
  image?: Maybe<StringOperators>;
  smallProgramQRCodeLink?: Maybe<StringOperators>;
  status?: Maybe<StringOperators>;
  postCount?: Maybe<NumberOperators>;
  replyCount?: Maybe<NumberOperators>;
  maxUpvoteCount?: Maybe<NumberOperators>;
  sort?: Maybe<NumberOperators>;
  tagHash?: Maybe<StringOperators>;
  shareTitle?: Maybe<StringOperators>;
  shareImg?: Maybe<StringOperators>;
  visitorsCount?: Maybe<NumberOperators>;
  pageViews?: Maybe<NumberOperators>;
};

export type ForumTagInput = {
  id?: Maybe<Scalars['ID']>;
  name: Scalars['String'];
  remark?: Maybe<Scalars['String']>;
  description?: Maybe<Scalars['String']>;
  image?: Maybe<Scalars['String']>;
  status: ForumTagStatus;
  sort?: Maybe<Scalars['Int']>;
  shareTitle?: Maybe<Scalars['String']>;
  shareImg?: Maybe<Scalars['String']>;
};

export type ForumTagList = PaginatedList & {
  __typename?: 'ForumTagList';
  items: Array<ForumTag>;
  totalItems: Scalars['Int'];
};

export type ForumTagListOptions = {
  /** Skips the first n results, for use in pagination */
  skip?: Maybe<Scalars['Int']>;
  /** Takes n results, for use in pagination */
  take?: Maybe<Scalars['Int']>;
  /** Specifies which properties to sort the results by */
  sort?: Maybe<ForumTagSortParameter>;
  /** Allows the results to be filtered */
  filter?: Maybe<ForumTagFilterParameter>;
  /** Specifies whether multiple "filter" arguments should be combines with a logical AND or OR operation. Defaults to AND. */
  filterOperator?: Maybe<LogicalOperator>;
};

export type ForumTagSortParameter = {
  id?: Maybe<SortOrder>;
  createdAt?: Maybe<SortOrder>;
  updatedAt?: Maybe<SortOrder>;
  deletedAt?: Maybe<SortOrder>;
  name?: Maybe<SortOrder>;
  remark?: Maybe<SortOrder>;
  description?: Maybe<SortOrder>;
  image?: Maybe<SortOrder>;
  smallProgramQRCodeLink?: Maybe<SortOrder>;
  postCount?: Maybe<SortOrder>;
  replyCount?: Maybe<SortOrder>;
  maxUpvoteCount?: Maybe<SortOrder>;
  sort?: Maybe<SortOrder>;
  tagHash?: Maybe<SortOrder>;
  shareTitle?: Maybe<SortOrder>;
  shareImg?: Maybe<SortOrder>;
  visitorsCount?: Maybe<SortOrder>;
  pageViews?: Maybe<SortOrder>;
};

export enum ForumTagStatus {
  Active = 'active',
  Inactive = 'inactive'
}

export type ForumTopicNodeBb = {
  __typename?: 'ForumTopicNodeBB';
  tid?: Maybe<Scalars['Int']>;
  downVotes?: Maybe<Scalars['Int']>;
  upVotes?: Maybe<Scalars['Int']>;
  votes?: Maybe<Scalars['Int']>;
  viewCount?: Maybe<Scalars['Int']>;
  mainPid?: Maybe<Scalars['Int']>;
  postCount?: Maybe<Scalars['Int']>;
  tags?: Maybe<Array<Maybe<Scalars['String']>>>;
  title?: Maybe<Scalars['String']>;
  mainImage?: Maybe<Scalars['String']>;
  user?: Maybe<ForumUser>;
  content?: Maybe<Scalars['String']>;
  images?: Maybe<Array<Maybe<Scalars['String']>>>;
  type?: Maybe<ForumPostType>;
  isWish?: Maybe<Scalars['Boolean']>;
};

export type ForumUser = {
  __typename?: 'ForumUser';
  uid?: Maybe<Scalars['Int']>;
  username?: Maybe<Scalars['String']>;
  picture?: Maybe<Scalars['String']>;
};

export type ForumVote = {
  __typename?: 'ForumVote';
  operation?: Maybe<OperationResponse>;
  upVoted?: Maybe<Scalars['Boolean']>;
};

export type FreeGift = Node & {
  __typename?: 'FreeGift';
  id: Scalars['ID'];
  name: Scalars['String'];
  status: FreeGiftStatus;
  product: Product;
};

export type FreeGiftFilterParameter = {
  id?: Maybe<IdOperators>;
  name?: Maybe<StringOperators>;
  status?: Maybe<StringOperators>;
};

export type FreeGiftInput = {
  id?: Maybe<Scalars['ID']>;
  name: Scalars['String'];
  productId: Scalars['ID'];
};

export type FreeGiftList = PaginatedList & {
  __typename?: 'FreeGiftList';
  items: Array<FreeGift>;
  totalItems: Scalars['Int'];
};

export type FreeGiftListOptions = {
  /** Skips the first n results, for use in pagination */
  skip?: Maybe<Scalars['Int']>;
  /** Takes n results, for use in pagination */
  take?: Maybe<Scalars['Int']>;
  /** Specifies which properties to sort the results by */
  sort?: Maybe<FreeGiftSortParameter>;
  /** Allows the results to be filtered */
  filter?: Maybe<FreeGiftFilterParameter>;
  /** Specifies whether multiple "filter" arguments should be combines with a logical AND or OR operation. Defaults to AND. */
  filterOperator?: Maybe<LogicalOperator>;
};

export type FreeGiftSortParameter = {
  id?: Maybe<SortOrder>;
  name?: Maybe<SortOrder>;
};

export enum FreeGiftStatus {
  Normal = 'normal',
  Failure = 'failure'
}

export type FreeGiftValue = {
  __typename?: 'FreeGiftValue';
  freeGiftId: Scalars['ID'];
  skuId?: Maybe<Scalars['ID']>;
  freeGiftName: Scalars['String'];
  freeGiftPrice: Scalars['Int'];
  freeGiftProductId: Scalars['ID'];
  maximumOffer: Scalars['Int'];
  priority?: Maybe<Scalars['Int']>;
};

export type FreeGiftValueInput = {
  freeGiftId: Scalars['ID'];
  skuId?: Maybe<Scalars['ID']>;
  freeGiftName: Scalars['String'];
  freeGiftPrice: Scalars['Int'];
  freeGiftProductId: Scalars['ID'];
  maximumOffer: Scalars['Int'];
  priority?: Maybe<Scalars['Int']>;
};

export enum FrequencyUnit {
  Week = 'week',
  Month = 'month',
  Day = 'day',
  Minute = 'minute'
}

export type Fulfillment = Node & {
  __typename?: 'Fulfillment';
  id: Scalars['ID'];
  createdAt: Scalars['DateTime'];
  updatedAt: Scalars['DateTime'];
  lines: Array<FulfillmentLine>;
  /** @deprecated Use the `lines` field instead */
  summary: Array<FulfillmentLine>;
  state: Scalars['String'];
  method: Scalars['String'];
  trackingCode?: Maybe<Scalars['String']>;
  customFields?: Maybe<Scalars['JSON']>;
};

export type FulfillmentLine = {
  __typename?: 'FulfillmentLine';
  orderLine: OrderLine;
  orderLineId: Scalars['ID'];
  quantity: Scalars['Int'];
  fulfillment: Fulfillment;
  fulfillmentId: Scalars['ID'];
};

export type FullDiscountPresent = Node & {
  __typename?: 'FullDiscountPresent';
  id: Scalars['ID'];
  createdAt: Scalars['DateTime'];
  updatedAt: Scalars['DateTime'];
  displayName: Scalars['String'];
  name: Scalars['String'];
  type?: Maybe<FullDiscountPresentType>;
  remarks?: Maybe<Scalars['String']>;
  status?: Maybe<ActivityStatus>;
  startTime?: Maybe<Scalars['DateTime']>;
  endTime?: Maybe<Scalars['DateTime']>;
  introduce?: Maybe<Scalars['String']>;
  ruleType?: Maybe<RuleType>;
  ruleValues: Array<RuleValue>;
  applicableProduct?: Maybe<ApplicableProduct>;
  stackingDiscountSwitch?: Maybe<Scalars['Boolean']>;
  stackingPromotionTypes?: Maybe<Array<Maybe<PromotionType>>>;
  whetherRestrictUsers?: Maybe<Scalars['Boolean']>;
  groupType?: Maybe<GroupType>;
  memberPlanIds?: Maybe<Array<Maybe<Scalars['ID']>>>;
  activityContent?: Maybe<ActivityContent>;
  activitySynopsis?: Maybe<ActivitySynopsis>;
  activitySuperposition?: Maybe<Scalars['String']>;
  promotion?: Maybe<Promotion>;
  activityGifts?: Maybe<Array<Maybe<Product>>>;
  statisticsData?: Maybe<StatisticsData>;
};

export type FullDiscountPresentFilterParameter = {
  id?: Maybe<IdOperators>;
  createdAt?: Maybe<DateOperators>;
  updatedAt?: Maybe<DateOperators>;
  displayName?: Maybe<StringOperators>;
  name?: Maybe<StringOperators>;
  type?: Maybe<StringOperators>;
  remarks?: Maybe<StringOperators>;
  status?: Maybe<StringOperators>;
  startTime?: Maybe<DateOperators>;
  endTime?: Maybe<DateOperators>;
  introduce?: Maybe<StringOperators>;
  ruleType?: Maybe<StringOperators>;
  stackingDiscountSwitch?: Maybe<BooleanOperators>;
  whetherRestrictUsers?: Maybe<BooleanOperators>;
  groupType?: Maybe<StringOperators>;
  activitySuperposition?: Maybe<StringOperators>;
};

export type FullDiscountPresentInput = {
  id?: Maybe<Scalars['ID']>;
  name: Scalars['String'];
  displayName: Scalars['String'];
  type: FullDiscountPresentType;
  remarks?: Maybe<Scalars['String']>;
  startTime: Scalars['DateTime'];
  endTime: Scalars['DateTime'];
  introduce?: Maybe<Scalars['String']>;
  ruleType: RuleType;
  ruleValues: Array<RuleValueInput>;
  applicableProduct: ApplicableProductInput;
  stackingDiscountSwitch: Scalars['Boolean'];
  stackingPromotionTypes?: Maybe<Array<Maybe<PromotionType>>>;
  whetherRestrictUsers: Scalars['Boolean'];
  groupType?: Maybe<GroupType>;
  memberPlanIds?: Maybe<Array<Maybe<Scalars['ID']>>>;
};

export type FullDiscountPresentList = PaginatedList & {
  __typename?: 'FullDiscountPresentList';
  items: Array<FullDiscountPresent>;
  totalItems: Scalars['Int'];
};

export type FullDiscountPresentListOptions = {
  /** Skips the first n results, for use in pagination */
  skip?: Maybe<Scalars['Int']>;
  /** Takes n results, for use in pagination */
  take?: Maybe<Scalars['Int']>;
  /** Specifies which properties to sort the results by */
  sort?: Maybe<FullDiscountPresentSortParameter>;
  /** Allows the results to be filtered */
  filter?: Maybe<FullDiscountPresentFilterParameter>;
  /** Specifies whether multiple "filter" arguments should be combines with a logical AND or OR operation. Defaults to AND. */
  filterOperator?: Maybe<LogicalOperator>;
};

export type FullDiscountPresentSortParameter = {
  id?: Maybe<SortOrder>;
  createdAt?: Maybe<SortOrder>;
  updatedAt?: Maybe<SortOrder>;
  displayName?: Maybe<SortOrder>;
  name?: Maybe<SortOrder>;
  remarks?: Maybe<SortOrder>;
  startTime?: Maybe<SortOrder>;
  endTime?: Maybe<SortOrder>;
  introduce?: Maybe<SortOrder>;
  activitySuperposition?: Maybe<SortOrder>;
};

export enum FullDiscountPresentType {
  AmountFullReduction = 'amountFullReduction',
  QuantityFullReduction = 'quantityFullReduction',
  AmountFullPresent = 'amountFullPresent'
}

export type GainPhone = {
  jsCode: Scalars['String'];
  openIdKey: Scalars['String'];
};

export type Gift = {
  __typename?: 'Gift';
  promInstanceId?: Maybe<Scalars['ID']>;
  giftType?: Maybe<GiftType>;
  items?: Maybe<Array<Maybe<GiftItem>>>;
};

export type GiftCard = Node & {
  __typename?: 'GiftCard';
  id: Scalars['ID'];
  createdAt?: Maybe<Scalars['DateTime']>;
  updatedAt?: Maybe<Scalars['DateTime']>;
  name?: Maybe<Scalars['String']>;
  remarks?: Maybe<Scalars['String']>;
  backgroundImage?: Maybe<Scalars['String']>;
  price?: Maybe<Scalars['Int']>;
  introduce?: Maybe<Scalars['String']>;
  smallProgramQRCodeLink?: Maybe<Scalars['String']>;
  rightsCoupon?: Maybe<RightsCoupon>;
  membershipPlan?: Maybe<MembershipPlan>;
  giftCardCoupon?: Maybe<Array<Maybe<GiftCardCoupon>>>;
  state?: Maybe<GiftCardState>;
};

export type GiftCardCoupon = {
  __typename?: 'GiftCardCoupon';
  coupon?: Maybe<Coupon>;
  quantity?: Maybe<Scalars['Int']>;
};

export type GiftCardFilterParameter = {
  id?: Maybe<IdOperators>;
  createdAt?: Maybe<DateOperators>;
  updatedAt?: Maybe<DateOperators>;
  name?: Maybe<StringOperators>;
  remarks?: Maybe<StringOperators>;
  backgroundImage?: Maybe<StringOperators>;
  price?: Maybe<NumberOperators>;
  introduce?: Maybe<StringOperators>;
  smallProgramQRCodeLink?: Maybe<StringOperators>;
  state?: Maybe<StringOperators>;
};

export type GiftCardInput = {
  id?: Maybe<Scalars['ID']>;
  name: Scalars['String'];
  remarks?: Maybe<Scalars['String']>;
  backgroundImage: Scalars['String'];
  price: Scalars['Int'];
  introduce: Scalars['String'];
  rightsCoupon?: Maybe<RightsCouponInput>;
  membershipPlanId?: Maybe<Scalars['ID']>;
};

export type GiftCardList = PaginatedList & {
  __typename?: 'GiftCardList';
  items: Array<GiftCard>;
  totalItems: Scalars['Int'];
};

export type GiftCardListOptions = {
  /** Skips the first n results, for use in pagination */
  skip?: Maybe<Scalars['Int']>;
  /** Takes n results, for use in pagination */
  take?: Maybe<Scalars['Int']>;
  /** Specifies which properties to sort the results by */
  sort?: Maybe<GiftCardSortParameter>;
  /** Allows the results to be filtered */
  filter?: Maybe<GiftCardFilterParameter>;
  /** Specifies whether multiple "filter" arguments should be combines with a logical AND or OR operation. Defaults to AND. */
  filterOperator?: Maybe<LogicalOperator>;
};

export type GiftCardOrder = Node & {
  __typename?: 'GiftCardOrder';
  id: Scalars['ID'];
  createdAt?: Maybe<Scalars['DateTime']>;
  updatedAt?: Maybe<Scalars['DateTime']>;
  code?: Maybe<Scalars['String']>;
  giftCardDistributor?: Maybe<Distributor>;
  giftCardUserCoupons?: Maybe<Array<Maybe<UserCoupon>>>;
  state?: Maybe<MembershipOrderState>;
  paymentMethod?: Maybe<Scalars['String']>;
  amount?: Maybe<Scalars['Int']>;
  metadata?: Maybe<Scalars['JSON']>;
  customer?: Maybe<Customer>;
  giftCard?: Maybe<GiftCard>;
  payTime?: Maybe<Scalars['DateTime']>;
  membershipOrder?: Maybe<MembershipOrder>;
  giftCardReturns?: Maybe<Array<Maybe<GiftCardReturn>>>;
};

export type GiftCardOrderFilterParameter = {
  id?: Maybe<IdOperators>;
  createdAt?: Maybe<DateOperators>;
  updatedAt?: Maybe<DateOperators>;
  code?: Maybe<StringOperators>;
  state?: Maybe<StringOperators>;
  paymentMethod?: Maybe<StringOperators>;
  amount?: Maybe<NumberOperators>;
  payTime?: Maybe<DateOperators>;
};

export type GiftCardOrderList = PaginatedList & {
  __typename?: 'GiftCardOrderList';
  items: Array<GiftCardOrder>;
  totalItems: Scalars['Int'];
};

export type GiftCardOrderListOptions = {
  /** Skips the first n results, for use in pagination */
  skip?: Maybe<Scalars['Int']>;
  /** Takes n results, for use in pagination */
  take?: Maybe<Scalars['Int']>;
  /** Specifies which properties to sort the results by */
  sort?: Maybe<GiftCardOrderSortParameter>;
  /** Allows the results to be filtered */
  filter?: Maybe<GiftCardOrderFilterParameter>;
  /** Specifies whether multiple "filter" arguments should be combines with a logical AND or OR operation. Defaults to AND. */
  filterOperator?: Maybe<LogicalOperator>;
};

export type GiftCardOrderSortParameter = {
  id?: Maybe<SortOrder>;
  createdAt?: Maybe<SortOrder>;
  updatedAt?: Maybe<SortOrder>;
  code?: Maybe<SortOrder>;
  paymentMethod?: Maybe<SortOrder>;
  amount?: Maybe<SortOrder>;
  payTime?: Maybe<SortOrder>;
};

export type GiftCardPaymentInput = {
  method: Scalars['String'];
  metadata: Scalars['JSON'];
};

export type GiftCardReturn = Node & {
  __typename?: 'GiftCardReturn';
  id: Scalars['ID'];
  createdAt?: Maybe<Scalars['DateTime']>;
  updatedAt?: Maybe<Scalars['DateTime']>;
  price?: Maybe<Scalars['Int']>;
  giftCardOrder?: Maybe<GiftCardOrder>;
  userCouponIds?: Maybe<Array<Maybe<Scalars['ID']>>>;
  userMemberId?: Maybe<Scalars['ID']>;
};

export type GiftCardReturnFilterParameter = {
  id?: Maybe<IdOperators>;
  createdAt?: Maybe<DateOperators>;
  updatedAt?: Maybe<DateOperators>;
  price?: Maybe<NumberOperators>;
  userMemberId?: Maybe<IdOperators>;
};

export type GiftCardReturnList = PaginatedList & {
  __typename?: 'GiftCardReturnList';
  items: Array<GiftCardReturn>;
  totalItems: Scalars['Int'];
};

export type GiftCardReturnListOptions = {
  /** Skips the first n results, for use in pagination */
  skip?: Maybe<Scalars['Int']>;
  /** Takes n results, for use in pagination */
  take?: Maybe<Scalars['Int']>;
  /** Specifies which properties to sort the results by */
  sort?: Maybe<GiftCardReturnSortParameter>;
  /** Allows the results to be filtered */
  filter?: Maybe<GiftCardReturnFilterParameter>;
  /** Specifies whether multiple "filter" arguments should be combines with a logical AND or OR operation. Defaults to AND. */
  filterOperator?: Maybe<LogicalOperator>;
};

export type GiftCardReturnSortParameter = {
  id?: Maybe<SortOrder>;
  createdAt?: Maybe<SortOrder>;
  updatedAt?: Maybe<SortOrder>;
  price?: Maybe<SortOrder>;
  userMemberId?: Maybe<SortOrder>;
};

export type GiftCardSortParameter = {
  id?: Maybe<SortOrder>;
  createdAt?: Maybe<SortOrder>;
  updatedAt?: Maybe<SortOrder>;
  name?: Maybe<SortOrder>;
  remarks?: Maybe<SortOrder>;
  backgroundImage?: Maybe<SortOrder>;
  price?: Maybe<SortOrder>;
  introduce?: Maybe<SortOrder>;
  smallProgramQRCodeLink?: Maybe<SortOrder>;
};

export enum GiftCardState {
  Shelf = 'shelf',
  TakeOffTheShelf = 'takeOffTheShelf'
}

export type GiftItem = {
  __typename?: 'GiftItem';
  giftId?: Maybe<Scalars['ID']>;
  productId?: Maybe<Scalars['ID']>;
  count?: Maybe<Scalars['Int']>;
  skuId?: Maybe<Scalars['ID']>;
  name?: Maybe<Scalars['String']>;
  giftPrice?: Maybe<Scalars['Int']>;
  price?: Maybe<Scalars['Int']>;
  selected?: Maybe<Scalars['Boolean']>;
  autoSelected?: Maybe<Scalars['Boolean']>;
  priority?: Maybe<Scalars['Int']>;
  isAvailable?: Maybe<Scalars['Boolean']>;
  minimumStr?: Maybe<Scalars['String']>;
  ladderPriority?: Maybe<Scalars['Int']>;
  ladderLevel?: Maybe<Scalars['Int']>;
};

export enum GiftType {
  Free = 'free',
  MarkUp = 'mark_up'
}

export enum GlobalFlag {
  True = 'TRUE',
  False = 'FALSE',
  Inherit = 'INHERIT'
}

export type GoodsForExchange = {
  __typename?: 'GoodsForExchange';
  productVariantId: Scalars['ID'];
  price: Scalars['Int'];
};

export type GoodsForExchangeInput = {
  productId: Scalars['ID'];
  price: Scalars['Int'];
  sort?: Maybe<Scalars['Int']>;
  id?: Maybe<Scalars['ID']>;
};

export enum GrantTiming {
  ConfirmReceipt = 'confirmReceipt',
  CompletePayment = 'completePayment'
}

export enum GrantType {
  Automatic = 'automatic',
  Manual = 'manual'
}

export type GroupCustomer = Node & {
  __typename?: 'GroupCustomer';
  id: Scalars['ID'];
  createdAt?: Maybe<Scalars['DateTime']>;
  updatedAt?: Maybe<Scalars['DateTime']>;
  name?: Maybe<Scalars['String']>;
  isBirthday?: Maybe<Scalars['Boolean']>;
  birthdayMonth?: Maybe<Array<Maybe<Scalars['Int']>>>;
  isCardTime?: Maybe<Scalars['Boolean']>;
  cardStartTime?: Maybe<Scalars['DateTime']>;
  cardEndTime?: Maybe<Scalars['DateTime']>;
  customerCount?: Maybe<Scalars['Int']>;
  operationPlans?: Maybe<Array<Maybe<OperationPlan>>>;
  isTradeAmount?: Maybe<Scalars['Boolean']>;
  tradeAmountMin?: Maybe<Scalars['Int']>;
  tradeAmountMax?: Maybe<Scalars['Int']>;
  isMember?: Maybe<Scalars['Boolean']>;
};

export type GroupCustomerFilterParameter = {
  id?: Maybe<IdOperators>;
  createdAt?: Maybe<DateOperators>;
  updatedAt?: Maybe<DateOperators>;
  name?: Maybe<StringOperators>;
  isBirthday?: Maybe<BooleanOperators>;
  isCardTime?: Maybe<BooleanOperators>;
  cardStartTime?: Maybe<DateOperators>;
  cardEndTime?: Maybe<DateOperators>;
  customerCount?: Maybe<NumberOperators>;
  isTradeAmount?: Maybe<BooleanOperators>;
  tradeAmountMin?: Maybe<NumberOperators>;
  tradeAmountMax?: Maybe<NumberOperators>;
  isMember?: Maybe<BooleanOperators>;
};

export type GroupCustomerInput = {
  id?: Maybe<Scalars['ID']>;
  name: Scalars['String'];
  isBirthday: Scalars['Boolean'];
  birthdayMonth?: Maybe<Array<Maybe<Scalars['Int']>>>;
  isCardTime: Scalars['Boolean'];
  cardStartTime?: Maybe<Scalars['DateTime']>;
  cardEndTime?: Maybe<Scalars['DateTime']>;
  isTradeAmount?: Maybe<Scalars['Boolean']>;
  tradeAmountMin?: Maybe<Scalars['Int']>;
  tradeAmountMax?: Maybe<Scalars['Int']>;
  isMember?: Maybe<Scalars['Boolean']>;
};

export type GroupCustomerList = PaginatedList & {
  __typename?: 'GroupCustomerList';
  items: Array<GroupCustomer>;
  totalItems: Scalars['Int'];
};

export type GroupCustomerListOptions = {
  /** Skips the first n results, for use in pagination */
  skip?: Maybe<Scalars['Int']>;
  /** Takes n results, for use in pagination */
  take?: Maybe<Scalars['Int']>;
  /** Specifies which properties to sort the results by */
  sort?: Maybe<GroupCustomerSortParameter>;
  /** Allows the results to be filtered */
  filter?: Maybe<GroupCustomerFilterParameter>;
  /** Specifies whether multiple "filter" arguments should be combines with a logical AND or OR operation. Defaults to AND. */
  filterOperator?: Maybe<LogicalOperator>;
};

export type GroupCustomerSortParameter = {
  id?: Maybe<SortOrder>;
  createdAt?: Maybe<SortOrder>;
  updatedAt?: Maybe<SortOrder>;
  name?: Maybe<SortOrder>;
  cardStartTime?: Maybe<SortOrder>;
  cardEndTime?: Maybe<SortOrder>;
  customerCount?: Maybe<SortOrder>;
  tradeAmountMin?: Maybe<SortOrder>;
  tradeAmountMax?: Maybe<SortOrder>;
};

export enum GroupType {
  All = 'all',
  MemberPlan = 'memberPlan'
}

/** Returned when attempting to set the Customer on a guest checkout when the configured GuestCheckoutStrategy does not allow it. */
export type GuestCheckoutError = ErrorResult & {
  __typename?: 'GuestCheckoutError';
  errorCode: ErrorCode;
  message: Scalars['String'];
  errorDetail: Scalars['String'];
};

export type H5LineInfo = {
  __typename?: 'H5LineInfo';
  h5Link?: Maybe<Scalars['String']>;
  h5QRCode?: Maybe<Scalars['String']>;
};

export enum H5Type {
  H5Shop = 'h5Shop',
  DistributorInform = 'distributorInform'
}

export type HandlerInfo = {
  __typename?: 'HandlerInfo';
  handler?: Maybe<Scalars['String']>;
  title?: Maybe<Scalars['String']>;
};

export type HistoryEntry = Node & {
  __typename?: 'HistoryEntry';
  id: Scalars['ID'];
  createdAt: Scalars['DateTime'];
  updatedAt: Scalars['DateTime'];
  type: HistoryEntryType;
  data: Scalars['JSON'];
};

export type HistoryEntryFilterParameter = {
  id?: Maybe<IdOperators>;
  createdAt?: Maybe<DateOperators>;
  updatedAt?: Maybe<DateOperators>;
  type?: Maybe<StringOperators>;
};

export type HistoryEntryList = PaginatedList & {
  __typename?: 'HistoryEntryList';
  items: Array<HistoryEntry>;
  totalItems: Scalars['Int'];
};

export type HistoryEntryListOptions = {
  /** Skips the first n results, for use in pagination */
  skip?: Maybe<Scalars['Int']>;
  /** Takes n results, for use in pagination */
  take?: Maybe<Scalars['Int']>;
  /** Specifies which properties to sort the results by */
  sort?: Maybe<HistoryEntrySortParameter>;
  /** Allows the results to be filtered */
  filter?: Maybe<HistoryEntryFilterParameter>;
  /** Specifies whether multiple "filter" arguments should be combines with a logical AND or OR operation. Defaults to AND. */
  filterOperator?: Maybe<LogicalOperator>;
};

export type HistoryEntrySortParameter = {
  id?: Maybe<SortOrder>;
  createdAt?: Maybe<SortOrder>;
  updatedAt?: Maybe<SortOrder>;
};

export enum HistoryEntryType {
  CustomerRegistered = 'CUSTOMER_REGISTERED',
  CustomerVerified = 'CUSTOMER_VERIFIED',
  CustomerDetailUpdated = 'CUSTOMER_DETAIL_UPDATED',
  CustomerAddedToGroup = 'CUSTOMER_ADDED_TO_GROUP',
  CustomerRemovedFromGroup = 'CUSTOMER_REMOVED_FROM_GROUP',
  CustomerAddressCreated = 'CUSTOMER_ADDRESS_CREATED',
  CustomerAddressUpdated = 'CUSTOMER_ADDRESS_UPDATED',
  CustomerAddressDeleted = 'CUSTOMER_ADDRESS_DELETED',
  CustomerPasswordUpdated = 'CUSTOMER_PASSWORD_UPDATED',
  CustomerPasswordResetRequested = 'CUSTOMER_PASSWORD_RESET_REQUESTED',
  CustomerPasswordResetVerified = 'CUSTOMER_PASSWORD_RESET_VERIFIED',
  CustomerEmailUpdateRequested = 'CUSTOMER_EMAIL_UPDATE_REQUESTED',
  CustomerEmailUpdateVerified = 'CUSTOMER_EMAIL_UPDATE_VERIFIED',
  CustomerNote = 'CUSTOMER_NOTE',
  OrderStateTransition = 'ORDER_STATE_TRANSITION',
  OrderPaymentTransition = 'ORDER_PAYMENT_TRANSITION',
  OrderFulfillment = 'ORDER_FULFILLMENT',
  OrderCancellation = 'ORDER_CANCELLATION',
  OrderRefundTransition = 'ORDER_REFUND_TRANSITION',
  OrderFulfillmentTransition = 'ORDER_FULFILLMENT_TRANSITION',
  OrderNote = 'ORDER_NOTE',
  OrderCouponApplied = 'ORDER_COUPON_APPLIED',
  OrderCouponRemoved = 'ORDER_COUPON_REMOVED',
  OrderModified = 'ORDER_MODIFIED'
}

export type HotWord = Node & {
  __typename?: 'HotWord';
  id: Scalars['ID'];
  createdAt?: Maybe<Scalars['DateTime']>;
  updatedAt?: Maybe<Scalars['DateTime']>;
  type?: Maybe<HotWordType>;
  name?: Maybe<Scalars['String']>;
  imgUrl?: Maybe<Scalars['String']>;
  jumpType?: Maybe<JumpType>;
  jumpValue?: Maybe<Scalars['String']>;
  channels?: Maybe<Array<Maybe<Channel>>>;
};

export type HotWordFilterParameter = {
  id?: Maybe<IdOperators>;
  createdAt?: Maybe<DateOperators>;
  updatedAt?: Maybe<DateOperators>;
  type?: Maybe<StringOperators>;
  name?: Maybe<StringOperators>;
  imgUrl?: Maybe<StringOperators>;
  jumpType?: Maybe<StringOperators>;
  jumpValue?: Maybe<StringOperators>;
};

export type HotWordInput = {
  id?: Maybe<Scalars['ID']>;
  name: Scalars['String'];
  type: HotWordType;
  imgUrl: Scalars['String'];
  jumpType: JumpType;
  jumpValue: Scalars['String'];
};

export type HotWordList = PaginatedList & {
  __typename?: 'HotWordList';
  items: Array<HotWord>;
  totalItems: Scalars['Int'];
};

export type HotWordListOptions = {
  /** Skips the first n results, for use in pagination */
  skip?: Maybe<Scalars['Int']>;
  /** Takes n results, for use in pagination */
  take?: Maybe<Scalars['Int']>;
  /** Specifies which properties to sort the results by */
  sort?: Maybe<HotWordSortParameter>;
  /** Allows the results to be filtered */
  filter?: Maybe<HotWordFilterParameter>;
  /** Specifies whether multiple "filter" arguments should be combines with a logical AND or OR operation. Defaults to AND. */
  filterOperator?: Maybe<LogicalOperator>;
};

export type HotWordSortParameter = {
  id?: Maybe<SortOrder>;
  createdAt?: Maybe<SortOrder>;
  updatedAt?: Maybe<SortOrder>;
  name?: Maybe<SortOrder>;
  imgUrl?: Maybe<SortOrder>;
  jumpValue?: Maybe<SortOrder>;
};

export enum HotWordType {
  Ordinary = 'ordinary',
  Choiceness = 'choiceness'
}

/** Operators for filtering on a list of ID fields */
export type IdListOperators = {
  inList: Scalars['ID'];
};

/** Operators for filtering on an ID field */
export type IdOperators = {
  eq?: Maybe<Scalars['String']>;
  notEq?: Maybe<Scalars['String']>;
  in?: Maybe<Array<Scalars['String']>>;
  notIn?: Maybe<Array<Scalars['String']>>;
  isNull?: Maybe<Scalars['Boolean']>;
};

/**
 * Returned if the token used to change a Customer's email address is valid, but has
 * expired according to the `verificationTokenDuration` setting in the AuthOptions.
 */
export type IdentifierChangeTokenExpiredError = ErrorResult & {
  __typename?: 'IdentifierChangeTokenExpiredError';
  errorCode: ErrorCode;
  message: Scalars['String'];
};

/**
 * Returned if the token used to change a Customer's email address is either
 * invalid or does not match any expected tokens.
 */
export type IdentifierChangeTokenInvalidError = ErrorResult & {
  __typename?: 'IdentifierChangeTokenInvalidError';
  errorCode: ErrorCode;
  message: Scalars['String'];
};

/** Returned when attempting to add a Payment using a PaymentMethod for which the Order is not eligible. */
export type IneligiblePaymentMethodError = ErrorResult & {
  __typename?: 'IneligiblePaymentMethodError';
  errorCode: ErrorCode;
  message: Scalars['String'];
  eligibilityCheckerMessage?: Maybe<Scalars['String']>;
};

/** Returned when attempting to set a ShippingMethod for which the Order is not eligible */
export type IneligibleShippingMethodError = ErrorResult & {
  __typename?: 'IneligibleShippingMethodError';
  errorCode: ErrorCode;
  message: Scalars['String'];
};

/** Returned when attempting to add more items to the Order than are available */
export type InsufficientStockError = ErrorResult & {
  __typename?: 'InsufficientStockError';
  errorCode: ErrorCode;
  message: Scalars['String'];
  quantityAvailable: Scalars['Int'];
  order: Order;
};

export type IntCustomFieldConfig = CustomField & {
  __typename?: 'IntCustomFieldConfig';
  name: Scalars['String'];
  type: Scalars['String'];
  list: Scalars['Boolean'];
  label?: Maybe<Array<LocalizedString>>;
  description?: Maybe<Array<LocalizedString>>;
  readonly?: Maybe<Scalars['Boolean']>;
  internal?: Maybe<Scalars['Boolean']>;
  nullable?: Maybe<Scalars['Boolean']>;
  min?: Maybe<Scalars['Int']>;
  max?: Maybe<Scalars['Int']>;
  step?: Maybe<Scalars['Int']>;
  ui?: Maybe<Scalars['JSON']>;
};

export enum InvalidCondition {
  AnyOne = 'anyOne',
  All = 'all'
}

/** Returned if the user authentication credentials are not valid */
export type InvalidCredentialsError = ErrorResult & {
  __typename?: 'InvalidCredentialsError';
  errorCode: ErrorCode;
  message: Scalars['String'];
  authenticationError: Scalars['String'];
};


export type JsSdkConfig = {
  __typename?: 'JsSdkConfig';
  appId?: Maybe<Scalars['String']>;
  timestamp?: Maybe<Scalars['String']>;
  nonceStr?: Maybe<Scalars['String']>;
  signature?: Maybe<Scalars['String']>;
};

export type Jump = {
  __typename?: 'Jump';
  startAxisX?: Maybe<Scalars['Int']>;
  startAxisY?: Maybe<Scalars['Int']>;
  endPointAxisX?: Maybe<Scalars['Int']>;
  endPointAxisY?: Maybe<Scalars['Int']>;
  jumpType?: Maybe<JumpType>;
  jumpValue?: Maybe<Scalars['String']>;
  jumpName?: Maybe<Scalars['String']>;
};

export type JumpInput = {
  startAxisX: Scalars['Int'];
  startAxisY: Scalars['Int'];
  endPointAxisX: Scalars['Int'];
  endPointAxisY: Scalars['Int'];
  jumpType: JumpType;
  jumpValue: Scalars['String'];
  jumpName?: Maybe<Scalars['String']>;
};

export enum JumpType {
  Url = 'url',
  ProductDetails = 'productDetails',
  Blank = 'blank',
  ActivePage = 'activePage',
  Coupon = 'coupon',
  FullDiscount = 'fullDiscount',
  DiscountByQuantity = 'discountByQuantity',
  PurchasePremium = 'purchasePremium',
  MemberShipPlan = 'memberShipPlan',
  SmallProgram = 'smallProgram',
  Countdown = 'countdown',
  PackageDiscount = 'packageDiscount',
  CouponBundle = 'couponBundle',
  MemberCenter = 'memberCenter',
  MembershipPlanListPage = 'membershipPlanListPage',
  CheckinPage = 'checkinPage',
  BlindBoxActivity = 'blindBoxActivity',
  PointsMall = 'pointsMall',
  Forum = 'forum'
}

/**
 * @description
 * Languages in the form of a ISO 639-1 language code with optional
 * region or script modifier (e.g. de_AT). The selection available is based
 * on the [Unicode CLDR summary list](https://unicode-org.github.io/cldr-staging/charts/37/summary/root.html)
 * and includes the major spoken languages of the world and any widely-used variants.
 *
 * @docsCategory common
 */
export enum LanguageCode {
  /** Afrikaans */
  Af = 'af',
  /** Akan */
  Ak = 'ak',
  /** Albanian */
  Sq = 'sq',
  /** Amharic */
  Am = 'am',
  /** Arabic */
  Ar = 'ar',
  /** Armenian */
  Hy = 'hy',
  /** Assamese */
  As = 'as',
  /** Azerbaijani */
  Az = 'az',
  /** Bambara */
  Bm = 'bm',
  /** Bangla */
  Bn = 'bn',
  /** Basque */
  Eu = 'eu',
  /** Belarusian */
  Be = 'be',
  /** Bosnian */
  Bs = 'bs',
  /** Breton */
  Br = 'br',
  /** Bulgarian */
  Bg = 'bg',
  /** Burmese */
  My = 'my',
  /** Catalan */
  Ca = 'ca',
  /** Chechen */
  Ce = 'ce',
  /** Chinese */
  Zh = 'zh',
  /** Simplified Chinese */
  ZhHans = 'zh_Hans',
  /** Traditional Chinese */
  ZhHant = 'zh_Hant',
  /** Church Slavic */
  Cu = 'cu',
  /** Cornish */
  Kw = 'kw',
  /** Corsican */
  Co = 'co',
  /** Croatian */
  Hr = 'hr',
  /** Czech */
  Cs = 'cs',
  /** Danish */
  Da = 'da',
  /** Dutch */
  Nl = 'nl',
  /** Flemish */
  NlBe = 'nl_BE',
  /** Dzongkha */
  Dz = 'dz',
  /** English */
  En = 'en',
  /** Australian English */
  EnAu = 'en_AU',
  /** Canadian English */
  EnCa = 'en_CA',
  /** British English */
  EnGb = 'en_GB',
  /** American English */
  EnUs = 'en_US',
  /** Esperanto */
  Eo = 'eo',
  /** Estonian */
  Et = 'et',
  /** Ewe */
  Ee = 'ee',
  /** Faroese */
  Fo = 'fo',
  /** Finnish */
  Fi = 'fi',
  /** French */
  Fr = 'fr',
  /** Canadian French */
  FrCa = 'fr_CA',
  /** Swiss French */
  FrCh = 'fr_CH',
  /** Fulah */
  Ff = 'ff',
  /** Galician */
  Gl = 'gl',
  /** Ganda */
  Lg = 'lg',
  /** Georgian */
  Ka = 'ka',
  /** German */
  De = 'de',
  /** Austrian German */
  DeAt = 'de_AT',
  /** Swiss High German */
  DeCh = 'de_CH',
  /** Greek */
  El = 'el',
  /** Gujarati */
  Gu = 'gu',
  /** Haitian Creole */
  Ht = 'ht',
  /** Hausa */
  Ha = 'ha',
  /** Hebrew */
  He = 'he',
  /** Hindi */
  Hi = 'hi',
  /** Hungarian */
  Hu = 'hu',
  /** Icelandic */
  Is = 'is',
  /** Igbo */
  Ig = 'ig',
  /** Indonesian */
  Id = 'id',
  /** Interlingua */
  Ia = 'ia',
  /** Irish */
  Ga = 'ga',
  /** Italian */
  It = 'it',
  /** Japanese */
  Ja = 'ja',
  /** Javanese */
  Jv = 'jv',
  /** Kalaallisut */
  Kl = 'kl',
  /** Kannada */
  Kn = 'kn',
  /** Kashmiri */
  Ks = 'ks',
  /** Kazakh */
  Kk = 'kk',
  /** Khmer */
  Km = 'km',
  /** Kikuyu */
  Ki = 'ki',
  /** Kinyarwanda */
  Rw = 'rw',
  /** Korean */
  Ko = 'ko',
  /** Kurdish */
  Ku = 'ku',
  /** Kyrgyz */
  Ky = 'ky',
  /** Lao */
  Lo = 'lo',
  /** Latin */
  La = 'la',
  /** Latvian */
  Lv = 'lv',
  /** Lingala */
  Ln = 'ln',
  /** Lithuanian */
  Lt = 'lt',
  /** Luba-Katanga */
  Lu = 'lu',
  /** Luxembourgish */
  Lb = 'lb',
  /** Macedonian */
  Mk = 'mk',
  /** Malagasy */
  Mg = 'mg',
  /** Malay */
  Ms = 'ms',
  /** Malayalam */
  Ml = 'ml',
  /** Maltese */
  Mt = 'mt',
  /** Manx */
  Gv = 'gv',
  /** Maori */
  Mi = 'mi',
  /** Marathi */
  Mr = 'mr',
  /** Mongolian */
  Mn = 'mn',
  /** Nepali */
  Ne = 'ne',
  /** North Ndebele */
  Nd = 'nd',
  /** Northern Sami */
  Se = 'se',
  /** Norwegian Bokmål */
  Nb = 'nb',
  /** Norwegian Nynorsk */
  Nn = 'nn',
  /** Nyanja */
  Ny = 'ny',
  /** Odia */
  Or = 'or',
  /** Oromo */
  Om = 'om',
  /** Ossetic */
  Os = 'os',
  /** Pashto */
  Ps = 'ps',
  /** Persian */
  Fa = 'fa',
  /** Dari */
  FaAf = 'fa_AF',
  /** Polish */
  Pl = 'pl',
  /** Portuguese */
  Pt = 'pt',
  /** Brazilian Portuguese */
  PtBr = 'pt_BR',
  /** European Portuguese */
  PtPt = 'pt_PT',
  /** Punjabi */
  Pa = 'pa',
  /** Quechua */
  Qu = 'qu',
  /** Romanian */
  Ro = 'ro',
  /** Moldavian */
  RoMd = 'ro_MD',
  /** Romansh */
  Rm = 'rm',
  /** Rundi */
  Rn = 'rn',
  /** Russian */
  Ru = 'ru',
  /** Samoan */
  Sm = 'sm',
  /** Sango */
  Sg = 'sg',
  /** Sanskrit */
  Sa = 'sa',
  /** Scottish Gaelic */
  Gd = 'gd',
  /** Serbian */
  Sr = 'sr',
  /** Shona */
  Sn = 'sn',
  /** Sichuan Yi */
  Ii = 'ii',
  /** Sindhi */
  Sd = 'sd',
  /** Sinhala */
  Si = 'si',
  /** Slovak */
  Sk = 'sk',
  /** Slovenian */
  Sl = 'sl',
  /** Somali */
  So = 'so',
  /** Southern Sotho */
  St = 'st',
  /** Spanish */
  Es = 'es',
  /** European Spanish */
  EsEs = 'es_ES',
  /** Mexican Spanish */
  EsMx = 'es_MX',
  /** Sundanese */
  Su = 'su',
  /** Swahili */
  Sw = 'sw',
  /** Congo Swahili */
  SwCd = 'sw_CD',
  /** Swedish */
  Sv = 'sv',
  /** Tajik */
  Tg = 'tg',
  /** Tamil */
  Ta = 'ta',
  /** Tatar */
  Tt = 'tt',
  /** Telugu */
  Te = 'te',
  /** Thai */
  Th = 'th',
  /** Tibetan */
  Bo = 'bo',
  /** Tigrinya */
  Ti = 'ti',
  /** Tongan */
  To = 'to',
  /** Turkish */
  Tr = 'tr',
  /** Turkmen */
  Tk = 'tk',
  /** Ukrainian */
  Uk = 'uk',
  /** Urdu */
  Ur = 'ur',
  /** Uyghur */
  Ug = 'ug',
  /** Uzbek */
  Uz = 'uz',
  /** Vietnamese */
  Vi = 'vi',
  /** Volapük */
  Vo = 'vo',
  /** Welsh */
  Cy = 'cy',
  /** Western Frisian */
  Fy = 'fy',
  /** Wolof */
  Wo = 'wo',
  /** Xhosa */
  Xh = 'xh',
  /** Yiddish */
  Yi = 'yi',
  /** Yoruba */
  Yo = 'yo',
  /** Zulu */
  Zu = 'zu'
}

export enum LayoutType {
  OneColumn = 'oneColumn',
  TwoColumn = 'twoColumn',
  ThreeColumn = 'threeColumn'
}

export type LeftOrderLine = {
  __typename?: 'LeftOrderLine';
  skuId?: Maybe<Scalars['ID']>;
  lineId?: Maybe<Scalars['ID']>;
  count?: Maybe<Scalars['Int']>;
  price?: Maybe<Scalars['Int']>;
  discountAmount?: Maybe<Scalars['Int']>;
};

export enum LimitType {
  Unlimited = 'unlimited',
  Day = 'day',
  Week = 'week',
  Month = 'month',
  Year = 'year',
  Forever = 'forever',
  Order = 'order'
}

export type LocaleStringCustomFieldConfig = CustomField & {
  __typename?: 'LocaleStringCustomFieldConfig';
  name: Scalars['String'];
  type: Scalars['String'];
  list: Scalars['Boolean'];
  length?: Maybe<Scalars['Int']>;
  label?: Maybe<Array<LocalizedString>>;
  description?: Maybe<Array<LocalizedString>>;
  readonly?: Maybe<Scalars['Boolean']>;
  internal?: Maybe<Scalars['Boolean']>;
  nullable?: Maybe<Scalars['Boolean']>;
  pattern?: Maybe<Scalars['String']>;
  ui?: Maybe<Scalars['JSON']>;
};

export type LocaleTextCustomFieldConfig = CustomField & {
  __typename?: 'LocaleTextCustomFieldConfig';
  name: Scalars['String'];
  type: Scalars['String'];
  list: Scalars['Boolean'];
  label?: Maybe<Array<LocalizedString>>;
  description?: Maybe<Array<LocalizedString>>;
  readonly?: Maybe<Scalars['Boolean']>;
  internal?: Maybe<Scalars['Boolean']>;
  nullable?: Maybe<Scalars['Boolean']>;
  ui?: Maybe<Scalars['JSON']>;
};

export type LocalizedString = {
  __typename?: 'LocalizedString';
  languageCode: LanguageCode;
  value: Scalars['String'];
};

export type LocationData = {
  __typename?: 'LocationData';
  value?: Maybe<Scalars['String']>;
  text?: Maybe<Scalars['String']>;
  children?: Maybe<Array<Maybe<LocationData>>>;
};

export enum LogicalOperator {
  And = 'AND',
  Or = 'OR'
}

export type LoginReturn = {
  __typename?: 'LoginReturn';
  token?: Maybe<Scalars['String']>;
  openIdKey?: Maybe<Scalars['String']>;
};

export type LogisticInfo = {
  __typename?: 'LogisticInfo';
  logisticCode?: Maybe<Scalars['String']>;
  shipperCode?: Maybe<Scalars['String']>;
  traces?: Maybe<Array<Maybe<Traces>>>;
  state?: Maybe<LogisticState>;
  success?: Maybe<Scalars['String']>;
  courier?: Maybe<Scalars['String']>;
  courierPhone?: Maybe<Scalars['String']>;
  updateTime?: Maybe<Scalars['DateTime']>;
  takeTime?: Maybe<Scalars['String']>;
  name?: Maybe<Scalars['String']>;
  site?: Maybe<Scalars['String']>;
  phone?: Maybe<Scalars['String']>;
  logo?: Maybe<Scalars['String']>;
  reason?: Maybe<Scalars['String']>;
};

export enum LogisticState {
  CodeError = 'codeError',
  NoInformation = 'noInformation',
  PickUp = 'pickUp',
  InTransit = 'inTransit',
  SignFor = 'signFor',
  ProblemShipment = 'problemShipment',
  DifficultItem = 'difficultItem',
  ReturnSignFor = 'returnSignFor'
}

export type Logistics = {
  __typename?: 'Logistics';
  id: Scalars['ID'];
  logisticCode?: Maybe<Scalars['String']>;
  company?: Maybe<Scalars['String']>;
  state?: Maybe<LogisticState>;
  logisticInfo?: Maybe<LogisticInfo>;
  order?: Maybe<Order>;
  fulfillment?: Maybe<Fulfillment>;
};

export enum LogisticsType {
  ReturnGoods = 'returnGoods',
  DeliverGoods = 'deliverGoods'
}

export type Member = Node & {
  __typename?: 'Member';
  id: Scalars['ID'];
  createdAt?: Maybe<Scalars['DateTime']>;
  updatedAt?: Maybe<Scalars['DateTime']>;
  code?: Maybe<Scalars['String']>;
  claimAt?: Maybe<Scalars['DateTime']>;
  activationAt?: Maybe<Scalars['DateTime']>;
  maturityAt?: Maybe<Scalars['DateTime']>;
  maturityType?: Maybe<ValidityPeriodType>;
  customer?: Maybe<Customer>;
  membershipPlan?: Maybe<MembershipPlan>;
  state?: Maybe<MemberState>;
  membershipOrders?: Maybe<Array<Maybe<MembershipOrder>>>;
};

export enum MemberCardDiscountLimitSourceType {
  CustomerMember = 'customerMember',
  MemberPlan = 'memberPlan'
}

export type MemberFilterParameter = {
  id?: Maybe<IdOperators>;
  createdAt?: Maybe<DateOperators>;
  updatedAt?: Maybe<DateOperators>;
  code?: Maybe<StringOperators>;
  claimAt?: Maybe<DateOperators>;
  activationAt?: Maybe<DateOperators>;
  maturityAt?: Maybe<DateOperators>;
  maturityType?: Maybe<StringOperators>;
  state?: Maybe<StringOperators>;
};

export type MemberList = PaginatedList & {
  __typename?: 'MemberList';
  items: Array<Member>;
  totalItems: Scalars['Int'];
};

export type MemberListOptions = {
  /** Skips the first n results, for use in pagination */
  skip?: Maybe<Scalars['Int']>;
  /** Takes n results, for use in pagination */
  take?: Maybe<Scalars['Int']>;
  /** Specifies which properties to sort the results by */
  sort?: Maybe<MemberSortParameter>;
  /** Allows the results to be filtered */
  filter?: Maybe<MemberFilterParameter>;
  /** Specifies whether multiple "filter" arguments should be combines with a logical AND or OR operation. Defaults to AND. */
  filterOperator?: Maybe<LogicalOperator>;
};

export type MemberPrice = Node & {
  __typename?: 'MemberPrice';
  id: Scalars['ID'];
  createdAt?: Maybe<Scalars['DateTime']>;
  updatedAt?: Maybe<Scalars['DateTime']>;
  name?: Maybe<Scalars['String']>;
  remark?: Maybe<Scalars['String']>;
  memberPriceProducts?: Maybe<Array<Maybe<MemberPriceProduct>>>;
  membershipPlanStr?: Maybe<Scalars['String']>;
};

export type MemberPriceActivityAmount = {
  __typename?: 'MemberPriceActivityAmount';
  minDiscount?: Maybe<Scalars['Int']>;
  maxDiscount?: Maybe<Scalars['Int']>;
  minMemberPriceAmount?: Maybe<Scalars['Int']>;
  maxMemberPriceAmount?: Maybe<Scalars['Int']>;
  memberPriceProductVariant?: Maybe<Array<Maybe<MemberPriceProductVariantInfo>>>;
};

export type MemberPriceFilterParameter = {
  id?: Maybe<IdOperators>;
  createdAt?: Maybe<DateOperators>;
  updatedAt?: Maybe<DateOperators>;
  name?: Maybe<StringOperators>;
  remark?: Maybe<StringOperators>;
  membershipPlanStr?: Maybe<StringOperators>;
};

export type MemberPriceInput = {
  id?: Maybe<Scalars['ID']>;
  name?: Maybe<Scalars['String']>;
  remark?: Maybe<Scalars['String']>;
};

export type MemberPriceList = PaginatedList & {
  __typename?: 'MemberPriceList';
  items: Array<MemberPrice>;
  totalItems: Scalars['Int'];
};

export type MemberPriceListOptions = {
  /** Skips the first n results, for use in pagination */
  skip?: Maybe<Scalars['Int']>;
  /** Takes n results, for use in pagination */
  take?: Maybe<Scalars['Int']>;
  /** Specifies which properties to sort the results by */
  sort?: Maybe<MemberPriceSortParameter>;
  /** Allows the results to be filtered */
  filter?: Maybe<MemberPriceFilterParameter>;
  /** Specifies whether multiple "filter" arguments should be combines with a logical AND or OR operation. Defaults to AND. */
  filterOperator?: Maybe<LogicalOperator>;
};

export type MemberPriceProduct = Node & {
  __typename?: 'MemberPriceProduct';
  id: Scalars['ID'];
  createdAt?: Maybe<Scalars['DateTime']>;
  updatedAt?: Maybe<Scalars['DateTime']>;
  memberPriceProductVariant?: Maybe<Array<Maybe<MemberPriceProductVariant>>>;
  product?: Maybe<Product>;
  memberPrice?: Maybe<MemberPrice>;
};

export type MemberPriceProductFilterParameter = {
  id?: Maybe<IdOperators>;
  createdAt?: Maybe<DateOperators>;
  updatedAt?: Maybe<DateOperators>;
};

export type MemberPriceProductInput = {
  productId: Scalars['ID'];
  memberPriceProductVariants: Array<MemberPriceProductVariantInput>;
};

export type MemberPriceProductList = PaginatedList & {
  __typename?: 'MemberPriceProductList';
  items: Array<MemberPriceProduct>;
  totalItems: Scalars['Int'];
};

export type MemberPriceProductListOptions = {
  /** Skips the first n results, for use in pagination */
  skip?: Maybe<Scalars['Int']>;
  /** Takes n results, for use in pagination */
  take?: Maybe<Scalars['Int']>;
  /** Specifies which properties to sort the results by */
  sort?: Maybe<MemberPriceProductSortParameter>;
  /** Allows the results to be filtered */
  filter?: Maybe<MemberPriceProductFilterParameter>;
  /** Specifies whether multiple "filter" arguments should be combines with a logical AND or OR operation. Defaults to AND. */
  filterOperator?: Maybe<LogicalOperator>;
};

export type MemberPriceProductSortParameter = {
  id?: Maybe<SortOrder>;
  createdAt?: Maybe<SortOrder>;
  updatedAt?: Maybe<SortOrder>;
};

export type MemberPriceProductVariant = Node & {
  __typename?: 'MemberPriceProductVariant';
  id: Scalars['ID'];
  createdAt?: Maybe<Scalars['DateTime']>;
  updatedAt?: Maybe<Scalars['DateTime']>;
  productVariant?: Maybe<ProductVariant>;
  memberPriceProduct?: Maybe<MemberPriceProduct>;
  memberPriceAmount?: Maybe<Scalars['Int']>;
  memberDiscount?: Maybe<Scalars['Int']>;
  discountType?: Maybe<DiscountType>;
};

export type MemberPriceProductVariantFilterParameter = {
  id?: Maybe<IdOperators>;
  createdAt?: Maybe<DateOperators>;
  updatedAt?: Maybe<DateOperators>;
  memberPriceAmount?: Maybe<NumberOperators>;
  memberDiscount?: Maybe<NumberOperators>;
  discountType?: Maybe<StringOperators>;
};

export type MemberPriceProductVariantInfo = {
  __typename?: 'MemberPriceProductVariantInfo';
  memberPriceAmount?: Maybe<Scalars['Int']>;
  productVariantId?: Maybe<Scalars['ID']>;
  memberDiscount?: Maybe<Scalars['Int']>;
  discountType?: Maybe<DiscountType>;
};

export type MemberPriceProductVariantInput = {
  productVariantId: Scalars['ID'];
  memberPriceAmount?: Maybe<Scalars['Int']>;
  memberDiscount?: Maybe<Scalars['Int']>;
  discountType?: Maybe<DiscountType>;
};

export type MemberPriceProductVariantList = PaginatedList & {
  __typename?: 'MemberPriceProductVariantList';
  items: Array<MemberPriceProductVariant>;
  totalItems: Scalars['Int'];
};

export type MemberPriceProductVariantListOptions = {
  /** Skips the first n results, for use in pagination */
  skip?: Maybe<Scalars['Int']>;
  /** Takes n results, for use in pagination */
  take?: Maybe<Scalars['Int']>;
  /** Specifies which properties to sort the results by */
  sort?: Maybe<MemberPriceProductVariantSortParameter>;
  /** Allows the results to be filtered */
  filter?: Maybe<MemberPriceProductVariantFilterParameter>;
  /** Specifies whether multiple "filter" arguments should be combines with a logical AND or OR operation. Defaults to AND. */
  filterOperator?: Maybe<LogicalOperator>;
};

export type MemberPriceProductVariantSortParameter = {
  id?: Maybe<SortOrder>;
  createdAt?: Maybe<SortOrder>;
  updatedAt?: Maybe<SortOrder>;
  memberPriceAmount?: Maybe<SortOrder>;
  memberDiscount?: Maybe<SortOrder>;
};

export type MemberPriceSortParameter = {
  id?: Maybe<SortOrder>;
  createdAt?: Maybe<SortOrder>;
  updatedAt?: Maybe<SortOrder>;
  name?: Maybe<SortOrder>;
  remark?: Maybe<SortOrder>;
  membershipPlanStr?: Maybe<SortOrder>;
};

export type MemberSortParameter = {
  id?: Maybe<SortOrder>;
  createdAt?: Maybe<SortOrder>;
  updatedAt?: Maybe<SortOrder>;
  code?: Maybe<SortOrder>;
  claimAt?: Maybe<SortOrder>;
  activationAt?: Maybe<SortOrder>;
  maturityAt?: Maybe<SortOrder>;
};

export enum MemberSource {
  OrderBuy = 'orderBuy',
  OwnMall = 'ownMall',
  YouZanYun = 'youZanYun',
  GiftCard = 'giftCard',
  ShopGrant = 'shopGrant'
}

export enum MemberState {
  Unactivated = 'unactivated',
  Normal = 'normal',
  Expired = 'expired',
  ReturnTheCard = 'returnTheCard'
}

export enum MemberStateInput {
  All = 'all',
  Unactivated = 'unactivated',
  Normal = 'normal',
  Expired = 'expired',
  ReturnTheCard = 'returnTheCard'
}

export type MembershipOrder = Node & {
  __typename?: 'MembershipOrder';
  id: Scalars['ID'];
  createdAt?: Maybe<Scalars['DateTime']>;
  updatedAt?: Maybe<Scalars['DateTime']>;
  memberOrderUserCoupons?: Maybe<Array<Maybe<UserCoupon>>>;
  code?: Maybe<Scalars['String']>;
  state?: Maybe<MembershipOrderState>;
  paymentMethod?: Maybe<Scalars['String']>;
  amount?: Maybe<Scalars['Int']>;
  metadata?: Maybe<Scalars['JSON']>;
  customer?: Maybe<Customer>;
  member?: Maybe<Member>;
  membershipPlan?: Maybe<MembershipPlan>;
  source?: Maybe<MemberSource>;
  sourceId?: Maybe<Scalars['ID']>;
  payTime?: Maybe<Scalars['DateTime']>;
};

export type MembershipOrderFilterParameter = {
  id?: Maybe<IdOperators>;
  createdAt?: Maybe<DateOperators>;
  updatedAt?: Maybe<DateOperators>;
  code?: Maybe<StringOperators>;
  state?: Maybe<StringOperators>;
  paymentMethod?: Maybe<StringOperators>;
  amount?: Maybe<NumberOperators>;
  source?: Maybe<StringOperators>;
  sourceId?: Maybe<IdOperators>;
  payTime?: Maybe<DateOperators>;
};

export type MembershipOrderList = PaginatedList & {
  __typename?: 'MembershipOrderList';
  items: Array<MembershipOrder>;
  totalItems: Scalars['Int'];
};

export type MembershipOrderListOptions = {
  /** Skips the first n results, for use in pagination */
  skip?: Maybe<Scalars['Int']>;
  /** Takes n results, for use in pagination */
  take?: Maybe<Scalars['Int']>;
  /** Specifies which properties to sort the results by */
  sort?: Maybe<MembershipOrderSortParameter>;
  /** Allows the results to be filtered */
  filter?: Maybe<MembershipOrderFilterParameter>;
  /** Specifies whether multiple "filter" arguments should be combines with a logical AND or OR operation. Defaults to AND. */
  filterOperator?: Maybe<LogicalOperator>;
};

export type MembershipOrderSortParameter = {
  id?: Maybe<SortOrder>;
  createdAt?: Maybe<SortOrder>;
  updatedAt?: Maybe<SortOrder>;
  code?: Maybe<SortOrder>;
  paymentMethod?: Maybe<SortOrder>;
  amount?: Maybe<SortOrder>;
  sourceId?: Maybe<SortOrder>;
  payTime?: Maybe<SortOrder>;
};

export enum MembershipOrderState {
  ToBePaid = 'toBePaid',
  Paid = 'paid',
  Cancelled = 'cancelled',
  Refunded = 'refunded',
  PartiallyRefunded = 'partiallyRefunded'
}

export type MembershipPlan = Node & {
  __typename?: 'MembershipPlan';
  id: Scalars['ID'];
  createdAt?: Maybe<Scalars['DateTime']>;
  updatedAt?: Maybe<Scalars['DateTime']>;
  name?: Maybe<Scalars['String']>;
  bannerImg?: Maybe<Array<Maybe<Scalars['String']>>>;
  backgroundImage?: Maybe<Scalars['String']>;
  validityPeriod?: Maybe<MembershipPlanValidityPeriod>;
  price?: Maybe<Scalars['Int']>;
  introduce?: Maybe<Scalars['String']>;
  protocolUsage?: Maybe<Scalars['String']>;
  isShow?: Maybe<Scalars['Boolean']>;
  customerServiceNumber?: Maybe<Scalars['String']>;
  customerGroup?: Maybe<CustomerGroup>;
  memberPriceId?: Maybe<Scalars['ID']>;
  promotion?: Maybe<Promotion>;
  state?: Maybe<MembershipPlanState>;
  rightsDiscount?: Maybe<RightsDiscount>;
  rightsPoints?: Maybe<RightsPoints>;
  rightsCoupon?: Maybe<RightsCoupon>;
  membershipPlanCoupon?: Maybe<Array<Maybe<MembershipPlanCoupon>>>;
  discountLimitSourceType?: Maybe<MemberCardDiscountLimitSourceType>;
  needActivate?: Maybe<Scalars['Boolean']>;
};

export type MembershipPlanCoupon = {
  __typename?: 'MembershipPlanCoupon';
  coupon?: Maybe<Coupon>;
  quantity?: Maybe<Scalars['Int']>;
};

export type MembershipPlanFilterParameter = {
  id?: Maybe<IdOperators>;
  createdAt?: Maybe<DateOperators>;
  updatedAt?: Maybe<DateOperators>;
  name?: Maybe<StringOperators>;
  backgroundImage?: Maybe<StringOperators>;
  price?: Maybe<NumberOperators>;
  introduce?: Maybe<StringOperators>;
  protocolUsage?: Maybe<StringOperators>;
  isShow?: Maybe<BooleanOperators>;
  customerServiceNumber?: Maybe<StringOperators>;
  memberPriceId?: Maybe<IdOperators>;
  state?: Maybe<StringOperators>;
  discountLimitSourceType?: Maybe<StringOperators>;
  needActivate?: Maybe<BooleanOperators>;
};

export type MembershipPlanInput = {
  id?: Maybe<Scalars['ID']>;
  name: Scalars['String'];
  backgroundImage: Scalars['String'];
  bannerImg?: Maybe<Array<Maybe<Scalars['String']>>>;
  validityPeriod: MembershipPlanValidityPeriodInput;
  price: Scalars['Int'];
  isShow?: Maybe<Scalars['Boolean']>;
  introduce: Scalars['String'];
  protocolUsage: Scalars['String'];
  customerServiceNumber: Scalars['String'];
  rightsDiscount: RightsDiscountInput;
  rightsPoints: RightsPointsInput;
  rightsCoupon: RightsCouponInput;
  memberPriceId?: Maybe<Scalars['ID']>;
  discountLimitSourceType?: Maybe<MemberCardDiscountLimitSourceType>;
  needActivate?: Maybe<Scalars['Boolean']>;
};

export type MembershipPlanList = PaginatedList & {
  __typename?: 'MembershipPlanList';
  items: Array<MembershipPlan>;
  totalItems: Scalars['Int'];
};

export type MembershipPlanListOptions = {
  /** Skips the first n results, for use in pagination */
  skip?: Maybe<Scalars['Int']>;
  /** Takes n results, for use in pagination */
  take?: Maybe<Scalars['Int']>;
  /** Specifies which properties to sort the results by */
  sort?: Maybe<MembershipPlanSortParameter>;
  /** Allows the results to be filtered */
  filter?: Maybe<MembershipPlanFilterParameter>;
  /** Specifies whether multiple "filter" arguments should be combines with a logical AND or OR operation. Defaults to AND. */
  filterOperator?: Maybe<LogicalOperator>;
};

export type MembershipPlanSortParameter = {
  id?: Maybe<SortOrder>;
  createdAt?: Maybe<SortOrder>;
  updatedAt?: Maybe<SortOrder>;
  name?: Maybe<SortOrder>;
  backgroundImage?: Maybe<SortOrder>;
  price?: Maybe<SortOrder>;
  introduce?: Maybe<SortOrder>;
  protocolUsage?: Maybe<SortOrder>;
  customerServiceNumber?: Maybe<SortOrder>;
  memberPriceId?: Maybe<SortOrder>;
};

export enum MembershipPlanState {
  Draft = 'draft',
  Shelf = 'shelf',
  Disable = 'disable',
  TakeOffTheShelf = 'takeOffTheShelf'
}

export type MembershipPlanSwitchingStateInput = {
  id: Scalars['ID'];
  state: SwitchingState;
};

export type MembershipPlanValidityPeriod = {
  __typename?: 'MembershipPlanValidityPeriod';
  type: ValidityPeriodType;
  startTime?: Maybe<Scalars['DateTime']>;
  endTime?: Maybe<Scalars['DateTime']>;
  numberOfDays?: Maybe<Scalars['Int']>;
};

export type MembershipPlanValidityPeriodInput = {
  type: ValidityPeriodType;
  startTime?: Maybe<Scalars['DateTime']>;
  endTime?: Maybe<Scalars['DateTime']>;
  numberOfDays?: Maybe<Scalars['Int']>;
};

export type MenuCollection = {
  __typename?: 'MenuCollection';
  menuName: Scalars['String'];
  collectionId: Scalars['ID'];
};

export type MenuCollectionInput = {
  menuName: Scalars['String'];
  collectionId: Scalars['ID'];
};

export type MerchantVoluntaryRefund = Node & {
  __typename?: 'MerchantVoluntaryRefund';
  id: Scalars['ID'];
  createdAt: Scalars['DateTime'];
  updatedAt: Scalars['DateTime'];
  order?: Maybe<Order>;
  orderLine?: Maybe<OrderLine>;
  price?: Maybe<Scalars['Int']>;
  reason?: Maybe<Scalars['String']>;
};

/** Returned when attempting to register or verify a customer account without a password, when one is required. */
export type MissingPasswordError = ErrorResult & {
  __typename?: 'MissingPasswordError';
  errorCode: ErrorCode;
  message: Scalars['String'];
};


export type Mutation = {
  __typename?: 'Mutation';
  /** Adds an item to the order. If custom fields are defined on the OrderLine entity, a third argument 'customFields' will be available. */
  addItemToOrder: UpdateOrderItemsResult;
  /** Remove an OrderLine from the Order */
  removeOrderLine: RemoveOrderItemsResult;
  /** Remove all OrderLine from the Order */
  removeAllOrderLines: RemoveOrderItemsResult;
  /** Adjusts an OrderLine. If custom fields are defined on the OrderLine entity, a third argument 'customFields' of type `OrderLineCustomFieldsInput` will be available. */
  adjustOrderLine: UpdateOrderItemsResult;
  /** Applies the given coupon code to the active Order */
  applyCouponCode: ApplyCouponCodeResult;
  /** Removes the given coupon code from the active Order */
  removeCouponCode?: Maybe<Order>;
  /** Transitions an Order to a new state. Valid next states can be found by querying `nextOrderStates` */
  transitionOrderToState?: Maybe<TransitionOrderToStateResult>;
  /** Sets the shipping address for this order */
  setOrderShippingAddress: ActiveOrderResult;
  /** Sets the billing address for this order */
  setOrderBillingAddress: ActiveOrderResult;
  /** Allows any custom fields to be set for the active order */
  setOrderCustomFields: ActiveOrderResult;
  /**
   * Sets the shipping method by id, which can be obtained with the `eligibleShippingMethods` query.
   * An Order can have multiple shipping methods, in which case you can pass an array of ids. In this case,
   * you should configure a custom ShippingLineAssignmentStrategy in order to know which OrderLines each
   * shipping method will apply to.
   */
  setOrderShippingMethod: SetOrderShippingMethodResult;
  /** Add a Payment to the Order */
  addPaymentToOrder: AddPaymentToOrderResult;
  /** Set the Customer for the Order. Required only if the Customer is not currently logged in */
  setCustomerForOrder: SetCustomerForOrderResult;
  /** Authenticates the user using the native authentication strategy. This mutation is an alias for `authenticate({ native: { ... }})` */
  login: NativeAuthenticationResult;
  /** Authenticates the user using a named authentication strategy */
  authenticate: AuthenticationResult;
  /** End the current authenticated session */
  logout: Success;
  /**
   * Register a Customer account with the given credentials. There are three possible registration flows:
   *
   * _If `authOptions.requireVerification` is set to `true`:_
   *
   * 1. **The Customer is registered _with_ a password**. A verificationToken will be created (and typically emailed to the Customer). That
   *    verificationToken would then be passed to the `verifyCustomerAccount` mutation _without_ a password. The Customer is then
   *    verified and authenticated in one step.
   * 2. **The Customer is registered _without_ a password**. A verificationToken will be created (and typically emailed to the Customer). That
   *    verificationToken would then be passed to the `verifyCustomerAccount` mutation _with_ the chosen password of the Customer. The Customer is then
   *    verified and authenticated in one step.
   *
   * _If `authOptions.requireVerification` is set to `false`:_
   *
   * 3. The Customer _must_ be registered _with_ a password. No further action is needed - the Customer is able to authenticate immediately.
   */
  registerCustomerAccount: RegisterCustomerAccountResult;
  /** Regenerate and send a verification token for a new Customer registration. Only applicable if `authOptions.requireVerification` is set to true. */
  refreshCustomerVerification: RefreshCustomerVerificationResult;
  /** Update an existing Customer */
  updateCustomer: Customer;
  /** Create a new Customer Address */
  createCustomerAddress: Address;
  /** Update an existing Address */
  updateCustomerAddress: Address;
  /** Delete an existing Address */
  deleteCustomerAddress: Success;
  /**
   * Verify a Customer email address with the token sent to that address. Only applicable if `authOptions.requireVerification` is set to true.
   *
   * If the Customer was not registered with a password in the `registerCustomerAccount` mutation, the password _must_ be
   * provided here.
   */
  verifyCustomerAccount: VerifyCustomerAccountResult;
  /** Update the password of the active Customer */
  updateCustomerPassword: UpdateCustomerPasswordResult;
  /**
   * Request to update the emailAddress of the active Customer. If `authOptions.requireVerification` is enabled
   * (as is the default), then the `identifierChangeToken` will be assigned to the current User and
   * a IdentifierChangeRequestEvent will be raised. This can then be used e.g. by the EmailPlugin to email
   * that verification token to the Customer, which is then used to verify the change of email address.
   */
  requestUpdateCustomerEmailAddress: RequestUpdateCustomerEmailAddressResult;
  /**
   * Confirm the update of the emailAddress with the provided token, which has been generated by the
   * `requestUpdateCustomerEmailAddress` mutation.
   */
  updateCustomerEmailAddress: UpdateCustomerEmailAddressResult;
  /** Requests a password reset email to be sent */
  requestPasswordReset?: Maybe<RequestPasswordResetResult>;
  /** Resets a Customer's password based on the provided token */
  resetPassword: ResetPasswordResult;
  toggleFavorite: FavoriteList;
  createReviewProduct?: Maybe<ReviewProduct>;
  updateReviewProduct?: Maybe<ReviewProduct>;
  upsertBlindBox: BlindBox;
  deleteBlindBox: DeletionResponse;
  upsertBlindBoxActivity: BlindBoxActivity;
  deleteBlindBoxActivity: DeletionResponse;
  upsertBlindBoxActivityLimitConfig?: Maybe<BlindBoxActivityLimitConfig>;
  upsertAssistGift: AssistGift;
  deleteAssistGift: DeletionResponse;
  upsertAssistGiftConfig: AssistGiftConfig;
  deleteAssistGiftConfig: DeletionResponse;
  purchaseBlindBox: WeChatInitPayInfo;
  assistBlindBox?: Maybe<AssistStatusResponse>;
  receiveAssistGift?: Maybe<ReceiveAssistGiftResponse>;
  reOpenBlindBox?: Maybe<BlindBoxOpenRecord>;
  refundBlindBox?: Maybe<BlindBoxBuy>;
  testAddPaymentToBlindBoxOrder?: Maybe<Scalars['Boolean']>;
  openBlindBoxAssist?: Maybe<BlindBoxBuy>;
  takeBlindBoxOrder?: Maybe<BlindBoxBuy>;
  blindBoxActivityBooking?: Maybe<Scalars['Boolean']>;
  addCustomerSource?: Maybe<CustomerSource>;
  addCustomerSourceMiniProgram?: Maybe<CustomerReferralSource>;
  blindBoxPickUp?: Maybe<BlindBoxOpenRecord>;
  failureBlindBoxActivity?: Maybe<BlindBoxActivity>;
  upsertFloatingWindow?: Maybe<FloatingWindow>;
  customerCheckin?: Maybe<CustomerCheckinResult>;
  upsertCheckinConfig?: Maybe<CheckinConfig>;
  upsertProductRestrictions?: Maybe<ProductRestrictions>;
  upsertPointsConfig?: Maybe<PointsConfig>;
  upsertPointsProduct?: Maybe<PointsProduct>;
  softDeletePointsProduct?: Maybe<DeletionResponse>;
  upsertCouponBundle?: Maybe<CouponBundle>;
  deleteCouponBundle?: Maybe<DeletionResponse>;
  failureCouponBundle?: Maybe<CouponBundle>;
  upsertFirstCustomerBenefit?: Maybe<FirstCustomerBenefit>;
  deleteFirstCustomerBenefit?: Maybe<DeletionResponse>;
  failureFirstCustomerBenefit?: Maybe<FirstCustomerBenefit>;
  upsertProductPurchase?: Maybe<ProductPurchasePermission>;
  reportedDistributorRecord?: Maybe<Scalars['Boolean']>;
  createErrorLog?: Maybe<ErrorLogs>;
  updateAssetSize?: Maybe<Scalars['String']>;
  distributorDetailStatistics?: Maybe<Scalars['String']>;
  distributorOrderTotalAmountStatistics?: Maybe<Scalars['String']>;
  upsertDistributorGroup?: Maybe<DistributorGroup>;
  bindingOrderLineTracking?: Maybe<Scalars['String']>;
  addOrderTracking?: Maybe<OrderTracking>;
  upsertUMengConfig?: Maybe<UMengConfig>;
  createBanner?: Maybe<Banner>;
  updateBanner?: Maybe<Banner>;
  deleteBanner?: Maybe<Scalars['ID']>;
  upsertCustomPage?: Maybe<CustomPage>;
  updateSortCustomPage?: Maybe<CustomPage>;
  transitionEnableCustomPage?: Maybe<CustomPage>;
  deleteCustomPage?: Maybe<DeletionResponse>;
  upsertHotWord?: Maybe<HotWord>;
  deleteHotWord?: Maybe<DeletionResponse>;
  upsertAnnouncement?: Maybe<Announcement>;
  upsertCoupon?: Maybe<Coupon>;
  failureUserCoupon?: Maybe<UserCoupon>;
  failureCoupon?: Maybe<Coupon>;
  softDeleteCoupon?: Maybe<DeletionResponse>;
  enableSwitchCoupon?: Maybe<Coupon>;
  claimCoupon?: Maybe<UserCoupon>;
  complimentaryCouponToMember?: Maybe<Coupon>;
  grantCoupon?: Maybe<Coupon>;
  applyCoupon?: Maybe<Order>;
  orderApplyCoupon?: Maybe<Order>;
  cancelCoupon?: Maybe<Order>;
  applyMember?: Maybe<Order>;
  cancelMember?: Maybe<Order>;
  applyShoppingCredits?: Maybe<Order>;
  cancelShoppingCredits?: Maybe<Order>;
  selectGift?: Maybe<Order>;
  cancelGift?: Maybe<Order>;
  upsertPurchasePremium?: Maybe<PurchasePremium>;
  purchasePremiumFailure?: Maybe<PurchasePremium>;
  softDeletePurchasePremium?: Maybe<DeletionResponse>;
  selectMarkUp?: Maybe<Order>;
  cancelMarkUp?: Maybe<Order>;
  cancelAllMarkUp?: Maybe<Order>;
  upsertDistributor?: Maybe<Distributor>;
  boundDistributor?: Maybe<Scalars['String']>;
  softDeleteDistributor?: Maybe<DeletionResponse>;
  upsertDiscountActivity?: Maybe<DiscountActivity>;
  failureDiscountActivity?: Maybe<DiscountActivity>;
  softDeleteDiscountActivity?: Maybe<DeletionResponse>;
  upsertFullDiscountPresent?: Maybe<FullDiscountPresent>;
  softDeleteFullDiscountPresent?: Maybe<DeletionResponse>;
  failureFullDiscountPresent?: Maybe<FullDiscountPresent>;
  upsertSelectiveGiftActivity?: Maybe<SelectiveGiftActivity>;
  softDeleteSelectiveGiftActivity?: Maybe<DeletionResponse>;
  failureSelectiveGiftActivity?: Maybe<SelectiveGiftActivity>;
  upsertFreeGift?: Maybe<FreeGift>;
  failureFreeGift?: Maybe<FreeGift>;
  softDeleteFreeGift?: Maybe<DeletionResponse>;
  upsertSetting?: Maybe<Array<Maybe<Setting>>>;
  automaticPromotionCreate?: Maybe<Promotion>;
  userClick?: Maybe<Scalars['Boolean']>;
  userClickProduct?: Maybe<Scalars['Boolean']>;
  upsertGroupCustomer?: Maybe<GroupCustomer>;
  deleteGroupCustomer?: Maybe<DeletionResponse>;
  upsertOperationPlan?: Maybe<OperationPlan>;
  deleteOperationPlan?: Maybe<DeletionResponse>;
  changeOperationPlanState?: Maybe<OperationPlan>;
  sendMessage?: Maybe<Scalars['String']>;
  asyncWechatLogisticsCompany?: Maybe<Scalars['String']>;
  switchoverDistributor?: Maybe<Scalars['String']>;
  upsertPackageDiscount?: Maybe<PackageDiscount>;
  softDeletePackageDiscount?: Maybe<DeletionResponse>;
  failurePackageDiscount?: Maybe<PackageDiscount>;
  upsertMemberPrice?: Maybe<MemberPrice>;
  upsertMemberPriceProduct?: Maybe<MemberPriceProductList>;
  deleteMemberPrice?: Maybe<DeletionResponse>;
  deleteMemberPriceProduct?: Maybe<DeletionResponse>;
  deleteMemberPriceProductVariant?: Maybe<DeletionResponse>;
  setMemberShipPlanMemberPrice?: Maybe<MemberPrice>;
  returnGiftCardOrder?: Maybe<GiftCardReturn>;
  upsertPaymentRewardActivity?: Maybe<PaymentRewardActivity>;
  softDeletePaymentRewardActivity?: Maybe<DeletionResponse>;
  failurePaymentRewardActivity?: Maybe<PaymentRewardActivity>;
  timedPublishingPageAll?: Maybe<Scalars['String']>;
  paymentReward?: Maybe<Scalars['Boolean']>;
  shoppingCreditValidPeriodAll?: Maybe<Scalars['Boolean']>;
  upsertShareSetting?: Maybe<Array<Maybe<ShareSetting>>>;
  upsertPersonalCenter?: Maybe<PersonalCenter>;
  upsertShoppingCreditsConfig?: Maybe<ShoppingCreditsConfig>;
  upsertShoppingCreditsClaimActivity?: Maybe<ShoppingCreditsClaimActivity>;
  failureShoppingCreditsClaimActivity?: Maybe<ShoppingCreditsClaimActivity>;
  softDeleteShoppingCreditsClaimActivity?: Maybe<DeletionResponse>;
  upsertActivityCountdown?: Maybe<ActivityCountdown>;
  deleteActivityCountdown?: Maybe<DeletionResponse>;
  setDistributorBindingRemark?: Maybe<DistributorBinding>;
  addDistributorShareProduct?: Maybe<DistributorProductRecord>;
  receivePaymentRewardCoupon?: Maybe<OrderPaymentRewardCoupon>;
  receiveCouponBundle?: Maybe<Array<Maybe<UserCoupon>>>;
  receiveFirstCustomerBenefit?: Maybe<Array<Maybe<UserCoupon>>>;
  upsertSubscriptionPlan?: Maybe<SubscriptionPlan>;
  deleteSubscriptionPlan?: Maybe<Scalars['String']>;
  updateSubscriptionAddress?: Maybe<Subscription>;
  deleteSubscription?: Maybe<DeletionResponse>;
  updateSubscriptionFrequency?: Maybe<Subscription>;
  updateSubscriptionFirstShippingDate?: Maybe<Subscription>;
  unsubscribe?: Maybe<RefundResult>;
  createOrderBySubscription?: Maybe<Subscription>;
  createOrderAfterCreatingCtx?: Maybe<Subscription>;
  updateSubscriptionPeriodSkipValidation?: Maybe<Subscription>;
  accomplishRefundOrder?: Maybe<OrderRefund>;
  createRefund?: Maybe<OrderRefund>;
  cancelRefund?: Maybe<OrderRefund>;
  updateRefund?: Maybe<OrderRefund>;
  updatePeriod?: Maybe<Scalars['String']>;
  createOrder?: Maybe<Scalars['String']>;
  deleteOrder?: Maybe<DeletionResponse>;
  updateOrderShippingAddress?: Maybe<Order>;
  setShippingAddress?: Maybe<Order>;
  setShippingMethod?: Maybe<Order>;
  confirmReceiptOfGoods?: Maybe<Order>;
  addItemToOrderByOrderType?: Maybe<UpdateOrderItemsResult>;
  adjustOrderLineByOrderType?: Maybe<Order>;
  removeOrderLineByOrderType?: Maybe<Order>;
  removeAllOrderLinesByOrderType?: Maybe<Order>;
  shoppingCartSettlement?: Maybe<Order>;
  cancelledOrder?: Maybe<Order>;
  updateShoppingCartSku?: Maybe<Order>;
  addSurchargeToOrder?: Maybe<Order>;
  newSetOrderCustomFields?: Maybe<Order>;
  noLogisticsUpdateOrder?: Maybe<Order>;
  setShippingMethodByOrderType?: Maybe<Order>;
  addPaymentToOrderByOrderType?: Maybe<Order>;
  createSubscriptionReview?: Maybe<Subscription>;
  loginExternal?: Maybe<LoginReturn>;
  getWechatPhone?: Maybe<LoginReturn>;
  refundByOrderId?: Maybe<Scalars['String']>;
  createWxChatPaymentIntent?: Maybe<WeChatInitPayInfo>;
  upsertWeChatConfig?: Maybe<WeChatConfig>;
  initPay?: Maybe<Scalars['String']>;
  phoneLogin?: Maybe<LoginReturn>;
  sendCode?: Maybe<Note>;
  getJsConfig?: Maybe<JsSdkConfig>;
  purchaseMembership?: Maybe<WeChatInitPayInfo>;
  purchaseGiftCard?: Maybe<WeChatInitPayInfo>;
  clickAdvertisingSave?: Maybe<Scalars['String']>;
  reportingAdvertisingAttribution?: Maybe<Scalars['String']>;
  setWechatMsgJumpPath?: Maybe<WeChatConfig>;
  userDevice?: Maybe<ReturnInfo>;
  uploadByStream?: Maybe<Scalars['String']>;
  settingCompActivity?: Maybe<CompActivity>;
  joinCompActivity?: Maybe<CompActivity>;
  quitCompActivity?: Maybe<CompActivity>;
  draw?: Maybe<CompRecord>;
  pick?: Maybe<CompRecord>;
  ship?: Maybe<CompRecord>;
  createChannelAddress?: Maybe<ChannelAddress>;
  updateChannelAddress?: Maybe<ChannelAddress>;
  deleteChannelAddress?: Maybe<DeletionResponse>;
  createAfterSale?: Maybe<AfterSale>;
  updateAfterSale?: Maybe<AfterSale>;
  afterSaleAudit?: Maybe<AfterSale>;
  fillOutReturnLogistics?: Maybe<AfterSale>;
  updateReturnLogistics?: Maybe<AfterSale>;
  cancelAfterSale?: Maybe<AfterSale>;
  updateOrderPrice?: Maybe<Order>;
  afterSaleCustomerReceipt?: Maybe<AfterSale>;
  createMembershipPlan?: Maybe<MembershipPlan>;
  updateMembershipPlan?: Maybe<MembershipPlan>;
  deleteMembershipPlan?: Maybe<DeletionResponse>;
  switchingState?: Maybe<MembershipPlan>;
  membershipCardActivation?: Maybe<Member>;
  returnMembershipCard?: Maybe<MembershipOrder>;
  extendMembershipTime?: Maybe<Member>;
  reduceMembershipTime?: Maybe<Member>;
  showHideMembershipPlan?: Maybe<MembershipPlan>;
  addCustomerToMember?: Maybe<Member>;
  upsertGiftCard?: Maybe<GiftCard>;
  softDeleteGiftCard?: Maybe<DeletionResponse>;
  createGiftCardOrder?: Maybe<GiftCardOrder>;
  addPaymentToGiftCardOrder?: Maybe<GiftCardOrder>;
  switchingGiftCardState?: Maybe<GiftCard>;
  upsertForumTag: ForumTag;
  deleteForumTag: DeletionResponse;
  updateForumTagStatus: ForumTag;
  upsertForumActivity: ForumActivity;
  deleteForumActivity: DeletionResponse;
  updateForumActivityStatus: ForumActivity;
  upsertForumCustomer: ForumCustomer;
  deleteForumCustomer: DeletionResponse;
  upsertForumPost: ForumPost;
  deleteForumPost: DeletionResponse;
  auditForumPost: ForumPost;
  forumDownPost?: Maybe<ForumVote>;
  forumUpPost?: Maybe<ForumVote>;
  forumReviewPost?: Maybe<ForumReview>;
  reviewForumAudit: ForumReview;
  deleteForumReview: DeletionResponse;
  markNotificationsAsRead?: Maybe<Scalars['Boolean']>;
  forumPostShare?: Maybe<ForumPost>;
  forumPostPin?: Maybe<ForumPost>;
  upsertWangDianTongConfig: WangDianTongConfig;
};


export type MutationAddItemToOrderArgs = {
  productVariantId: Scalars['ID'];
  quantity: Scalars['Int'];
  customFields?: Maybe<OrderLineCustomFieldsInput>;
};


export type MutationRemoveOrderLineArgs = {
  orderLineId: Scalars['ID'];
};


export type MutationAdjustOrderLineArgs = {
  orderLineId: Scalars['ID'];
  quantity: Scalars['Int'];
  customFields?: Maybe<OrderLineCustomFieldsInput>;
};


export type MutationApplyCouponCodeArgs = {
  couponCode: Scalars['String'];
};


export type MutationRemoveCouponCodeArgs = {
  couponCode: Scalars['String'];
};


export type MutationTransitionOrderToStateArgs = {
  state: Scalars['String'];
};


export type MutationSetOrderShippingAddressArgs = {
  input: CreateAddressInput;
};


export type MutationSetOrderBillingAddressArgs = {
  input: CreateAddressInput;
};


export type MutationSetOrderCustomFieldsArgs = {
  input: UpdateOrderInput;
};


export type MutationSetOrderShippingMethodArgs = {
  shippingMethodId: Array<Scalars['ID']>;
};


export type MutationAddPaymentToOrderArgs = {
  input: PaymentInput;
};


export type MutationSetCustomerForOrderArgs = {
  input: CreateCustomerInput;
};


export type MutationLoginArgs = {
  username: Scalars['String'];
  password: Scalars['String'];
  rememberMe?: Maybe<Scalars['Boolean']>;
};


export type MutationAuthenticateArgs = {
  input: AuthenticationInput;
  rememberMe?: Maybe<Scalars['Boolean']>;
};


export type MutationRegisterCustomerAccountArgs = {
  input: RegisterCustomerInput;
};


export type MutationRefreshCustomerVerificationArgs = {
  emailAddress: Scalars['String'];
};


export type MutationUpdateCustomerArgs = {
  input: UpdateCustomerInput;
};


export type MutationCreateCustomerAddressArgs = {
  input: CreateAddressInput;
};


export type MutationUpdateCustomerAddressArgs = {
  input: UpdateAddressInput;
};


export type MutationDeleteCustomerAddressArgs = {
  id: Scalars['ID'];
};


export type MutationVerifyCustomerAccountArgs = {
  token: Scalars['String'];
  password?: Maybe<Scalars['String']>;
};


export type MutationUpdateCustomerPasswordArgs = {
  currentPassword: Scalars['String'];
  newPassword: Scalars['String'];
};


export type MutationRequestUpdateCustomerEmailAddressArgs = {
  password: Scalars['String'];
  newEmailAddress: Scalars['String'];
};


export type MutationUpdateCustomerEmailAddressArgs = {
  token: Scalars['String'];
};


export type MutationRequestPasswordResetArgs = {
  emailAddress: Scalars['String'];
};


export type MutationResetPasswordArgs = {
  token: Scalars['String'];
  password: Scalars['String'];
};


export type MutationToggleFavoriteArgs = {
  productId: Scalars['ID'];
  options?: Maybe<FavoriteListOptions>;
};


export type MutationCreateReviewProductArgs = {
  input: CreateReviewProductInput;
};


export type MutationUpdateReviewProductArgs = {
  input: UpdateReviewProductInput;
};


export type MutationUpsertBlindBoxArgs = {
  input: BlindBoxInput;
};


export type MutationDeleteBlindBoxArgs = {
  blindBoxId: Scalars['ID'];
};


export type MutationUpsertBlindBoxActivityArgs = {
  input: BlindBoxActivityInput;
};


export type MutationDeleteBlindBoxActivityArgs = {
  blindBoxActivityId: Scalars['ID'];
};


export type MutationUpsertBlindBoxActivityLimitConfigArgs = {
  input: BlindBoxActivityLimitConfigInput;
};


export type MutationUpsertAssistGiftArgs = {
  input: AssistGiftInput;
};


export type MutationDeleteAssistGiftArgs = {
  assistGiftId: Scalars['ID'];
};


export type MutationUpsertAssistGiftConfigArgs = {
  input: AssistGiftConfigInput;
};


export type MutationDeleteAssistGiftConfigArgs = {
  assistGiftConfigId: Scalars['ID'];
};


export type MutationPurchaseBlindBoxArgs = {
  blindBoxActivityId: Scalars['ID'];
  wishBlindBoxId: Scalars['ID'];
  paymentType?: Maybe<WeChatPaymentType>;
};


export type MutationAssistBlindBoxArgs = {
  blindBoxBuyId: Scalars['ID'];
};


export type MutationReceiveAssistGiftArgs = {
  assistGiftRecordId: Scalars['ID'];
};


export type MutationReOpenBlindBoxArgs = {
  blindBoxBuyId: Scalars['ID'];
};


export type MutationRefundBlindBoxArgs = {
  blindBoxBuyId: Scalars['ID'];
};


export type MutationTestAddPaymentToBlindBoxOrderArgs = {
  orderCode: Scalars['String'];
  payment?: Maybe<BlindBoxPaymentInput>;
};


export type MutationOpenBlindBoxAssistArgs = {
  blindBoxBuyId: Scalars['ID'];
};


export type MutationTakeBlindBoxOrderArgs = {
  blindBoxBuyId: Scalars['ID'];
};


export type MutationBlindBoxActivityBookingArgs = {
  blindBoxActivityId: Scalars['ID'];
};


export type MutationAddCustomerSourceArgs = {
  customerSourceInput: CustomerSourceInput;
};


export type MutationAddCustomerSourceMiniProgramArgs = {
  customerReferralInput: CustomerReferralSourceInput;
};


export type MutationBlindBoxPickUpArgs = {
  blindBoxOpenRecordId: Scalars['ID'];
  blindBoxBuyId: Scalars['ID'];
};


export type MutationFailureBlindBoxActivityArgs = {
  blindBoxActivityId: Scalars['ID'];
};


export type MutationUpsertFloatingWindowArgs = {
  input: FloatingWindowInput;
};


export type MutationUpsertCheckinConfigArgs = {
  input: CheckinConfigInput;
};


export type MutationUpsertProductRestrictionsArgs = {
  input: ProductRestrictionsInput;
};


export type MutationUpsertPointsConfigArgs = {
  input: PointsConfigInput;
};


export type MutationUpsertPointsProductArgs = {
  input: PointsProductInput;
};


export type MutationSoftDeletePointsProductArgs = {
  id: Scalars['ID'];
};


export type MutationUpsertCouponBundleArgs = {
  input: CouponBundleInput;
};


export type MutationDeleteCouponBundleArgs = {
  id: Scalars['ID'];
};


export type MutationFailureCouponBundleArgs = {
  id: Scalars['ID'];
};


export type MutationUpsertFirstCustomerBenefitArgs = {
  input: FirstCustomerBenefitInput;
};


export type MutationDeleteFirstCustomerBenefitArgs = {
  id: Scalars['ID'];
};


export type MutationFailureFirstCustomerBenefitArgs = {
  id: Scalars['ID'];
};


export type MutationUpsertProductPurchaseArgs = {
  input: ProductPurchasePermissionInput;
};


export type MutationReportedDistributorRecordArgs = {
  md5Str?: Maybe<Scalars['String']>;
  sourceCode?: Maybe<Scalars['String']>;
  distributionId?: Maybe<Scalars['String']>;
};


export type MutationCreateErrorLogArgs = {
  errorType: Scalars['String'];
  errorStatus: Scalars['String'];
  details: Scalars['JSON'];
};


export type MutationUpdateAssetSizeArgs = {
  pageSize?: Maybe<Scalars['Int']>;
  page?: Maybe<Scalars['Int']>;
  isUpdateAll?: Maybe<Scalars['Boolean']>;
};


export type MutationUpsertDistributorGroupArgs = {
  input?: Maybe<DistributorGroupInput>;
};


export type MutationBindingOrderLineTrackingArgs = {
  orderId: Scalars['ID'];
  productVariantId: Scalars['ID'];
  trackingPageType: TrackingPageType;
  trackingPageId?: Maybe<Scalars['ID']>;
};


export type MutationAddOrderTrackingArgs = {
  orderId: Scalars['ID'];
  trackingPageType: TrackingPageType;
  trackingPageId?: Maybe<Scalars['ID']>;
};


export type MutationUpsertUMengConfigArgs = {
  input?: Maybe<UMengConfigInput>;
};


export type MutationCreateBannerArgs = {
  input: BannerInput;
};


export type MutationUpdateBannerArgs = {
  input: BannerInput;
  bannerId: Scalars['String'];
};


export type MutationDeleteBannerArgs = {
  bannerId: Scalars['String'];
};


export type MutationUpsertCustomPageArgs = {
  input?: Maybe<CustomPageInput>;
};


export type MutationUpdateSortCustomPageArgs = {
  customPageId: Scalars['ID'];
  componentId: Scalars['ID'];
  direction: Direction;
};


export type MutationTransitionEnableCustomPageArgs = {
  enable: Scalars['Boolean'];
  customPageId: Scalars['ID'];
};


export type MutationDeleteCustomPageArgs = {
  customPageId: Scalars['ID'];
};


export type MutationUpsertHotWordArgs = {
  input: HotWordInput;
};


export type MutationDeleteHotWordArgs = {
  hotWordId: Scalars['ID'];
};


export type MutationUpsertAnnouncementArgs = {
  input: AnnouncementInput;
};


export type MutationUpsertCouponArgs = {
  input: CouponInput;
};


export type MutationFailureUserCouponArgs = {
  userCouponId: Scalars['ID'];
};


export type MutationFailureCouponArgs = {
  couponId?: Maybe<Scalars['ID']>;
};


export type MutationSoftDeleteCouponArgs = {
  couponId: Scalars['ID'];
};


export type MutationEnableSwitchCouponArgs = {
  couponId: Scalars['ID'];
};


export type MutationClaimCouponArgs = {
  couponId: Scalars['ID'];
};


export type MutationComplimentaryCouponToMemberArgs = {
  input: ComplimentaryCouponObjectInput;
};


export type MutationGrantCouponArgs = {
  quantity: Scalars['Int'];
  couponId: Scalars['ID'];
  customerId: Scalars['ID'];
};


export type MutationApplyCouponArgs = {
  userCouponId: Scalars['ID'];
  orderId?: Maybe<Scalars['ID']>;
};


export type MutationOrderApplyCouponArgs = {
  orderId: Scalars['ID'];
};


export type MutationCancelCouponArgs = {
  orderId?: Maybe<Scalars['ID']>;
};


export type MutationApplyMemberArgs = {
  orderId?: Maybe<Scalars['ID']>;
};


export type MutationCancelMemberArgs = {
  orderId?: Maybe<Scalars['ID']>;
};


export type MutationApplyShoppingCreditsArgs = {
  orderId: Scalars['ID'];
};


export type MutationCancelShoppingCreditsArgs = {
  orderId: Scalars['ID'];
};


export type MutationSelectGiftArgs = {
  orderId: Scalars['ID'];
  skuId: Scalars['ID'];
  giftId: Scalars['ID'];
  promInstanceId: Scalars['ID'];
  ladderLevel?: Maybe<Scalars['Int']>;
};


export type MutationCancelGiftArgs = {
  orderId: Scalars['ID'];
  giftId: Scalars['ID'];
  promInstanceId: Scalars['ID'];
};


export type MutationUpsertPurchasePremiumArgs = {
  input: PurchasePremiumInput;
};


export type MutationPurchasePremiumFailureArgs = {
  purchasePremiumId: Scalars['ID'];
};


export type MutationSoftDeletePurchasePremiumArgs = {
  purchasePremiumId: Scalars['ID'];
};


export type MutationSelectMarkUpArgs = {
  orderId: Scalars['ID'];
  skuId: Scalars['ID'];
  productId: Scalars['ID'];
  promInstanceId: Scalars['ID'];
};


export type MutationCancelMarkUpArgs = {
  orderId: Scalars['ID'];
  productId: Scalars['ID'];
  promInstanceId: Scalars['ID'];
};


export type MutationCancelAllMarkUpArgs = {
  orderId: Scalars['ID'];
};


export type MutationUpsertDistributorArgs = {
  input?: Maybe<DistributorInput>;
};


export type MutationBoundDistributorArgs = {
  distributorId: Scalars['ID'];
};


export type MutationSoftDeleteDistributorArgs = {
  distributorId: Scalars['ID'];
};


export type MutationUpsertDiscountActivityArgs = {
  input: DiscountActivityInput;
};


export type MutationFailureDiscountActivityArgs = {
  discountActivityId: Scalars['ID'];
};


export type MutationSoftDeleteDiscountActivityArgs = {
  discountActivityId: Scalars['ID'];
};


export type MutationUpsertFullDiscountPresentArgs = {
  input: FullDiscountPresentInput;
};


export type MutationSoftDeleteFullDiscountPresentArgs = {
  id: Scalars['ID'];
};


export type MutationFailureFullDiscountPresentArgs = {
  id: Scalars['ID'];
};


export type MutationUpsertSelectiveGiftActivityArgs = {
  input: SelectiveGiftActivityInput;
};


export type MutationSoftDeleteSelectiveGiftActivityArgs = {
  id: Scalars['ID'];
};


export type MutationFailureSelectiveGiftActivityArgs = {
  id: Scalars['ID'];
};


export type MutationUpsertFreeGiftArgs = {
  input: FreeGiftInput;
};


export type MutationFailureFreeGiftArgs = {
  id: Scalars['ID'];
};


export type MutationSoftDeleteFreeGiftArgs = {
  id: Scalars['ID'];
};


export type MutationUpsertSettingArgs = {
  input: Array<Maybe<SettingInput>>;
};


export type MutationUserClickProductArgs = {
  productId?: Maybe<Scalars['ID']>;
};


export type MutationUpsertGroupCustomerArgs = {
  input: GroupCustomerInput;
};


export type MutationDeleteGroupCustomerArgs = {
  id: Scalars['ID'];
};


export type MutationUpsertOperationPlanArgs = {
  input: OperationPlanInput;
};


export type MutationDeleteOperationPlanArgs = {
  id: Scalars['ID'];
};


export type MutationChangeOperationPlanStateArgs = {
  id: Scalars['ID'];
  state: OperationPlanState;
};


export type MutationSendMessageArgs = {
  phone: Scalars['String'];
};


export type MutationSwitchoverDistributorArgs = {
  distributorId: Scalars['ID'];
  customerId: Scalars['ID'];
};


export type MutationUpsertPackageDiscountArgs = {
  input: PackageDiscountInput;
};


export type MutationSoftDeletePackageDiscountArgs = {
  id: Scalars['ID'];
};


export type MutationFailurePackageDiscountArgs = {
  id: Scalars['ID'];
};


export type MutationUpsertMemberPriceArgs = {
  input: MemberPriceInput;
};


export type MutationUpsertMemberPriceProductArgs = {
  memberPriceId: Scalars['ID'];
  input: Array<MemberPriceProductInput>;
  options?: Maybe<MemberPriceProductListOptions>;
};


export type MutationDeleteMemberPriceArgs = {
  memberPriceId: Scalars['ID'];
};


export type MutationDeleteMemberPriceProductArgs = {
  memberPriceProductIds: Array<Scalars['ID']>;
};


export type MutationDeleteMemberPriceProductVariantArgs = {
  memberPriceProductVariantIds: Array<Scalars['ID']>;
};


export type MutationSetMemberShipPlanMemberPriceArgs = {
  memberPriceId: Scalars['ID'];
  membershipPlanId: Scalars['ID'];
};


export type MutationReturnGiftCardOrderArgs = {
  input: ReturnTheGiftCard;
};


export type MutationUpsertPaymentRewardActivityArgs = {
  input: PaymentRewardActivityInput;
};


export type MutationSoftDeletePaymentRewardActivityArgs = {
  id: Scalars['ID'];
};


export type MutationFailurePaymentRewardActivityArgs = {
  id: Scalars['ID'];
};


export type MutationPaymentRewardArgs = {
  orderId: Scalars['ID'];
};


export type MutationUpsertShareSettingArgs = {
  inputs: Array<ShareSettingInput>;
};


export type MutationUpsertPersonalCenterArgs = {
  input: PersonalCenterInput;
};


export type MutationUpsertShoppingCreditsConfigArgs = {
  input: ShoppingCreditsConfigInput;
};


export type MutationUpsertShoppingCreditsClaimActivityArgs = {
  input: ShoppingCreditsClaimActivityInput;
};


export type MutationFailureShoppingCreditsClaimActivityArgs = {
  shoppingCreditsClaimActivityId: Scalars['ID'];
};


export type MutationSoftDeleteShoppingCreditsClaimActivityArgs = {
  shoppingCreditsClaimActivityId: Scalars['ID'];
};


export type MutationUpsertActivityCountdownArgs = {
  input: ActivityCountdownInput;
};


export type MutationDeleteActivityCountdownArgs = {
  activityCountdownId: Scalars['ID'];
};


export type MutationSetDistributorBindingRemarkArgs = {
  customerId: Scalars['ID'];
  remark: Scalars['String'];
};


export type MutationAddDistributorShareProductArgs = {
  productId: Scalars['ID'];
};


export type MutationReceivePaymentRewardCouponArgs = {
  orderPaymentRewardCouponId: Scalars['ID'];
};


export type MutationReceiveCouponBundleArgs = {
  id: Scalars['ID'];
};


export type MutationReceiveFirstCustomerBenefitArgs = {
  id: Scalars['ID'];
};


export type MutationUpsertSubscriptionPlanArgs = {
  input?: Maybe<UpsertSubscriptionPlanInput>;
  productId: Scalars['String'];
};


export type MutationDeleteSubscriptionPlanArgs = {
  productId: Scalars['String'];
};


export type MutationUpdateSubscriptionAddressArgs = {
  input?: Maybe<CreateAddressInput>;
  subscriptionId: Scalars['String'];
};


export type MutationDeleteSubscriptionArgs = {
  subscriptionId: Scalars['String'];
};


export type MutationUpdateSubscriptionFrequencyArgs = {
  frequency?: Maybe<Scalars['Int']>;
  subscriptionId: Scalars['String'];
};


export type MutationUpdateSubscriptionFirstShippingDateArgs = {
  firstShippingDate?: Maybe<Scalars['DateTime']>;
  subscriptionId: Scalars['String'];
};


export type MutationUnsubscribeArgs = {
  input?: Maybe<SubscritionCancelInput>;
};


export type MutationCreateOrderBySubscriptionArgs = {
  subscriptionId: Scalars['String'];
};


export type MutationCreateOrderAfterCreatingCtxArgs = {
  subscriptionId: Scalars['String'];
};


export type MutationUpdateSubscriptionPeriodSkipValidationArgs = {
  subscriptionId: Scalars['String'];
};


export type MutationAccomplishRefundOrderArgs = {
  refundId: Scalars['ID'];
  state: RefundState;
};


export type MutationCreateRefundArgs = {
  refundOrderCustom?: Maybe<RefundOrderCustom>;
};


export type MutationCancelRefundArgs = {
  refundId: Scalars['ID'];
};


export type MutationUpdateRefundArgs = {
  refundId: Scalars['ID'];
  refundOrderCustom: RefundOrderCustom;
};


export type MutationDeleteOrderArgs = {
  orderId: Scalars['ID'];
};


export type MutationUpdateOrderShippingAddressArgs = {
  orderId: Scalars['ID'];
  input: CreateAddressInput;
};


export type MutationSetShippingAddressArgs = {
  orderId: Scalars['ID'];
  input: CreateAddressInput;
};


export type MutationSetShippingMethodArgs = {
  orderId: Scalars['ID'];
  shippingMethodId: Scalars['ID'];
};


export type MutationConfirmReceiptOfGoodsArgs = {
  orderId: Scalars['ID'];
};


export type MutationAddItemToOrderByOrderTypeArgs = {
  productVariantId: Scalars['ID'];
  quantity: Scalars['Int'];
  type: OrderPurchaseType;
  buyType?: Maybe<OrderBuyType>;
};


export type MutationAdjustOrderLineByOrderTypeArgs = {
  orderLineId: Scalars['ID'];
  quantity: Scalars['Int'];
  type: OrderPurchaseType;
};


export type MutationRemoveOrderLineByOrderTypeArgs = {
  orderLineIds: Array<Scalars['ID']>;
  type: OrderPurchaseType;
};


export type MutationRemoveAllOrderLinesByOrderTypeArgs = {
  type: OrderPurchaseType;
};


export type MutationShoppingCartSettlementArgs = {
  input: Array<SettlementProduct>;
};


export type MutationCancelledOrderArgs = {
  orderId: Scalars['ID'];
};


export type MutationUpdateShoppingCartSkuArgs = {
  orderLineId: Scalars['ID'];
  productVariantId: Scalars['ID'];
  quantity: Scalars['Int'];
};


export type MutationAddSurchargeToOrderArgs = {
  orderId: Scalars['ID'];
  surcharge: Scalars['Int'];
};


export type MutationNewSetOrderCustomFieldsArgs = {
  input: UpdateOrderInput;
  orderId: Scalars['ID'];
};


export type MutationNoLogisticsUpdateOrderArgs = {
  orderId: Scalars['ID'];
};


export type MutationSetShippingMethodByOrderTypeArgs = {
  type: OrderPurchaseType;
  shippingMethodId: Scalars['ID'];
};


export type MutationAddPaymentToOrderByOrderTypeArgs = {
  type: OrderPurchaseType;
  input: PaymentInputByOrderType;
};


export type MutationCreateSubscriptionReviewArgs = {
  input: CreateSubscriptionReviewInput;
};


export type MutationLoginExternalArgs = {
  args?: Maybe<ExternalLoginMutationArgs>;
};


export type MutationGetWechatPhoneArgs = {
  args: GainPhone;
};


export type MutationRefundByOrderIdArgs = {
  orderInfo?: Maybe<OrderInfo>;
};


export type MutationCreateWxChatPaymentIntentArgs = {
  paymentType?: Maybe<WeChatPaymentType>;
  orderId?: Maybe<Scalars['ID']>;
};


export type MutationUpsertWeChatConfigArgs = {
  input: WeChatConfigInput;
  weChatConfigId?: Maybe<Scalars['ID']>;
};


export type MutationPhoneLoginArgs = {
  args: PhoneLoginArgs;
  h5Type?: Maybe<H5Type>;
};


export type MutationSendCodeArgs = {
  phone: Scalars['String'];
};


export type MutationGetJsConfigArgs = {
  url: Scalars['String'];
};


export type MutationPurchaseMembershipArgs = {
  membershipPlanId: Scalars['ID'];
  paymentType?: Maybe<WeChatPaymentType>;
};


export type MutationPurchaseGiftCardArgs = {
  giftCardId: Scalars['ID'];
  paymentType?: Maybe<WeChatPaymentType>;
};


export type MutationClickAdvertisingSaveArgs = {
  input: AdvertisingAttributionInput;
};


export type MutationReportingAdvertisingAttributionArgs = {
  orderId: Scalars['ID'];
};


export type MutationSetWechatMsgJumpPathArgs = {
  path: Scalars['String'];
};


export type MutationUserDeviceArgs = {
  openIdKey: Scalars['String'];
  deviceInfo?: Maybe<Scalars['JSON']>;
};


export type MutationUploadByStreamArgs = {
  input: FileInput;
};


export type MutationSettingCompActivityArgs = {
  input: CompActivityInput;
};


export type MutationDrawArgs = {
  compRecordId: Scalars['String'];
};


export type MutationPickArgs = {
  compRecordId: Scalars['String'];
  input: CreateAddressInput;
};


export type MutationShipArgs = {
  compRecordId: Scalars['String'];
  shippingInfoInput: ShippingInfoInput;
};


export type MutationCreateChannelAddressArgs = {
  input: CreateAddressInput;
};


export type MutationUpdateChannelAddressArgs = {
  input: UpdateAddressInput;
};


export type MutationDeleteChannelAddressArgs = {
  addressId: Scalars['ID'];
};


export type MutationCreateAfterSaleArgs = {
  input: AfterSaleInput;
};


export type MutationUpdateAfterSaleArgs = {
  input: AfterSaleInput;
};


export type MutationAfterSaleAuditArgs = {
  input: AfterSaleAuditInput;
};


export type MutationFillOutReturnLogisticsArgs = {
  input: ReturnLogisticsInput;
};


export type MutationUpdateReturnLogisticsArgs = {
  input: ReturnLogisticsInput;
};


export type MutationCancelAfterSaleArgs = {
  afterSaleId: Scalars['ID'];
};


export type MutationUpdateOrderPriceArgs = {
  orderId: Scalars['ID'];
  price: Scalars['Int'];
};


export type MutationAfterSaleCustomerReceiptArgs = {
  afterSaleId: Scalars['ID'];
};


export type MutationCreateMembershipPlanArgs = {
  input: MembershipPlanInput;
};


export type MutationUpdateMembershipPlanArgs = {
  input: MembershipPlanInput;
};


export type MutationDeleteMembershipPlanArgs = {
  membershipPlanId: Scalars['ID'];
};


export type MutationSwitchingStateArgs = {
  input?: Maybe<MembershipPlanSwitchingStateInput>;
};


export type MutationMembershipCardActivationArgs = {
  input: ActivateMembershipCardInput;
};


export type MutationReturnMembershipCardArgs = {
  input: ReturnTheCard;
};


export type MutationExtendMembershipTimeArgs = {
  memberId: Scalars['ID'];
  numberOfDays: Scalars['Int'];
};


export type MutationReduceMembershipTimeArgs = {
  memberId: Scalars['ID'];
  numberOfDays: Scalars['Int'];
};


export type MutationShowHideMembershipPlanArgs = {
  membershipPlanId: Scalars['ID'];
  isShow: Scalars['Boolean'];
};


export type MutationAddCustomerToMemberArgs = {
  membershipId: Scalars['ID'];
};


export type MutationUpsertGiftCardArgs = {
  input: GiftCardInput;
};


export type MutationSoftDeleteGiftCardArgs = {
  giftCardId: Scalars['ID'];
};


export type MutationCreateGiftCardOrderArgs = {
  giftCardId: Scalars['ID'];
};


export type MutationAddPaymentToGiftCardOrderArgs = {
  giftCardOrderId: Scalars['ID'];
  input: GiftCardPaymentInput;
};


export type MutationSwitchingGiftCardStateArgs = {
  giftCardId: Scalars['ID'];
  state: GiftCardState;
};


export type MutationUpsertForumTagArgs = {
  input: ForumTagInput;
};


export type MutationDeleteForumTagArgs = {
  forumTagId: Scalars['ID'];
};


export type MutationUpdateForumTagStatusArgs = {
  forumTagId: Scalars['ID'];
  status: ForumTagStatus;
};


export type MutationUpsertForumActivityArgs = {
  input: ForumActivityInput;
};


export type MutationDeleteForumActivityArgs = {
  forumActivityId: Scalars['ID'];
};


export type MutationUpdateForumActivityStatusArgs = {
  forumActivityId: Scalars['ID'];
  status: ForumActivityStatus;
};


export type MutationUpsertForumCustomerArgs = {
  input: ForumCustomerInput;
};


export type MutationDeleteForumCustomerArgs = {
  forumCustomerId: Scalars['ID'];
};


export type MutationUpsertForumPostArgs = {
  input: ForumPostInput;
};


export type MutationDeleteForumPostArgs = {
  forumPostId: Scalars['ID'];
};


export type MutationAuditForumPostArgs = {
  input: ForumPostAuditInput;
};


export type MutationForumDownPostArgs = {
  pid: Scalars['String'];
};


export type MutationForumUpPostArgs = {
  pid: Scalars['String'];
};


export type MutationForumReviewPostArgs = {
  reviewId?: Maybe<Scalars['ID']>;
  postId?: Maybe<Scalars['ID']>;
  content: Scalars['String'];
  images?: Maybe<Array<Maybe<Scalars['String']>>>;
};


export type MutationReviewForumAuditArgs = {
  input: ForumReviewAuditInput;
};


export type MutationDeleteForumReviewArgs = {
  forumReviewId: Scalars['ID'];
};


export type MutationMarkNotificationsAsReadArgs = {
  notificationType?: Maybe<NotificationType>;
};


export type MutationForumPostShareArgs = {
  id: Scalars['ID'];
};


export type MutationForumPostPinArgs = {
  forumPostId: Scalars['ID'];
  isPinned: Scalars['Boolean'];
};


export type MutationUpsertWangDianTongConfigArgs = {
  input: WangDianTongConfigInput;
};

export type MyForumPostCount = {
  __typename?: 'MyForumPostCount';
  topicCount?: Maybe<Scalars['Int']>;
  postCount?: Maybe<Scalars['Int']>;
  reputation?: Maybe<Scalars['Int']>;
};

export type NativeAuthInput = {
  username: Scalars['String'];
  password: Scalars['String'];
};

/** Returned when attempting an operation that relies on the NativeAuthStrategy, if that strategy is not configured. */
export type NativeAuthStrategyError = ErrorResult & {
  __typename?: 'NativeAuthStrategyError';
  errorCode: ErrorCode;
  message: Scalars['String'];
};

export type NativeAuthenticationResult = CurrentUser | InvalidCredentialsError | NotVerifiedError | NativeAuthStrategyError;

/** Returned when attempting to set a negative OrderLine quantity. */
export type NegativeQuantityError = ErrorResult & {
  __typename?: 'NegativeQuantityError';
  errorCode: ErrorCode;
  message: Scalars['String'];
};

/**
 * Returned when invoking a mutation which depends on there being an active Order on the
 * current session.
 */
export type NoActiveOrderError = ErrorResult & {
  __typename?: 'NoActiveOrderError';
  errorCode: ErrorCode;
  message: Scalars['String'];
};

export type Node = {
  id: Scalars['ID'];
};

export type NonConvertedGiftLevel = {
  __typename?: 'NonConvertedGiftLevel';
  convertedGiftId?: Maybe<Scalars['ID']>;
  level?: Maybe<Scalars['Int']>;
};

export type NonConvertedGiftLevelInput = {
  convertedGiftId?: Maybe<Scalars['ID']>;
  assistGiftType?: Maybe<AssistGiftType>;
  targetId?: Maybe<Scalars['ID']>;
  level?: Maybe<Scalars['Int']>;
  giftImage?: Maybe<Scalars['String']>;
};

/**
 * Returned if `authOptions.requireVerification` is set to `true` (which is the default)
 * and an unverified user attempts to authenticate.
 */
export type NotVerifiedError = ErrorResult & {
  __typename?: 'NotVerifiedError';
  errorCode: ErrorCode;
  message: Scalars['String'];
};

export type Note = {
  __typename?: 'Note';
  code?: Maybe<Scalars['String']>;
  message?: Maybe<Scalars['String']>;
};

export type NoticeConfig = {
  __typename?: 'NoticeConfig';
  noticeType?: Maybe<NoticeType>;
  noticeContent?: Maybe<NoticeContent>;
};

export type NoticeConfigInput = {
  noticeType: NoticeType;
  noticeContent: NoticeContentInput;
};

export type NoticeContent = {
  __typename?: 'NoticeContent';
  activityName?: Maybe<Scalars['String']>;
  activityContent?: Maybe<Scalars['String']>;
  warmPrompt?: Maybe<Scalars['String']>;
  jumpType?: Maybe<ShareType>;
  jumpValue?: Maybe<Scalars['String']>;
};

export type NoticeContentInput = {
  activityName: Scalars['String'];
  activityContent: Scalars['String'];
  warmPrompt: Scalars['String'];
  jumpType: ShareType;
  jumpValue: Scalars['String'];
};

export enum NoticeType {
  Sms = 'sms',
  SmallProgram = 'smallProgram'
}

export enum NotificationType {
  Upvote = 'upvote',
  Reply = 'reply',
  Mention = 'mention',
  All = 'all'
}

/** Operators for filtering on a list of Number fields */
export type NumberListOperators = {
  inList: Scalars['Float'];
};

/** Operators for filtering on a Int or Float field */
export type NumberOperators = {
  eq?: Maybe<Scalars['Float']>;
  lt?: Maybe<Scalars['Float']>;
  lte?: Maybe<Scalars['Float']>;
  gt?: Maybe<Scalars['Float']>;
  gte?: Maybe<Scalars['Float']>;
  between?: Maybe<NumberRange>;
  isNull?: Maybe<Scalars['Boolean']>;
};

export type NumberRange = {
  start: Scalars['Float'];
  end: Scalars['Float'];
};

export type OfficialAccount = {
  __typename?: 'OfficialAccount';
  officialAppId?: Maybe<Scalars['String']>;
  officialAppSecret?: Maybe<Scalars['String']>;
};

export enum OpenBoxMode {
  AssistOnce = 'assistOnce',
  AssistMultiple = 'assistMultiple'
}

export type OperationPlan = Node & {
  __typename?: 'OperationPlan';
  id: Scalars['ID'];
  createdAt?: Maybe<Scalars['DateTime']>;
  updatedAt?: Maybe<Scalars['DateTime']>;
  name?: Maybe<Scalars['String']>;
  groupCustomer?: Maybe<GroupCustomer>;
  state?: Maybe<OperationPlanState>;
  planType?: Maybe<OperationPlanType>;
  planTime?: Maybe<Scalars['DateTime']>;
  coupon?: Maybe<Coupon>;
  noticeConfig?: Maybe<NoticeConfig>;
  operationPlanCustomers?: Maybe<Array<Maybe<OperationPlanCustomer>>>;
  numberOfSuccessfulRolls?: Maybe<Scalars['Int']>;
  numberOfFailedRolls?: Maybe<Scalars['Int']>;
  numberOfSuccessfulNotice?: Maybe<Scalars['Int']>;
  numberOfFailedNotice?: Maybe<Scalars['Int']>;
};

export type OperationPlanCustomer = Node & {
  __typename?: 'OperationPlanCustomer';
  id: Scalars['ID'];
  createdAt?: Maybe<Scalars['DateTime']>;
  updatedAt?: Maybe<Scalars['DateTime']>;
  operationPlan?: Maybe<OperationPlan>;
  customer?: Maybe<Customer>;
  isGrant?: Maybe<Scalars['Boolean']>;
  grantState?: Maybe<Scalars['Boolean']>;
  isNotice?: Maybe<Scalars['Boolean']>;
  noticeState?: Maybe<Scalars['Boolean']>;
  coupon?: Maybe<Coupon>;
};

export type OperationPlanCustomerFilterParameter = {
  id?: Maybe<IdOperators>;
  createdAt?: Maybe<DateOperators>;
  updatedAt?: Maybe<DateOperators>;
  isGrant?: Maybe<BooleanOperators>;
  grantState?: Maybe<BooleanOperators>;
  isNotice?: Maybe<BooleanOperators>;
  noticeState?: Maybe<BooleanOperators>;
};

export type OperationPlanCustomerList = PaginatedList & {
  __typename?: 'OperationPlanCustomerList';
  items: Array<OperationPlanCustomer>;
  totalItems: Scalars['Int'];
};

export type OperationPlanCustomerListOptions = {
  /** Skips the first n results, for use in pagination */
  skip?: Maybe<Scalars['Int']>;
  /** Takes n results, for use in pagination */
  take?: Maybe<Scalars['Int']>;
  /** Specifies which properties to sort the results by */
  sort?: Maybe<OperationPlanCustomerSortParameter>;
  /** Allows the results to be filtered */
  filter?: Maybe<OperationPlanCustomerFilterParameter>;
  /** Specifies whether multiple "filter" arguments should be combines with a logical AND or OR operation. Defaults to AND. */
  filterOperator?: Maybe<LogicalOperator>;
};

export type OperationPlanCustomerSortParameter = {
  id?: Maybe<SortOrder>;
  createdAt?: Maybe<SortOrder>;
  updatedAt?: Maybe<SortOrder>;
};

export type OperationPlanFilterParameter = {
  id?: Maybe<IdOperators>;
  createdAt?: Maybe<DateOperators>;
  updatedAt?: Maybe<DateOperators>;
  name?: Maybe<StringOperators>;
  state?: Maybe<StringOperators>;
  planType?: Maybe<StringOperators>;
  planTime?: Maybe<DateOperators>;
  numberOfSuccessfulRolls?: Maybe<NumberOperators>;
  numberOfFailedRolls?: Maybe<NumberOperators>;
  numberOfSuccessfulNotice?: Maybe<NumberOperators>;
  numberOfFailedNotice?: Maybe<NumberOperators>;
};

export type OperationPlanInput = {
  id?: Maybe<Scalars['ID']>;
  name: Scalars['String'];
  groupCustomerId: Scalars['ID'];
  planType: OperationPlanType;
  planTime?: Maybe<Scalars['DateTime']>;
  couponId: Scalars['ID'];
  noticeConfig?: Maybe<NoticeConfigInput>;
};

export type OperationPlanList = PaginatedList & {
  __typename?: 'OperationPlanList';
  items: Array<OperationPlan>;
  totalItems: Scalars['Int'];
};

export type OperationPlanListOptions = {
  /** Skips the first n results, for use in pagination */
  skip?: Maybe<Scalars['Int']>;
  /** Takes n results, for use in pagination */
  take?: Maybe<Scalars['Int']>;
  /** Specifies which properties to sort the results by */
  sort?: Maybe<OperationPlanSortParameter>;
  /** Allows the results to be filtered */
  filter?: Maybe<OperationPlanFilterParameter>;
  /** Specifies whether multiple "filter" arguments should be combines with a logical AND or OR operation. Defaults to AND. */
  filterOperator?: Maybe<LogicalOperator>;
};

export type OperationPlanSortParameter = {
  id?: Maybe<SortOrder>;
  createdAt?: Maybe<SortOrder>;
  updatedAt?: Maybe<SortOrder>;
  name?: Maybe<SortOrder>;
  planTime?: Maybe<SortOrder>;
  numberOfSuccessfulRolls?: Maybe<SortOrder>;
  numberOfFailedRolls?: Maybe<SortOrder>;
  numberOfSuccessfulNotice?: Maybe<SortOrder>;
  numberOfFailedNotice?: Maybe<SortOrder>;
};

export enum OperationPlanState {
  NotStarted = 'notStarted',
  InProgress = 'inProgress',
  Suspended = 'suspended',
  HaveEnded = 'haveEnded'
}

export enum OperationPlanType {
  Immediate = 'immediate',
  Scheduled = 'scheduled'
}

export type OperationResponse = {
  __typename?: 'OperationResponse';
  code?: Maybe<Scalars['String']>;
  message?: Maybe<Scalars['String']>;
};

export enum OperationSubscritionType {
  UpdateAddress = 'updateAddress',
  UpdateFrequency = 'updateFrequency',
  UpdateFirstShippingDate = 'updateFirstShippingDate',
  Unsubscribe = 'unsubscribe'
}

export type OperationSubscritionValue = {
  __typename?: 'OperationSubscritionValue';
  cancelExplain?: Maybe<Scalars['String']>;
  cancelCause?: Maybe<Scalars['String']>;
  cancelMoney?: Maybe<Scalars['Float']>;
  cancelImg?: Maybe<Array<Maybe<Scalars['String']>>>;
  modification?: Maybe<Scalars['JSON']>;
  original?: Maybe<Scalars['JSON']>;
};

export enum OperatorType {
  User = 'user',
  Channel = 'channel',
  Platform = 'platform'
}

export type Order = Node & {
  __typename?: 'Order';
  id: Scalars['ID'];
  createdAt: Scalars['DateTime'];
  updatedAt: Scalars['DateTime'];
  type: OrderType;
  /**
   * The date & time that the Order was placed, i.e. the Customer
   * completed the checkout and the Order is no longer "active"
   */
  orderPlacedAt?: Maybe<Scalars['DateTime']>;
  /** A unique code for the Order */
  code: Scalars['String'];
  state: Scalars['String'];
  /** An order is active as long as the payment process has not been completed */
  active: Scalars['Boolean'];
  customer?: Maybe<Customer>;
  shippingAddress?: Maybe<OrderAddress>;
  billingAddress?: Maybe<OrderAddress>;
  lines: Array<OrderLine>;
  /**
   * Surcharges are arbitrary modifications to the Order total which are neither
   * ProductVariants nor discounts resulting from applied Promotions. For example,
   * one-off discounts based on customer interaction, or surcharges based on payment
   * methods.
   */
  surcharges: Array<Surcharge>;
  discounts: Array<Discount>;
  /** An array of all coupon codes applied to the Order */
  couponCodes: Array<Scalars['String']>;
  /** Promotions applied to the order. Only gets populated after the payment process has completed. */
  promotions: Array<Promotion>;
  payments?: Maybe<Array<Payment>>;
  fulfillments?: Maybe<Array<Fulfillment>>;
  totalQuantity: Scalars['Int'];
  /**
   * The subTotal is the total of all OrderLines in the Order. This figure also includes any Order-level
   * discounts which have been prorated (proportionally distributed) amongst the items of each OrderLine.
   * To get a total of all OrderLines which does not account for prorated discounts, use the
   * sum of `OrderLine.discountedLinePrice` values.
   */
  subTotal: Scalars['Money'];
  /** Same as subTotal, but inclusive of tax */
  subTotalWithTax: Scalars['Money'];
  currencyCode: CurrencyCode;
  shippingLines: Array<ShippingLine>;
  shipping: Scalars['Money'];
  shippingWithTax: Scalars['Money'];
  /** Equal to subTotal plus shipping */
  total: Scalars['Money'];
  /** The final payable amount. Equal to subTotalWithTax plus shippingWithTax */
  totalWithTax: Scalars['Money'];
  /** A summary of the taxes being applied to this Order */
  taxSummary: Array<OrderTaxSummary>;
  history: HistoryEntryList;
  totalPrice?: Maybe<Scalars['Int']>;
  subTotalPrice?: Maybe<Scalars['Int']>;
  isPaymentRewardActivity?: Maybe<Scalars['Boolean']>;
  giftsProductVariant?: Maybe<Array<Maybe<ProductVariant>>>;
  orderBuyUserCouponId?: Maybe<Scalars['ID']>;
  customFields?: Maybe<OrderCustomFields>;
};


export type OrderHistoryArgs = {
  options?: Maybe<HistoryEntryListOptions>;
};

export type OrderAddress = {
  __typename?: 'OrderAddress';
  fullName?: Maybe<Scalars['String']>;
  company?: Maybe<Scalars['String']>;
  streetLine1?: Maybe<Scalars['String']>;
  streetLine2?: Maybe<Scalars['String']>;
  city?: Maybe<Scalars['String']>;
  province?: Maybe<Scalars['String']>;
  postalCode?: Maybe<Scalars['String']>;
  country?: Maybe<Scalars['String']>;
  countryCode?: Maybe<Scalars['String']>;
  phoneNumber?: Maybe<Scalars['String']>;
  customFields?: Maybe<AddressCustomFields>;
};

export enum OrderBuyType {
  Ordinary = 'ordinary',
  PointsExchange = 'pointsExchange'
}

export type OrderCustomFields = {
  __typename?: 'OrderCustomFields';
  remark?: Maybe<Scalars['String']>;
  orderPromotionResult?: Maybe<OrderPromotionResult>;
  merchantRemarks?: Maybe<Scalars['String']>;
  buyType?: Maybe<Scalars['String']>;
  isHidden?: Maybe<Scalars['Boolean']>;
  isReturnPoints?: Maybe<Scalars['Boolean']>;
  closeReasonType?: Maybe<Scalars['String']>;
  receiverName?: Maybe<Scalars['String']>;
  receiverPhone?: Maybe<Scalars['String']>;
  shippingFeeType?: Maybe<Scalars['String']>;
  subscriptionPlan?: Maybe<SubscriptionPlan>;
  subscription?: Maybe<Subscription>;
  currentPeriods?: Maybe<Scalars['Int']>;
  periods?: Maybe<Scalars['Int']>;
  deliveryInterval?: Maybe<Scalars['Int']>;
  firstShippingDate?: Maybe<Scalars['DateTime']>;
  logistics?: Maybe<Logistics>;
  timeoutPeriodToBePaid?: Maybe<Scalars['DateTime']>;
  timeoutPeriodToBeReceived?: Maybe<Scalars['DateTime']>;
  isAvailableAfterSale?: Maybe<Scalars['Boolean']>;
  confirmReceiptTime?: Maybe<Scalars['DateTime']>;
  signingTime?: Maybe<Scalars['DateTime']>;
  timeToPlaceOrder?: Maybe<Scalars['DateTime']>;
  originSubTotal?: Maybe<Scalars['Int']>;
  newOrderCode?: Maybe<Scalars['String']>;
};

export type OrderDiscounts = {
  __typename?: 'OrderDiscounts';
  couponDiscounts?: Maybe<Array<Maybe<DiscountsCoupon>>>;
  memberDiscounts?: Maybe<Array<Maybe<DiscountsMember>>>;
  purchasePremiumDiscounts?: Maybe<Array<Maybe<DiscountsPurchasePremium>>>;
  discountActivities?: Maybe<Array<Maybe<DiscountsActivities>>>;
  fullDiscountPresent?: Maybe<Array<Maybe<DiscountsFullPresent>>>;
};

export type OrderFilterParameter = {
  id?: Maybe<IdOperators>;
  createdAt?: Maybe<DateOperators>;
  updatedAt?: Maybe<DateOperators>;
  type?: Maybe<StringOperators>;
  orderPlacedAt?: Maybe<DateOperators>;
  code?: Maybe<StringOperators>;
  state?: Maybe<StringOperators>;
  active?: Maybe<BooleanOperators>;
  totalQuantity?: Maybe<NumberOperators>;
  subTotal?: Maybe<NumberOperators>;
  subTotalWithTax?: Maybe<NumberOperators>;
  currencyCode?: Maybe<StringOperators>;
  shipping?: Maybe<NumberOperators>;
  shippingWithTax?: Maybe<NumberOperators>;
  total?: Maybe<NumberOperators>;
  totalWithTax?: Maybe<NumberOperators>;
  totalPrice?: Maybe<NumberOperators>;
  subTotalPrice?: Maybe<NumberOperators>;
  isPaymentRewardActivity?: Maybe<BooleanOperators>;
  orderBuyUserCouponId?: Maybe<IdOperators>;
  remark?: Maybe<StringOperators>;
  merchantRemarks?: Maybe<StringOperators>;
  buyType?: Maybe<StringOperators>;
  isHidden?: Maybe<BooleanOperators>;
  isReturnPoints?: Maybe<BooleanOperators>;
  closeReasonType?: Maybe<StringOperators>;
  receiverName?: Maybe<StringOperators>;
  receiverPhone?: Maybe<StringOperators>;
  shippingFeeType?: Maybe<StringOperators>;
  currentPeriods?: Maybe<NumberOperators>;
  periods?: Maybe<NumberOperators>;
  deliveryInterval?: Maybe<NumberOperators>;
  firstShippingDate?: Maybe<DateOperators>;
  timeoutPeriodToBePaid?: Maybe<DateOperators>;
  timeoutPeriodToBeReceived?: Maybe<DateOperators>;
  isAvailableAfterSale?: Maybe<BooleanOperators>;
  confirmReceiptTime?: Maybe<DateOperators>;
  signingTime?: Maybe<DateOperators>;
  timeToPlaceOrder?: Maybe<DateOperators>;
  originSubTotal?: Maybe<NumberOperators>;
  newOrderCode?: Maybe<StringOperators>;
};

export enum OrderGenre {
  Normal = 'normal',
  Subscription = 'subscription'
}

export type OrderInfo = {
  orderId?: Maybe<Scalars['String']>;
};

/** Returned when the maximum order size limit has been reached. */
export type OrderLimitError = ErrorResult & {
  __typename?: 'OrderLimitError';
  errorCode: ErrorCode;
  message: Scalars['String'];
  maxItems: Scalars['Int'];
};

export type OrderLine = Node & {
  __typename?: 'OrderLine';
  id: Scalars['ID'];
  createdAt: Scalars['DateTime'];
  updatedAt: Scalars['DateTime'];
  productVariant: ProductVariant;
  featuredAsset?: Maybe<Asset>;
  /** The price of a single unit, excluding tax and discounts */
  unitPrice: Scalars['Money'];
  /** The price of a single unit, including tax but excluding discounts */
  unitPriceWithTax: Scalars['Money'];
  /** Non-zero if the unitPrice has changed since it was initially added to Order */
  unitPriceChangeSinceAdded: Scalars['Money'];
  /** Non-zero if the unitPriceWithTax has changed since it was initially added to Order */
  unitPriceWithTaxChangeSinceAdded: Scalars['Money'];
  /**
   * The price of a single unit including discounts, excluding tax.
   *
   * If Order-level discounts have been applied, this will not be the
   * actual taxable unit price (see `proratedUnitPrice`), but is generally the
   * correct price to display to customers to avoid confusion
   * about the internal handling of distributed Order-level discounts.
   */
  discountedUnitPrice: Scalars['Money'];
  /** The price of a single unit including discounts and tax */
  discountedUnitPriceWithTax: Scalars['Money'];
  /**
   * The actual unit price, taking into account both item discounts _and_ prorated (proportionally-distributed)
   * Order-level discounts. This value is the true economic value of the OrderItem, and is used in tax
   * and refund calculations.
   */
  proratedUnitPrice: Scalars['Money'];
  /** The proratedUnitPrice including tax */
  proratedUnitPriceWithTax: Scalars['Money'];
  quantity: Scalars['Int'];
  /** The quantity at the time the Order was placed */
  orderPlacedQuantity: Scalars['Int'];
  taxRate: Scalars['Float'];
  /** The total price of the line excluding tax and discounts. */
  linePrice: Scalars['Money'];
  /** The total price of the line including tax but excluding discounts. */
  linePriceWithTax: Scalars['Money'];
  /** The price of the line including discounts, excluding tax */
  discountedLinePrice: Scalars['Money'];
  /** The price of the line including discounts and tax */
  discountedLinePriceWithTax: Scalars['Money'];
  /**
   * The actual line price, taking into account both item discounts _and_ prorated (proportionally-distributed)
   * Order-level discounts. This value is the true economic value of the OrderLine, and is used in tax
   * and refund calculations.
   */
  proratedLinePrice: Scalars['Money'];
  /** The proratedLinePrice including tax */
  proratedLinePriceWithTax: Scalars['Money'];
  /** The total tax on this line */
  lineTax: Scalars['Money'];
  discounts: Array<Discount>;
  taxLines: Array<TaxLine>;
  order: Order;
  fulfillmentLines?: Maybe<Array<FulfillmentLine>>;
  merchantVoluntaryRefund?: Maybe<Scalars['Int']>;
  totalQuantity?: Maybe<Scalars['Int']>;
  blindBoxRefundableAmount?: Maybe<Scalars['Int']>;
  customFields?: Maybe<OrderLineCustomFields>;
};

export type OrderLineBlindBoxMap = {
  __typename?: 'OrderLineBlindBoxMap';
  orderLineId?: Maybe<Scalars['ID']>;
  blindBoxOrderBuyId?: Maybe<Scalars['ID']>;
  blindBoxOrderBuyPrice?: Maybe<Scalars['Int']>;
  blindBoxOpenRecordId?: Maybe<Scalars['ID']>;
  blindBoxItemId?: Maybe<Scalars['ID']>;
};

export type OrderLineCustomFields = {
  __typename?: 'OrderLineCustomFields';
  purchasePattern?: Maybe<Scalars['String']>;
  promInstanceIds?: Maybe<Array<Scalars['Int']>>;
  isReview?: Maybe<Scalars['Boolean']>;
  isAvailableAfterSale?: Maybe<Scalars['Boolean']>;
  afterSaleLine?: Maybe<AfterSaleLine>;
  afterSale?: Maybe<AfterSale>;
};

export type OrderLineCustomFieldsInput = {
  purchasePattern?: Maybe<Scalars['String']>;
  promInstanceIds?: Maybe<Array<Maybe<Scalars['Int']>>>;
  isReview?: Maybe<Scalars['Boolean']>;
  isAvailableAfterSale?: Maybe<Scalars['Boolean']>;
  afterSaleLineId?: Maybe<Scalars['ID']>;
  afterSaleId?: Maybe<Scalars['ID']>;
};

export type OrderLinePromResult = {
  __typename?: 'OrderLinePromResult';
  orderLineId?: Maybe<Scalars['ID']>;
  skuId?: Maybe<Scalars['ID']>;
  count?: Maybe<Scalars['Int']>;
  price?: Maybe<Scalars['Int']>;
  discountAmount?: Maybe<Scalars['Int']>;
  discountDetails?: Maybe<Array<Maybe<DiscountDetail>>>;
  totalPrice?: Maybe<Scalars['Int']>;
};

export type OrderLinesRefundable = {
  __typename?: 'OrderLinesRefundable';
  orderLine?: Maybe<OrderLine>;
  refundableAmount?: Maybe<Scalars['Int']>;
  isAllowAfterSale?: Maybe<Scalars['Boolean']>;
};

export type OrderList = PaginatedList & {
  __typename?: 'OrderList';
  items: Array<Order>;
  totalItems: Scalars['Int'];
};

export type OrderListOptions = {
  /** Skips the first n results, for use in pagination */
  skip?: Maybe<Scalars['Int']>;
  /** Takes n results, for use in pagination */
  take?: Maybe<Scalars['Int']>;
  /** Specifies which properties to sort the results by */
  sort?: Maybe<OrderSortParameter>;
  /** Allows the results to be filtered */
  filter?: Maybe<OrderFilterParameter>;
  /** Specifies whether multiple "filter" arguments should be combines with a logical AND or OR operation. Defaults to AND. */
  filterOperator?: Maybe<LogicalOperator>;
};

/** Returned when attempting to modify the contents of an Order that is not in the `AddingItems` state. */
export type OrderModificationError = ErrorResult & {
  __typename?: 'OrderModificationError';
  errorCode: ErrorCode;
  message: Scalars['String'];
};

export type OrderPaymentRewardCoupon = Node & {
  __typename?: 'OrderPaymentRewardCoupon';
  id: Scalars['ID'];
  createdAt?: Maybe<Scalars['DateTime']>;
  updatedAt?: Maybe<Scalars['DateTime']>;
  orderId: Scalars['ID'];
  userCouponId?: Maybe<Scalars['ID']>;
  couponId: Scalars['ID'];
  paymentRewardActivityId: Scalars['ID'];
  order?: Maybe<Order>;
  userCoupon?: Maybe<UserCoupon>;
  coupon?: Maybe<Coupon>;
  paymentRewardActivity?: Maybe<PaymentRewardActivity>;
  isClaimed?: Maybe<Scalars['Boolean']>;
  isUsed?: Maybe<Scalars['Boolean']>;
};

export type OrderPaymentRewardCouponFilterParameter = {
  id?: Maybe<IdOperators>;
  createdAt?: Maybe<DateOperators>;
  updatedAt?: Maybe<DateOperators>;
  orderId?: Maybe<IdOperators>;
  userCouponId?: Maybe<IdOperators>;
  couponId?: Maybe<IdOperators>;
  paymentRewardActivityId?: Maybe<IdOperators>;
  isClaimed?: Maybe<BooleanOperators>;
  isUsed?: Maybe<BooleanOperators>;
};

export type OrderPaymentRewardCouponList = PaginatedList & {
  __typename?: 'OrderPaymentRewardCouponList';
  items: Array<OrderPaymentRewardCoupon>;
  totalItems: Scalars['Int'];
};

export type OrderPaymentRewardCouponListOptions = {
  /** Skips the first n results, for use in pagination */
  skip?: Maybe<Scalars['Int']>;
  /** Takes n results, for use in pagination */
  take?: Maybe<Scalars['Int']>;
  /** Specifies which properties to sort the results by */
  sort?: Maybe<OrderPaymentRewardCouponSortParameter>;
  /** Allows the results to be filtered */
  filter?: Maybe<OrderPaymentRewardCouponFilterParameter>;
  /** Specifies whether multiple "filter" arguments should be combines with a logical AND or OR operation. Defaults to AND. */
  filterOperator?: Maybe<LogicalOperator>;
};

export type OrderPaymentRewardCouponSortParameter = {
  id?: Maybe<SortOrder>;
  createdAt?: Maybe<SortOrder>;
  updatedAt?: Maybe<SortOrder>;
  orderId?: Maybe<SortOrder>;
  userCouponId?: Maybe<SortOrder>;
  couponId?: Maybe<SortOrder>;
  paymentRewardActivityId?: Maybe<SortOrder>;
};

/** Returned when attempting to add a Payment to an Order that is not in the `ArrangingPayment` state. */
export type OrderPaymentStateError = ErrorResult & {
  __typename?: 'OrderPaymentStateError';
  errorCode: ErrorCode;
  message: Scalars['String'];
};

export enum OrderProductType {
  Normal = 'normal',
  Coupon = 'coupon',
  MemberCard = 'memberCard'
}

export type OrderPromotionResult = Node & {
  __typename?: 'OrderPromotionResult';
  id: Scalars['ID'];
  createdAt: Scalars['DateTime'];
  updatedAt: Scalars['DateTime'];
  order?: Maybe<Order>;
  promResult?: Maybe<PromResult>;
  isPayReward?: Maybe<Scalars['Boolean']>;
  isCartShowActuallyPaidGift?: Maybe<Scalars['Boolean']>;
  isCartShowFullDiscountPresentGift?: Maybe<Scalars['Boolean']>;
  isCartShowSelectiveGift?: Maybe<Scalars['Boolean']>;
  isMember?: Maybe<Scalars['Boolean']>;
};

export type OrderPromotionResultInput = {
  __typename?: 'OrderPromotionResultInput';
  orderId?: Maybe<Scalars['ID']>;
  promResult?: Maybe<PromResult>;
};

export enum OrderPurchaseType {
  ShoppingTrolley = 'shoppingTrolley',
  OutrightPurchase = 'outrightPurchase',
  RegularOrder = 'regularOrder'
}

export enum OrderReferralSource {
  All = 'all',
  TopicFriendCircle = 'topicFriendCircle',
  WechatGroup = 'wechatGroup'
}

export type OrderRefund = Node & {
  __typename?: 'OrderRefund';
  id: Scalars['ID'];
  channels?: Maybe<Array<Channel>>;
  createdAt?: Maybe<Scalars['DateTime']>;
  updatedAt?: Maybe<Scalars['DateTime']>;
  state?: Maybe<RefundState>;
  images?: Maybe<Array<Maybe<Scalars['String']>>>;
  cancelType?: Maybe<Scalars['String']>;
  cancelExplain?: Maybe<Scalars['String']>;
  cancelCause?: Maybe<Scalars['String']>;
  refundAmount?: Maybe<Scalars['Float']>;
  order?: Maybe<Order>;
};

export type OrderRefundFilterParameter = {
  id?: Maybe<IdOperators>;
  createdAt?: Maybe<DateOperators>;
  updatedAt?: Maybe<DateOperators>;
  state?: Maybe<StringOperators>;
  cancelType?: Maybe<StringOperators>;
  cancelExplain?: Maybe<StringOperators>;
  cancelCause?: Maybe<StringOperators>;
  refundAmount?: Maybe<NumberOperators>;
};

export type OrderRefundList = PaginatedList & {
  __typename?: 'OrderRefundList';
  items: Array<OrderRefund>;
  totalItems: Scalars['Int'];
};

export type OrderRefundListOptions = {
  /** Skips the first n results, for use in pagination */
  skip?: Maybe<Scalars['Int']>;
  /** Takes n results, for use in pagination */
  take?: Maybe<Scalars['Int']>;
  /** Specifies which properties to sort the results by */
  sort?: Maybe<OrderRefundSortParameter>;
  /** Allows the results to be filtered */
  filter?: Maybe<OrderRefundFilterParameter>;
  /** Specifies whether multiple "filter" arguments should be combines with a logical AND or OR operation. Defaults to AND. */
  filterOperator?: Maybe<LogicalOperator>;
};

export type OrderRefundSortParameter = {
  id?: Maybe<SortOrder>;
  createdAt?: Maybe<SortOrder>;
  updatedAt?: Maybe<SortOrder>;
  cancelType?: Maybe<SortOrder>;
  cancelExplain?: Maybe<SortOrder>;
  cancelCause?: Maybe<SortOrder>;
  refundAmount?: Maybe<SortOrder>;
};

export type OrderShoppingCredits = {
  __typename?: 'OrderShoppingCredits';
  orderLineId?: Maybe<Scalars['ID']>;
  shoppingCreditsClaimAmount?: Maybe<Scalars['Int']>;
  shoppingCreditsDeductionAmount?: Maybe<Scalars['Int']>;
};

export type OrderSortParameter = {
  id?: Maybe<SortOrder>;
  createdAt?: Maybe<SortOrder>;
  updatedAt?: Maybe<SortOrder>;
  orderPlacedAt?: Maybe<SortOrder>;
  code?: Maybe<SortOrder>;
  state?: Maybe<SortOrder>;
  totalQuantity?: Maybe<SortOrder>;
  subTotal?: Maybe<SortOrder>;
  subTotalWithTax?: Maybe<SortOrder>;
  shipping?: Maybe<SortOrder>;
  shippingWithTax?: Maybe<SortOrder>;
  total?: Maybe<SortOrder>;
  totalWithTax?: Maybe<SortOrder>;
  totalPrice?: Maybe<SortOrder>;
  subTotalPrice?: Maybe<SortOrder>;
  orderBuyUserCouponId?: Maybe<SortOrder>;
  remark?: Maybe<SortOrder>;
  orderPromotionResult?: Maybe<SortOrder>;
  merchantRemarks?: Maybe<SortOrder>;
  buyType?: Maybe<SortOrder>;
  isHidden?: Maybe<SortOrder>;
  isReturnPoints?: Maybe<SortOrder>;
  closeReasonType?: Maybe<SortOrder>;
  receiverName?: Maybe<SortOrder>;
  receiverPhone?: Maybe<SortOrder>;
  shippingFeeType?: Maybe<SortOrder>;
  subscriptionPlan?: Maybe<SortOrder>;
  subscription?: Maybe<SortOrder>;
  currentPeriods?: Maybe<SortOrder>;
  periods?: Maybe<SortOrder>;
  deliveryInterval?: Maybe<SortOrder>;
  firstShippingDate?: Maybe<SortOrder>;
  logistics?: Maybe<SortOrder>;
  timeoutPeriodToBePaid?: Maybe<SortOrder>;
  timeoutPeriodToBeReceived?: Maybe<SortOrder>;
  isAvailableAfterSale?: Maybe<SortOrder>;
  confirmReceiptTime?: Maybe<SortOrder>;
  signingTime?: Maybe<SortOrder>;
  timeToPlaceOrder?: Maybe<SortOrder>;
  originSubTotal?: Maybe<SortOrder>;
  newOrderCode?: Maybe<SortOrder>;
};

/** Returned if there is an error in transitioning the Order state */
export type OrderStateTransitionError = ErrorResult & {
  __typename?: 'OrderStateTransitionError';
  errorCode: ErrorCode;
  message: Scalars['String'];
  transitionError: Scalars['String'];
  fromState: Scalars['String'];
  toState: Scalars['String'];
};

/**
 * A summary of the taxes being applied to this order, grouped
 * by taxRate.
 */
export type OrderTaxSummary = {
  __typename?: 'OrderTaxSummary';
  /** A description of this tax */
  description: Scalars['String'];
  /** The taxRate as a percentage */
  taxRate: Scalars['Float'];
  /** The total net price of OrderLines to which this taxRate applies */
  taxBase: Scalars['Money'];
  /** The total tax being applied to the Order at this taxRate */
  taxTotal: Scalars['Money'];
};

export type OrderTracking = Node & {
  __typename?: 'OrderTracking';
  id: Scalars['ID'];
  createdAt?: Maybe<Scalars['DateTime']>;
  updatedAt?: Maybe<Scalars['DateTime']>;
  customerId: Scalars['ID'];
  orderId: Scalars['ID'];
  orderAmount: Scalars['String'];
  pageType: TrackingPageType;
  pageId?: Maybe<Scalars['String']>;
  channelId?: Maybe<Scalars['ID']>;
};

export enum OrderType {
  Regular = 'Regular',
  Seller = 'Seller',
  Aggregate = 'Aggregate'
}

export type OrderTypeResult = {
  __typename?: 'OrderTypeResult';
  orderType?: Maybe<PaymentOrderType>;
  orderId?: Maybe<Scalars['ID']>;
};

export type PackageDiscount = Node & {
  __typename?: 'PackageDiscount';
  id: Scalars['ID'];
  createdAt: Scalars['DateTime'];
  updatedAt: Scalars['DateTime'];
  introduce?: Maybe<Scalars['String']>;
  name: Scalars['String'];
  displayName: Scalars['String'];
  remarks?: Maybe<Scalars['String']>;
  status?: Maybe<ActivityStatus>;
  startTime?: Maybe<Scalars['DateTime']>;
  endTime?: Maybe<Scalars['DateTime']>;
  price?: Maybe<Scalars['Int']>;
  selectCount?: Maybe<Scalars['Int']>;
  productIds?: Maybe<Array<Maybe<Scalars['ID']>>>;
  stackingDiscountSwitch?: Maybe<Scalars['Boolean']>;
  stackingPromotionTypes?: Maybe<Array<Maybe<PromotionType>>>;
  promotion?: Maybe<Promotion>;
  statisticsData?: Maybe<StatisticsData>;
  activitySynopsis?: Maybe<Scalars['String']>;
  activityContent?: Maybe<Array<Maybe<Scalars['String']>>>;
  activitySuperposition?: Maybe<Scalars['String']>;
  whetherRestrictUsers?: Maybe<Scalars['Boolean']>;
  groupType?: Maybe<GroupType>;
  memberPlanIds?: Maybe<Array<Maybe<Scalars['ID']>>>;
};

export type PackageDiscountFilterParameter = {
  id?: Maybe<IdOperators>;
  createdAt?: Maybe<DateOperators>;
  updatedAt?: Maybe<DateOperators>;
  introduce?: Maybe<StringOperators>;
  name?: Maybe<StringOperators>;
  displayName?: Maybe<StringOperators>;
  remarks?: Maybe<StringOperators>;
  status?: Maybe<StringOperators>;
  startTime?: Maybe<DateOperators>;
  endTime?: Maybe<DateOperators>;
  price?: Maybe<NumberOperators>;
  selectCount?: Maybe<NumberOperators>;
  stackingDiscountSwitch?: Maybe<BooleanOperators>;
  activitySynopsis?: Maybe<StringOperators>;
  activitySuperposition?: Maybe<StringOperators>;
  whetherRestrictUsers?: Maybe<BooleanOperators>;
  groupType?: Maybe<StringOperators>;
};

export type PackageDiscountInput = {
  id?: Maybe<Scalars['ID']>;
  name: Scalars['String'];
  displayName: Scalars['String'];
  remarks?: Maybe<Scalars['String']>;
  startTime: Scalars['DateTime'];
  endTime: Scalars['DateTime'];
  price: Scalars['Int'];
  selectCount: Scalars['Int'];
  productIds: Array<Scalars['ID']>;
  stackingDiscountSwitch: Scalars['Boolean'];
  stackingPromotionTypes?: Maybe<Array<Maybe<PromotionType>>>;
  introduce?: Maybe<Scalars['String']>;
  whetherRestrictUsers?: Maybe<Scalars['Boolean']>;
  groupType?: Maybe<GroupType>;
  memberPlanIds?: Maybe<Array<Maybe<Scalars['ID']>>>;
};

export type PackageDiscountList = PaginatedList & {
  __typename?: 'PackageDiscountList';
  items: Array<PackageDiscount>;
  totalItems: Scalars['Int'];
};

export type PackageDiscountListOptions = {
  /** Skips the first n results, for use in pagination */
  skip?: Maybe<Scalars['Int']>;
  /** Takes n results, for use in pagination */
  take?: Maybe<Scalars['Int']>;
  /** Specifies which properties to sort the results by */
  sort?: Maybe<PackageDiscountSortParameter>;
  /** Allows the results to be filtered */
  filter?: Maybe<PackageDiscountFilterParameter>;
  /** Specifies whether multiple "filter" arguments should be combines with a logical AND or OR operation. Defaults to AND. */
  filterOperator?: Maybe<LogicalOperator>;
};

export type PackageDiscountSortParameter = {
  id?: Maybe<SortOrder>;
  createdAt?: Maybe<SortOrder>;
  updatedAt?: Maybe<SortOrder>;
  introduce?: Maybe<SortOrder>;
  name?: Maybe<SortOrder>;
  displayName?: Maybe<SortOrder>;
  remarks?: Maybe<SortOrder>;
  startTime?: Maybe<SortOrder>;
  endTime?: Maybe<SortOrder>;
  price?: Maybe<SortOrder>;
  selectCount?: Maybe<SortOrder>;
  activitySynopsis?: Maybe<SortOrder>;
  activitySuperposition?: Maybe<SortOrder>;
};

export enum PageKey {
  PointsMall = 'pointsMall',
  PointsCheckin = 'pointsCheckin',
  ForumPost = 'forumPost'
}

export enum PageType {
  HomePage = 'homePage',
  CommodityGroupPage = 'commodityGroupPage',
  ActivePage = 'activePage',
  MembershipPlanPage = 'membershipPlanPage',
  CheckinPage = 'checkinPage'
}

export type PaginatedList = {
  items: Array<Node>;
  totalItems: Scalars['Int'];
};

export type Panel = {
  __typename?: 'Panel';
  accumulative?: Maybe<Scalars['Int']>;
  addMemberNum?: Maybe<Scalars['Int']>;
  orderSumTotal?: Maybe<Scalars['Int']>;
  totalOrder?: Maybe<Scalars['Int']>;
  customNum?: Maybe<Scalars['Int']>;
};

/** Returned when attempting to verify a customer account with a password, when a password has already been set. */
export type PasswordAlreadySetError = ErrorResult & {
  __typename?: 'PasswordAlreadySetError';
  errorCode: ErrorCode;
  message: Scalars['String'];
};

/**
 * Returned if the token used to reset a Customer's password is valid, but has
 * expired according to the `verificationTokenDuration` setting in the AuthOptions.
 */
export type PasswordResetTokenExpiredError = ErrorResult & {
  __typename?: 'PasswordResetTokenExpiredError';
  errorCode: ErrorCode;
  message: Scalars['String'];
};

/**
 * Returned if the token used to reset a Customer's password is either
 * invalid or does not match any expected tokens.
 */
export type PasswordResetTokenInvalidError = ErrorResult & {
  __typename?: 'PasswordResetTokenInvalidError';
  errorCode: ErrorCode;
  message: Scalars['String'];
};

/** Returned when attempting to register or verify a customer account where the given password fails password validation. */
export type PasswordValidationError = ErrorResult & {
  __typename?: 'PasswordValidationError';
  errorCode: ErrorCode;
  message: Scalars['String'];
  validationErrorMessage: Scalars['String'];
};

export type Payment = Node & {
  __typename?: 'Payment';
  id: Scalars['ID'];
  createdAt: Scalars['DateTime'];
  updatedAt: Scalars['DateTime'];
  method: Scalars['String'];
  amount: Scalars['Money'];
  state: Scalars['String'];
  transactionId?: Maybe<Scalars['String']>;
  errorMessage?: Maybe<Scalars['String']>;
  refunds: Array<Refund>;
  metadata?: Maybe<Scalars['JSON']>;
};

/** Returned when a Payment is declined by the payment provider. */
export type PaymentDeclinedError = ErrorResult & {
  __typename?: 'PaymentDeclinedError';
  errorCode: ErrorCode;
  message: Scalars['String'];
  paymentErrorMessage: Scalars['String'];
};

/** Returned when a Payment fails due to an error. */
export type PaymentFailedError = ErrorResult & {
  __typename?: 'PaymentFailedError';
  errorCode: ErrorCode;
  message: Scalars['String'];
  paymentErrorMessage: Scalars['String'];
};

/** Passed as input to the `addPaymentToOrder` mutation. */
export type PaymentInput = {
  /** This field should correspond to the `code` property of a PaymentMethod. */
  method: Scalars['String'];
  /**
   * This field should contain arbitrary data passed to the specified PaymentMethodHandler's `createPayment()` method
   * as the "metadata" argument. For example, it could contain an ID for the payment and other
   * data generated by the payment provider.
   */
  metadata: Scalars['JSON'];
};

export type PaymentInputByOrderType = {
  method: Scalars['String'];
  metadata: Scalars['JSON'];
};

export type PaymentMethod = Node & {
  __typename?: 'PaymentMethod';
  id: Scalars['ID'];
  createdAt: Scalars['DateTime'];
  updatedAt: Scalars['DateTime'];
  name: Scalars['String'];
  code: Scalars['String'];
  description: Scalars['String'];
  enabled: Scalars['Boolean'];
  checker?: Maybe<ConfigurableOperation>;
  handler: ConfigurableOperation;
  translations: Array<PaymentMethodTranslation>;
  customFields?: Maybe<Scalars['JSON']>;
};

export type PaymentMethodQuote = {
  __typename?: 'PaymentMethodQuote';
  id: Scalars['ID'];
  code: Scalars['String'];
  name: Scalars['String'];
  description: Scalars['String'];
  isEligible: Scalars['Boolean'];
  eligibilityMessage?: Maybe<Scalars['String']>;
  customFields?: Maybe<Scalars['JSON']>;
};

export type PaymentMethodTranslation = {
  __typename?: 'PaymentMethodTranslation';
  id: Scalars['ID'];
  createdAt: Scalars['DateTime'];
  updatedAt: Scalars['DateTime'];
  languageCode: LanguageCode;
  name: Scalars['String'];
  description: Scalars['String'];
};

export enum PaymentOrderType {
  Order = 'order',
  MembershipOrder = 'membershipOrder',
  GiftCardOrder = 'giftCardOrder',
  BlindBoxBuyOrder = 'blindBoxBuyOrder'
}

export type PaymentRewardActivity = Node & {
  __typename?: 'PaymentRewardActivity';
  id: Scalars['ID'];
  createdAt?: Maybe<Scalars['DateTime']>;
  updatedAt?: Maybe<Scalars['DateTime']>;
  deletedAt?: Maybe<Scalars['DateTime']>;
  name?: Maybe<Scalars['String']>;
  remarks?: Maybe<Scalars['String']>;
  status?: Maybe<ActivityStatus>;
  displayName?: Maybe<Scalars['String']>;
  startTime?: Maybe<Scalars['DateTime']>;
  endTime?: Maybe<Scalars['DateTime']>;
  introduce?: Maybe<Scalars['String']>;
  applicableProduct?: Maybe<ApplicableProduct>;
  preferentialType?: Maybe<PreferentialType>;
  minimum?: Maybe<Scalars['Int']>;
  grantType?: Maybe<GrantType>;
  isRecovery?: Maybe<Scalars['Boolean']>;
  coupons?: Maybe<Array<Maybe<Coupon>>>;
  whetherRestrictUsers?: Maybe<Scalars['Boolean']>;
  groupType?: Maybe<GroupType>;
  memberPlanIds?: Maybe<Array<Maybe<Scalars['ID']>>>;
};

export type PaymentRewardActivityFilterParameter = {
  id?: Maybe<IdOperators>;
  createdAt?: Maybe<DateOperators>;
  updatedAt?: Maybe<DateOperators>;
  deletedAt?: Maybe<DateOperators>;
  name?: Maybe<StringOperators>;
  remarks?: Maybe<StringOperators>;
  status?: Maybe<StringOperators>;
  displayName?: Maybe<StringOperators>;
  startTime?: Maybe<DateOperators>;
  endTime?: Maybe<DateOperators>;
  introduce?: Maybe<StringOperators>;
  preferentialType?: Maybe<StringOperators>;
  minimum?: Maybe<NumberOperators>;
  grantType?: Maybe<StringOperators>;
  isRecovery?: Maybe<BooleanOperators>;
  whetherRestrictUsers?: Maybe<BooleanOperators>;
  groupType?: Maybe<StringOperators>;
};

export type PaymentRewardActivityInput = {
  id?: Maybe<Scalars['ID']>;
  name: Scalars['String'];
  remarks?: Maybe<Scalars['String']>;
  displayName?: Maybe<Scalars['String']>;
  startTime: Scalars['DateTime'];
  endTime: Scalars['DateTime'];
  introduce?: Maybe<Scalars['String']>;
  applicableProduct: ApplicableProductInput;
  preferentialType: PreferentialType;
  minimum?: Maybe<Scalars['Int']>;
  grantType: GrantType;
  isRecovery: Scalars['Boolean'];
  couponIds?: Maybe<Array<Maybe<Scalars['ID']>>>;
  whetherRestrictUsers?: Maybe<Scalars['Boolean']>;
  groupType?: Maybe<GroupType>;
  memberPlanIds?: Maybe<Array<Maybe<Scalars['ID']>>>;
};

export type PaymentRewardActivityList = PaginatedList & {
  __typename?: 'PaymentRewardActivityList';
  items: Array<PaymentRewardActivity>;
  totalItems: Scalars['Int'];
};

export type PaymentRewardActivityListOptions = {
  /** Skips the first n results, for use in pagination */
  skip?: Maybe<Scalars['Int']>;
  /** Takes n results, for use in pagination */
  take?: Maybe<Scalars['Int']>;
  /** Specifies which properties to sort the results by */
  sort?: Maybe<PaymentRewardActivitySortParameter>;
  /** Allows the results to be filtered */
  filter?: Maybe<PaymentRewardActivityFilterParameter>;
  /** Specifies whether multiple "filter" arguments should be combines with a logical AND or OR operation. Defaults to AND. */
  filterOperator?: Maybe<LogicalOperator>;
};

export type PaymentRewardActivitySortParameter = {
  id?: Maybe<SortOrder>;
  createdAt?: Maybe<SortOrder>;
  updatedAt?: Maybe<SortOrder>;
  deletedAt?: Maybe<SortOrder>;
  name?: Maybe<SortOrder>;
  remarks?: Maybe<SortOrder>;
  displayName?: Maybe<SortOrder>;
  startTime?: Maybe<SortOrder>;
  endTime?: Maybe<SortOrder>;
  introduce?: Maybe<SortOrder>;
  minimum?: Maybe<SortOrder>;
};

export enum PaymentRewardCouponState {
  NotReceived = 'notReceived',
  Received = 'received',
  Locked = 'locked',
  Invalid = 'invalid'
}

export type PeriodAndDiscount = {
  __typename?: 'PeriodAndDiscount';
  expireNumberOfPeriod?: Maybe<Scalars['Int']>;
  discountType?: Maybe<DiscountType>;
  discountAmount?: Maybe<Scalars['Int']>;
  recommend?: Maybe<Scalars['Boolean']>;
};

export type PeriodAndDiscountInput = {
  expireNumberOfPeriod?: Maybe<Scalars['Int']>;
  discountType?: Maybe<DiscountType>;
  discountAmount?: Maybe<Scalars['Int']>;
  recommend?: Maybe<Scalars['Boolean']>;
};

/**
 * @description
 * Permissions for administrators and customers. Used to control access to
 * GraphQL resolvers via the {@link Allow} decorator.
 *
 * ## Understanding Permission.Owner
 *
 * `Permission.Owner` is a special permission which is used in some Vendure resolvers to indicate that that resolver should only
 * be accessible to the "owner" of that resource.
 *
 * For example, the Shop API `activeCustomer` query resolver should only return the Customer object for the "owner" of that Customer, i.e.
 * based on the activeUserId of the current session. As a result, the resolver code looks like this:
 *
 * @example
 * ```TypeScript
 * \@Query()
 * \@Allow(Permission.Owner)
 * async activeCustomer(\@Ctx() ctx: RequestContext): Promise<Customer | undefined> {
 *   const userId = ctx.activeUserId;
 *   if (userId) {
 *     return this.customerService.findOneByUserId(ctx, userId);
 *   }
 * }
 * ```
 *
 * Here we can see that the "ownership" must be enforced by custom logic inside the resolver. Since "ownership" cannot be defined generally
 * nor statically encoded at build-time, any resolvers using `Permission.Owner` **must** include logic to enforce that only the owner
 * of the resource has access. If not, then it is the equivalent of using `Permission.Public`.
 *
 *
 * @docsCategory common
 */
export enum Permission {
  /** Authenticated means simply that the user is logged in */
  Authenticated = 'Authenticated',
  /** SuperAdmin has unrestricted access to all operations */
  SuperAdmin = 'SuperAdmin',
  /** Owner means the user owns this entity, e.g. a Customer's own Order */
  Owner = 'Owner',
  /** Public means any unauthenticated user may perform the operation */
  Public = 'Public',
  /** Grants permission to update GlobalSettings */
  UpdateGlobalSettings = 'UpdateGlobalSettings',
  /** Grants permission to create Products, Facets, Assets, Collections */
  CreateCatalog = 'CreateCatalog',
  /** Grants permission to read Products, Facets, Assets, Collections */
  ReadCatalog = 'ReadCatalog',
  /** Grants permission to update Products, Facets, Assets, Collections */
  UpdateCatalog = 'UpdateCatalog',
  /** Grants permission to delete Products, Facets, Assets, Collections */
  DeleteCatalog = 'DeleteCatalog',
  /** Grants permission to create PaymentMethods, ShippingMethods, TaxCategories, TaxRates, Zones, Countries, System & GlobalSettings */
  CreateSettings = 'CreateSettings',
  /** Grants permission to read PaymentMethods, ShippingMethods, TaxCategories, TaxRates, Zones, Countries, System & GlobalSettings */
  ReadSettings = 'ReadSettings',
  /** Grants permission to update PaymentMethods, ShippingMethods, TaxCategories, TaxRates, Zones, Countries, System & GlobalSettings */
  UpdateSettings = 'UpdateSettings',
  /** Grants permission to delete PaymentMethods, ShippingMethods, TaxCategories, TaxRates, Zones, Countries, System & GlobalSettings */
  DeleteSettings = 'DeleteSettings',
  /** Grants permission to create Administrator */
  CreateAdministrator = 'CreateAdministrator',
  /** Grants permission to read Administrator */
  ReadAdministrator = 'ReadAdministrator',
  /** Grants permission to update Administrator */
  UpdateAdministrator = 'UpdateAdministrator',
  /** Grants permission to delete Administrator */
  DeleteAdministrator = 'DeleteAdministrator',
  /** Grants permission to create Asset */
  CreateAsset = 'CreateAsset',
  /** Grants permission to read Asset */
  ReadAsset = 'ReadAsset',
  /** Grants permission to update Asset */
  UpdateAsset = 'UpdateAsset',
  /** Grants permission to delete Asset */
  DeleteAsset = 'DeleteAsset',
  /** Grants permission to create Channel */
  CreateChannel = 'CreateChannel',
  /** Grants permission to read Channel */
  ReadChannel = 'ReadChannel',
  /** Grants permission to update Channel */
  UpdateChannel = 'UpdateChannel',
  /** Grants permission to delete Channel */
  DeleteChannel = 'DeleteChannel',
  /** Grants permission to create Collection */
  CreateCollection = 'CreateCollection',
  /** Grants permission to read Collection */
  ReadCollection = 'ReadCollection',
  /** Grants permission to update Collection */
  UpdateCollection = 'UpdateCollection',
  /** Grants permission to delete Collection */
  DeleteCollection = 'DeleteCollection',
  /** Grants permission to create Country */
  CreateCountry = 'CreateCountry',
  /** Grants permission to read Country */
  ReadCountry = 'ReadCountry',
  /** Grants permission to update Country */
  UpdateCountry = 'UpdateCountry',
  /** Grants permission to delete Country */
  DeleteCountry = 'DeleteCountry',
  /** Grants permission to create Customer */
  CreateCustomer = 'CreateCustomer',
  /** Grants permission to read Customer */
  ReadCustomer = 'ReadCustomer',
  /** Grants permission to update Customer */
  UpdateCustomer = 'UpdateCustomer',
  /** Grants permission to delete Customer */
  DeleteCustomer = 'DeleteCustomer',
  /** Grants permission to create CustomerGroup */
  CreateCustomerGroup = 'CreateCustomerGroup',
  /** Grants permission to read CustomerGroup */
  ReadCustomerGroup = 'ReadCustomerGroup',
  /** Grants permission to update CustomerGroup */
  UpdateCustomerGroup = 'UpdateCustomerGroup',
  /** Grants permission to delete CustomerGroup */
  DeleteCustomerGroup = 'DeleteCustomerGroup',
  /** Grants permission to create Facet */
  CreateFacet = 'CreateFacet',
  /** Grants permission to read Facet */
  ReadFacet = 'ReadFacet',
  /** Grants permission to update Facet */
  UpdateFacet = 'UpdateFacet',
  /** Grants permission to delete Facet */
  DeleteFacet = 'DeleteFacet',
  /** Grants permission to create Order */
  CreateOrder = 'CreateOrder',
  /** Grants permission to read Order */
  ReadOrder = 'ReadOrder',
  /** Grants permission to update Order */
  UpdateOrder = 'UpdateOrder',
  /** Grants permission to delete Order */
  DeleteOrder = 'DeleteOrder',
  /** Grants permission to create PaymentMethod */
  CreatePaymentMethod = 'CreatePaymentMethod',
  /** Grants permission to read PaymentMethod */
  ReadPaymentMethod = 'ReadPaymentMethod',
  /** Grants permission to update PaymentMethod */
  UpdatePaymentMethod = 'UpdatePaymentMethod',
  /** Grants permission to delete PaymentMethod */
  DeletePaymentMethod = 'DeletePaymentMethod',
  /** Grants permission to create Product */
  CreateProduct = 'CreateProduct',
  /** Grants permission to read Product */
  ReadProduct = 'ReadProduct',
  /** Grants permission to update Product */
  UpdateProduct = 'UpdateProduct',
  /** Grants permission to delete Product */
  DeleteProduct = 'DeleteProduct',
  /** Grants permission to create Promotion */
  CreatePromotion = 'CreatePromotion',
  /** Grants permission to read Promotion */
  ReadPromotion = 'ReadPromotion',
  /** Grants permission to update Promotion */
  UpdatePromotion = 'UpdatePromotion',
  /** Grants permission to delete Promotion */
  DeletePromotion = 'DeletePromotion',
  /** Grants permission to create ShippingMethod */
  CreateShippingMethod = 'CreateShippingMethod',
  /** Grants permission to read ShippingMethod */
  ReadShippingMethod = 'ReadShippingMethod',
  /** Grants permission to update ShippingMethod */
  UpdateShippingMethod = 'UpdateShippingMethod',
  /** Grants permission to delete ShippingMethod */
  DeleteShippingMethod = 'DeleteShippingMethod',
  /** Grants permission to create Tag */
  CreateTag = 'CreateTag',
  /** Grants permission to read Tag */
  ReadTag = 'ReadTag',
  /** Grants permission to update Tag */
  UpdateTag = 'UpdateTag',
  /** Grants permission to delete Tag */
  DeleteTag = 'DeleteTag',
  /** Grants permission to create TaxCategory */
  CreateTaxCategory = 'CreateTaxCategory',
  /** Grants permission to read TaxCategory */
  ReadTaxCategory = 'ReadTaxCategory',
  /** Grants permission to update TaxCategory */
  UpdateTaxCategory = 'UpdateTaxCategory',
  /** Grants permission to delete TaxCategory */
  DeleteTaxCategory = 'DeleteTaxCategory',
  /** Grants permission to create TaxRate */
  CreateTaxRate = 'CreateTaxRate',
  /** Grants permission to read TaxRate */
  ReadTaxRate = 'ReadTaxRate',
  /** Grants permission to update TaxRate */
  UpdateTaxRate = 'UpdateTaxRate',
  /** Grants permission to delete TaxRate */
  DeleteTaxRate = 'DeleteTaxRate',
  /** Grants permission to create Seller */
  CreateSeller = 'CreateSeller',
  /** Grants permission to read Seller */
  ReadSeller = 'ReadSeller',
  /** Grants permission to update Seller */
  UpdateSeller = 'UpdateSeller',
  /** Grants permission to delete Seller */
  DeleteSeller = 'DeleteSeller',
  /** Grants permission to create StockLocation */
  CreateStockLocation = 'CreateStockLocation',
  /** Grants permission to read StockLocation */
  ReadStockLocation = 'ReadStockLocation',
  /** Grants permission to update StockLocation */
  UpdateStockLocation = 'UpdateStockLocation',
  /** Grants permission to delete StockLocation */
  DeleteStockLocation = 'DeleteStockLocation',
  /** Grants permission to create System */
  CreateSystem = 'CreateSystem',
  /** Grants permission to read System */
  ReadSystem = 'ReadSystem',
  /** Grants permission to update System */
  UpdateSystem = 'UpdateSystem',
  /** Grants permission to delete System */
  DeleteSystem = 'DeleteSystem',
  /** Grants permission to create Zone */
  CreateZone = 'CreateZone',
  /** Grants permission to read Zone */
  ReadZone = 'ReadZone',
  /** Grants permission to update Zone */
  UpdateZone = 'UpdateZone',
  /** Grants permission to delete Zone */
  DeleteZone = 'DeleteZone',
  /** Operation management banner */
  BannerOperate = 'BannerOperate',
  /** Customize page operation permissions */
  CustomPageOperate = 'CustomPageOperate',
  /** Hot Word Operate Relevant authority  */
  HotWordOperate = 'HotWordOperate',
  /** Announcement Operate Relevant authority  */
  AnnouncementOperate = 'AnnouncementOperate',
  /** Discount paper operation  */
  CouponOperate = 'CouponOperate',
  /** Grants permission to create Coupon */
  CreateCoupon = 'CreateCoupon',
  /** Grants permission to read Coupon */
  ReadCoupon = 'ReadCoupon',
  /** Grants permission to update Coupon */
  UpdateCoupon = 'UpdateCoupon',
  /** Grants permission to delete Coupon */
  DeleteCoupon = 'DeleteCoupon',
  /** 优惠券失效操作权限 */
  CouponFailureOperate = 'CouponFailureOperate',
  /** Grants permission to create UserCoupon */
  CreateUserCoupon = 'CreateUserCoupon',
  /** Grants permission to read UserCoupon */
  ReadUserCoupon = 'ReadUserCoupon',
  /** Grants permission to update UserCoupon */
  UpdateUserCoupon = 'UpdateUserCoupon',
  /** Grants permission to delete UserCoupon */
  DeleteUserCoupon = 'DeleteUserCoupon',
  /** 商家主动退款权限 */
  MerchantVoluntaryRefund = 'MerchantVoluntaryRefund',
  /** 分享推广权限 */
  ShareOperate = 'ShareOperate',
  /** Discount paper PurchasePremium  */
  PurchasePremiumOperate = 'PurchasePremiumOperate',
  /** The distributor manages authority */
  DistributorOperate = 'DistributorOperate',
  /** Grants permission to create Distributor */
  CreateDistributor = 'CreateDistributor',
  /** Grants permission to read Distributor */
  ReadDistributor = 'ReadDistributor',
  /** Grants permission to update Distributor */
  UpdateDistributor = 'UpdateDistributor',
  /** Grants permission to delete Distributor */
  DeleteDistributor = 'DeleteDistributor',
  /** 设置用户的分销员 */
  SetCustomerDistributor = 'SetCustomerDistributor',
  /** Discount activity management authority */
  DiscountActivityOperate = 'DiscountActivityOperate',
  /** Full discount gift management authority */
  FullDiscountPresentOperate = 'FullDiscountPresentOperate',
  /** Free gift management authority */
  FreeGiftOperate = 'FreeGiftOperate',
  /** Setting management authority */
  SettingOperate = 'SettingOperate',
  /** Statistics management authority */
  Statistics = 'Statistics',
  /** Group customer management authority */
  GroupCustomerOperate = 'GroupCustomerOperate',
  /** Operation plan management authority */
  OperationPlanOperate = 'OperationPlanOperate',
  /** 打包一口价活动管理权限 */
  PackageDiscountOperate = 'PackageDiscountOperate',
  /** 交易客户统计权限 */
  TransactionCustomerStatistics = 'TransactionCustomerStatistics',
  /** 友盟配置权限 */
  UMengConfig = 'UMengConfig',
  /** Grants permission to create MemberPrice */
  CreateMemberPrice = 'CreateMemberPrice',
  /** Grants permission to read MemberPrice */
  ReadMemberPrice = 'ReadMemberPrice',
  /** Grants permission to update MemberPrice */
  UpdateMemberPrice = 'UpdateMemberPrice',
  /** Grants permission to delete MemberPrice */
  DeleteMemberPrice = 'DeleteMemberPrice',
  /** Grants permission to create GiftCardOrder */
  CreateGiftCardOrder = 'CreateGiftCardOrder',
  /** Grants permission to read GiftCardOrder */
  ReadGiftCardOrder = 'ReadGiftCardOrder',
  /** Grants permission to update GiftCardOrder */
  UpdateGiftCardOrder = 'UpdateGiftCardOrder',
  /** Grants permission to delete GiftCardOrder */
  DeleteGiftCardOrder = 'DeleteGiftCardOrder',
  /** Grants permission to create SelectiveGiftActivity */
  CreateSelectiveGiftActivity = 'CreateSelectiveGiftActivity',
  /** Grants permission to read SelectiveGiftActivity */
  ReadSelectiveGiftActivity = 'ReadSelectiveGiftActivity',
  /** Grants permission to update SelectiveGiftActivity */
  UpdateSelectiveGiftActivity = 'UpdateSelectiveGiftActivity',
  /** Grants permission to delete SelectiveGiftActivity */
  DeleteSelectiveGiftActivity = 'DeleteSelectiveGiftActivity',
  /** Grants permission to create PaymentRewardActivity */
  CreatePaymentRewardActivity = 'CreatePaymentRewardActivity',
  /** Grants permission to read PaymentRewardActivity */
  ReadPaymentRewardActivity = 'ReadPaymentRewardActivity',
  /** Grants permission to update PaymentRewardActivity */
  UpdatePaymentRewardActivity = 'UpdatePaymentRewardActivity',
  /** Grants permission to delete PaymentRewardActivity */
  DeletePaymentRewardActivity = 'DeletePaymentRewardActivity',
  /** 商品数据统计导出文件 */
  ProductDataStatisticsExportFile = 'ProductDataStatisticsExportFile',
  /** 客户数据导出文件 */
  CustomerDataExportFile = 'CustomerDataExportFile',
  /** 会员订单数据导出 */
  MemberOrderDataExport = 'MemberOrderDataExport',
  /** 客户分销绑定导出 */
  CustomerDistributionBindingExport = 'CustomerDistributionBindingExport',
  /** 创建优惠券包 */
  CreateCouponBundle = 'CreateCouponBundle',
  /** 查看优惠券包 */
  ReadCouponBundle = 'ReadCouponBundle',
  /** 编辑优惠券包 */
  UpdateCouponBundle = 'UpdateCouponBundle',
  /** 删除优惠券包 */
  DeleteCouponBundle = 'DeleteCouponBundle',
  /** 创建新人礼包 */
  CreateFirstCustomerBenefit = 'CreateFirstCustomerBenefit',
  /** 查看新人礼包 */
  ReadFirstCustomerBenefit = 'ReadFirstCustomerBenefit',
  /** 编辑新人礼包 */
  UpdateFirstCustomerBenefit = 'UpdateFirstCustomerBenefit',
  /** 删除新人礼包 */
  DeleteFirstCustomerBenefit = 'DeleteFirstCustomerBenefit',
  /** 创建商品互斥组 */
  CreateExclusionGroup = 'CreateExclusionGroup',
  /** 查看商品互斥组 */
  ReadExclusionGroup = 'ReadExclusionGroup',
  /** 编辑商品互斥组 */
  UpdateExclusionGroup = 'UpdateExclusionGroup',
  /** 删除商品互斥组 */
  DeleteExclusionGroup = 'DeleteExclusionGroup',
  /** 创建积分商品 */
  CreatePointsProduct = 'CreatePointsProduct',
  /** 查看积分商品 */
  ReadPointsProduct = 'ReadPointsProduct',
  /** 编辑积分商品 */
  UpdatePointsProduct = 'UpdatePointsProduct',
  /** 删除积分商品 */
  DeletePointsProduct = 'DeletePointsProduct',
  /** 创建积分配置 */
  CreatePointsConfig = 'CreatePointsConfig',
  /** 查看积分配置 */
  ReadPointsConfig = 'ReadPointsConfig',
  /** 编辑积分配置 */
  UpdatePointsConfig = 'UpdatePointsConfig',
  /** 删除积分配置 */
  DeletePointsConfig = 'DeletePointsConfig',
  /** 创建商品购买地区限制 */
  CreateProductRestrictions = 'CreateProductRestrictions',
  /** 查看商品购买地区限制 */
  ReadProductRestrictions = 'ReadProductRestrictions',
  /** 编辑商品购买地区限制 */
  UpdateProductRestrictions = 'UpdateProductRestrictions',
  /** 删除商品购买地区限制 */
  DeleteProductRestrictions = 'DeleteProductRestrictions',
  /** 创建签到配置 */
  CreateCheckinConfig = 'CreateCheckinConfig',
  /** 查看签到配置 */
  ReadCheckinConfig = 'ReadCheckinConfig',
  /** 编辑签到配置 */
  UpdateCheckinConfig = 'UpdateCheckinConfig',
  /** 删除签到配置 */
  DeleteCheckinConfig = 'DeleteCheckinConfig',
  /** 创建导出任务 */
  CreateExportTask = 'CreateExportTask',
  /** 查看导出任务 */
  ReadExportTask = 'ReadExportTask',
  /** 编辑导出任务 */
  UpdateExportTask = 'UpdateExportTask',
  /** 删除导出任务 */
  DeleteExportTask = 'DeleteExportTask',
  /** 创建盲盒活动 */
  CreateBlindBoxActivity = 'CreateBlindBoxActivity',
  /** 查看盲盒活动 */
  ReadBlindBoxActivity = 'ReadBlindBoxActivity',
  /** 编辑盲盒活动 */
  UpdateBlindBoxActivity = 'UpdateBlindBoxActivity',
  /** 删除盲盒活动 */
  DeleteBlindBoxActivity = 'DeleteBlindBoxActivity',
  /** 创建盲盒活动限购配置 */
  CreateBlindBoxActivityLimitConfig = 'CreateBlindBoxActivityLimitConfig',
  /** 查看盲盒活动限购配置 */
  ReadBlindBoxActivityLimitConfig = 'ReadBlindBoxActivityLimitConfig',
  /** 编辑盲盒活动限购配置 */
  UpdateBlindBoxActivityLimitConfig = 'UpdateBlindBoxActivityLimitConfig',
  /** 删除盲盒活动限购配置 */
  DeleteBlindBoxActivityLimitConfig = 'DeleteBlindBoxActivityLimitConfig',
  /** 创建助力礼包 */
  CreateAssistGift = 'CreateAssistGift',
  /** 查看助力礼包 */
  ReadAssistGift = 'ReadAssistGift',
  /** 编辑助力礼包 */
  UpdateAssistGift = 'UpdateAssistGift',
  /** 删除助力礼包 */
  DeleteAssistGift = 'DeleteAssistGift',
  /** 创建盲盒购买 */
  CreateBlindBoxBuy = 'CreateBlindBoxBuy',
  /** 查看盲盒购买 */
  ReadBlindBoxBuy = 'ReadBlindBoxBuy',
  /** 编辑盲盒购买 */
  UpdateBlindBoxBuy = 'UpdateBlindBoxBuy',
  /** 删除盲盒购买 */
  DeleteBlindBoxBuy = 'DeleteBlindBoxBuy',
  /** 盲盒统计查看权限 */
  BlindBoxStatisticsOperate = 'BlindBoxStatisticsOperate',
  /** 创建浮窗 */
  CreateFloatingWindow = 'CreateFloatingWindow',
  /** 查看浮窗 */
  ReadFloatingWindow = 'ReadFloatingWindow',
  /** 编辑浮窗 */
  UpdateFloatingWindow = 'UpdateFloatingWindow',
  /** 删除浮窗 */
  DeleteFloatingWindow = 'DeleteFloatingWindow',
  /** 创建分享设置 */
  CreateShareSetting = 'CreateShareSetting',
  /** 查看分享设置 */
  ReadShareSetting = 'ReadShareSetting',
  /** 编辑分享设置 */
  UpdateShareSetting = 'UpdateShareSetting',
  /** 删除分享设置 */
  DeleteShareSetting = 'DeleteShareSetting',
  /** 创建个人中心页面 */
  CreatePersonalCenter = 'CreatePersonalCenter',
  /** 查看个人中心页面 */
  ReadPersonalCenter = 'ReadPersonalCenter',
  /** 编辑个人中心页面 */
  UpdatePersonalCenter = 'UpdatePersonalCenter',
  /** 删除个人中心页面 */
  DeletePersonalCenter = 'DeletePersonalCenter',
  /** 创建购物积分领取活动 */
  CreateShoppingCreditsClaimActivity = 'CreateShoppingCreditsClaimActivity',
  /** 查看购物积分领取活动 */
  ReadShoppingCreditsClaimActivity = 'ReadShoppingCreditsClaimActivity',
  /** 编辑购物积分领取活动 */
  UpdateShoppingCreditsClaimActivity = 'UpdateShoppingCreditsClaimActivity',
  /** 删除购物积分领取活动 */
  DeleteShoppingCreditsClaimActivity = 'DeleteShoppingCreditsClaimActivity',
  /** 创建购物积分抵扣活动 */
  CreateShoppingCreditsDeductionActivity = 'CreateShoppingCreditsDeductionActivity',
  /** 查看购物积分抵扣活动 */
  ReadShoppingCreditsDeductionActivity = 'ReadShoppingCreditsDeductionActivity',
  /** 编辑购物积分抵扣活动 */
  UpdateShoppingCreditsDeductionActivity = 'UpdateShoppingCreditsDeductionActivity',
  /** 删除购物积分抵扣活动 */
  DeleteShoppingCreditsDeductionActivity = 'DeleteShoppingCreditsDeductionActivity',
  /** 创建购物积分配置 */
  CreateShoppingCreditsConfig = 'CreateShoppingCreditsConfig',
  /** 查看购物积分配置 */
  ReadShoppingCreditsConfig = 'ReadShoppingCreditsConfig',
  /** 编辑购物积分配置 */
  UpdateShoppingCreditsConfig = 'UpdateShoppingCreditsConfig',
  /** 删除购物积分配置 */
  DeleteShoppingCreditsConfig = 'DeleteShoppingCreditsConfig',
  /** 创建活动倒计时 */
  CreateActivityCountdown = 'CreateActivityCountdown',
  /** 查看活动倒计时 */
  ReadActivityCountdown = 'ReadActivityCountdown',
  /** 编辑活动倒计时 */
  UpdateActivityCountdown = 'UpdateActivityCountdown',
  /** 删除活动倒计时 */
  DeleteActivityCountdown = 'DeleteActivityCountdown',
  /** Permission to manipulate information about subscriptions */
  SubscriptionOperate = 'SubscriptionOperate',
  /** Permission to view subscription information */
  SubscriptionRead = 'SubscriptionRead',
  /** Permission to manipulate information about subscription plan */
  SubscriptionPlanOperate = 'SubscriptionPlanOperate',
  /** Permission to view subscription plan information */
  SubscriptionPlanRead = 'SubscriptionPlanRead',
  /** Permission to update order custom field */
  UpdateOrderCustomFieldPermission = 'UpdateOrderCustomFieldPermission',
  /** Permission to update order shipping address */
  UpdateOrderShippingAddressPermission = 'UpdateOrderShippingAddressPermission',
  /** Permission to deliver goods */
  DeliveryPermission = 'DeliveryPermission',
  /** Create merchant applet configuration permissions */
  CreateWeChatConfig = 'CreateWeChatConfig',
  /** The super administrator sets the activity permission */
  SettingCompActivity = 'SettingCompActivity',
  /** View activity */
  ReadCompActivity = 'ReadCompActivity',
  /** Permissions after joining an activity */
  JoinCompActivity = 'JoinCompActivity',
  /** Set permissions for the blind box */
  SettingBox = 'SettingBox',
  /** Example Query blind box information */
  ReadBox = 'ReadBox',
  /** Authority to handle after sales service */
  AfterSaleOperate = 'AfterSaleOperate',
  /** 查看售后单权限 */
  AfterSaleRead = 'AfterSaleRead',
  /** 售后审核权限 */
  AfterSaleAudit = 'AfterSaleAudit',
  /** Grants permission to create AfterSaleChannelAddress */
  CreateAfterSaleChannelAddress = 'CreateAfterSaleChannelAddress',
  /** Grants permission to read AfterSaleChannelAddress */
  ReadAfterSaleChannelAddress = 'ReadAfterSaleChannelAddress',
  /** Grants permission to update AfterSaleChannelAddress */
  UpdateAfterSaleChannelAddress = 'UpdateAfterSaleChannelAddress',
  /** Grants permission to delete AfterSaleChannelAddress */
  DeleteAfterSaleChannelAddress = 'DeleteAfterSaleChannelAddress',
  /** Permission to update order price */
  UpdateOrderPricePermission = 'UpdateOrderPricePermission',
  /** Membership card operation related rights and interests */
  MembershipPlanOperate = 'MembershipPlanOperate',
  /** Grants permission to create MembershipPlan */
  CreateMembershipPlan = 'CreateMembershipPlan',
  /** Grants permission to read MembershipPlan */
  ReadMembershipPlan = 'ReadMembershipPlan',
  /** Grants permission to update MembershipPlan */
  UpdateMembershipPlan = 'UpdateMembershipPlan',
  /** Grants permission to delete MembershipPlan */
  DeleteMembershipPlan = 'DeleteMembershipPlan',
  /** 会员权益领取记录查看权限 */
  MemberClaimRecordRead = 'MemberClaimRecordRead',
  /** 会员权益退卡权限 */
  MemberClaimReturnCard = 'MemberClaimReturnCard',
  /** 会员权益延长会员有效期权限 */
  MembershipExtendTimeMember = 'MembershipExtendTimeMember',
  /** 会员权益退卡记录查看权限 */
  MemberReturnCardRecordRead = 'MemberReturnCardRecordRead',
  /** Grants permission to create GiftCard */
  CreateGiftCard = 'CreateGiftCard',
  /** Grants permission to read GiftCard */
  ReadGiftCard = 'ReadGiftCard',
  /** Grants permission to update GiftCard */
  UpdateGiftCard = 'UpdateGiftCard',
  /** Grants permission to delete GiftCard */
  DeleteGiftCard = 'DeleteGiftCard',
  /** 创建话题 */
  CreateForumTag = 'CreateForumTag',
  /** 查看话题 */
  ReadForumTag = 'ReadForumTag',
  /** 编辑话题 */
  UpdateForumTag = 'UpdateForumTag',
  /** 删除话题 */
  DeleteForumTag = 'DeleteForumTag',
  /** 创建帖子 */
  CreateForumPost = 'CreateForumPost',
  /** 查看帖子 */
  ReadForumPost = 'ReadForumPost',
  /** 编辑帖子 */
  UpdateForumPost = 'UpdateForumPost',
  /** 删除帖子 */
  DeleteForumPost = 'DeleteForumPost',
  /** 创建论坛活动 */
  CreateForumActivity = 'CreateForumActivity',
  /** 查看论坛活动 */
  ReadForumActivity = 'ReadForumActivity',
  /** 编辑论坛活动 */
  UpdateForumActivity = 'UpdateForumActivity',
  /** 删除论坛活动 */
  DeleteForumActivity = 'DeleteForumActivity',
  /** 创建论坛用户 */
  CreateForumCustomer = 'CreateForumCustomer',
  /** 查看论坛用户 */
  ReadForumCustomer = 'ReadForumCustomer',
  /** 编辑论坛用户 */
  UpdateForumCustomer = 'UpdateForumCustomer',
  /** 删除论坛用户 */
  DeleteForumCustomer = 'DeleteForumCustomer',
  /** 创建评论 */
  CreateForumReview = 'CreateForumReview',
  /** 查看评论 */
  ReadForumReview = 'ReadForumReview',
  /** 编辑评论 */
  UpdateForumReview = 'UpdateForumReview',
  /** 删除评论 */
  DeleteForumReview = 'DeleteForumReview',
  /** Create Gather Water Pond configuration permissions */
  CreateGatherWaterPondConfig = 'CreateGatherWaterPondConfig',
  /** Update QiYu configuration permissions */
  UpdateQiYuConfig = 'UpdateQiYuConfig',
  /** Create merchant applet configuration permissions */
  CreateYouZanYunConfig = 'CreateYouZanYunConfig',
  /** 添加同步有赞会员任务 */
  AddCustomerSyncMemberTask = 'AddCustomerSyncMemberTask',
  /** WangDianTongConfigOperation permissions */
  WangDianTongConfigOperation = 'WangDianTongConfigOperation'
}

export type PersonalCenter = Node & {
  __typename?: 'PersonalCenter';
  id: Scalars['ID'];
  createdAt?: Maybe<Scalars['DateTime']>;
  updatedAt?: Maybe<Scalars['DateTime']>;
  topComponentType?: Maybe<ComponentType>;
  topComponent?: Maybe<ComponentValue>;
  bottomComponentType?: Maybe<ComponentType>;
  bottomComponent?: Maybe<ComponentValue>;
  channelId?: Maybe<Scalars['ID']>;
};

export type PersonalCenterInput = {
  topComponentType?: Maybe<ComponentType>;
  topComponent?: Maybe<ComponentValueInput>;
  bottomComponentType?: Maybe<ComponentType>;
  bottomComponent?: Maybe<ComponentValueInput>;
};

export type PhoneLoginArgs = {
  phone: Scalars['String'];
  openIdKey?: Maybe<Scalars['String']>;
  code: Scalars['String'];
};

export type PictureResource = {
  __typename?: 'PictureResource';
  imgHeight: Scalars['Int'];
  imgWidth: Scalars['Int'];
  imgUrl?: Maybe<Scalars['String']>;
  videoUrl?: Maybe<Scalars['String']>;
  alt?: Maybe<Scalars['String']>;
  jump?: Maybe<Array<Maybe<Jump>>>;
};

export type PictureResourceInput = {
  imgHeight: Scalars['Int'];
  imgWidth: Scalars['Int'];
  imgUrl?: Maybe<Scalars['String']>;
  alt?: Maybe<Scalars['String']>;
  videoUrl?: Maybe<Scalars['String']>;
  jump: Array<JumpInput>;
};

export type PlatformAfterLineInput = {
  lineId: Scalars['ID'];
  quantity?: Maybe<Scalars['Int']>;
  price?: Maybe<Scalars['Int']>;
};

export type PlatformAfterSaleInput = {
  orderId: Scalars['ID'];
  description: Scalars['String'];
  platformAfterLines: Array<PlatformAfterLineInput>;
  price: Scalars['Int'];
};

export type PointExchange = {
  __typename?: 'PointExchange';
  points?: Maybe<Scalars['Int']>;
  cash?: Maybe<Scalars['Int']>;
  exchangeTotal?: Maybe<Scalars['Int']>;
  exchangeCount?: Maybe<Scalars['Int']>;
};

export type PointsConfig = Node & {
  __typename?: 'PointsConfig';
  id: Scalars['ID'];
  createdAt?: Maybe<Scalars['DateTime']>;
  updatedAt?: Maybe<Scalars['DateTime']>;
  validityPeriod?: Maybe<ValidityPeriod>;
  pointsGrantTiming?: Maybe<PointsGrantTiming>;
  pointsExchangeGrant?: Maybe<Scalars['Boolean']>;
  pointsRefundGrant?: Maybe<Scalars['Boolean']>;
  pointsExchangeRate?: Maybe<Scalars['Int']>;
  pointsRuleText?: Maybe<Scalars['String']>;
};

export type PointsConfigInput = {
  validityPeriod?: Maybe<ValidityPeriodInput>;
  pointsGrantTiming?: Maybe<PointsGrantTiming>;
  pointsExchangeGrant?: Maybe<Scalars['Boolean']>;
  pointsRefundGrant?: Maybe<Scalars['Boolean']>;
  pointsExchangeRate?: Maybe<Scalars['Int']>;
  pointsRuleText?: Maybe<Scalars['String']>;
};

export enum PointsGrantTiming {
  ConfirmReceipt = 'confirmReceipt',
  CompletePayment = 'completePayment'
}

export type PointsHistory = Node & {
  __typename?: 'PointsHistory';
  id: Scalars['ID'];
  createdAt?: Maybe<Scalars['DateTime']>;
  updatedAt?: Maybe<Scalars['DateTime']>;
  sourceId?: Maybe<Scalars['ID']>;
  sourceType?: Maybe<PointsSourceType>;
  points?: Maybe<Scalars['Int']>;
  symbolType?: Maybe<SymbolType>;
  description?: Maybe<Scalars['String']>;
};

export type PointsHistoryFilterParameter = {
  id?: Maybe<IdOperators>;
  createdAt?: Maybe<DateOperators>;
  updatedAt?: Maybe<DateOperators>;
  sourceId?: Maybe<IdOperators>;
  sourceType?: Maybe<StringOperators>;
  points?: Maybe<NumberOperators>;
  symbolType?: Maybe<StringOperators>;
  description?: Maybe<StringOperators>;
};

export type PointsHistoryList = PaginatedList & {
  __typename?: 'PointsHistoryList';
  items: Array<PointsHistory>;
  totalItems: Scalars['Int'];
};

export type PointsHistoryListOptions = {
  /** Skips the first n results, for use in pagination */
  skip?: Maybe<Scalars['Int']>;
  /** Takes n results, for use in pagination */
  take?: Maybe<Scalars['Int']>;
  /** Specifies which properties to sort the results by */
  sort?: Maybe<PointsHistorySortParameter>;
  /** Allows the results to be filtered */
  filter?: Maybe<PointsHistoryFilterParameter>;
  /** Specifies whether multiple "filter" arguments should be combines with a logical AND or OR operation. Defaults to AND. */
  filterOperator?: Maybe<LogicalOperator>;
};

export type PointsHistorySortParameter = {
  id?: Maybe<SortOrder>;
  createdAt?: Maybe<SortOrder>;
  updatedAt?: Maybe<SortOrder>;
  sourceId?: Maybe<SortOrder>;
  points?: Maybe<SortOrder>;
  description?: Maybe<SortOrder>;
};

export type PointsProduct = Node & {
  __typename?: 'PointsProduct';
  id: Scalars['ID'];
  createdAt?: Maybe<Scalars['DateTime']>;
  updatedAt?: Maybe<Scalars['DateTime']>;
  product?: Maybe<Product>;
  productId?: Maybe<Scalars['ID']>;
  productType?: Maybe<PointsProductType>;
  startTime?: Maybe<Scalars['DateTime']>;
  endTime?: Maybe<Scalars['DateTime']>;
  status?: Maybe<ActivityStatus>;
  allowPurchaseAtOriginalPrice?: Maybe<Scalars['Boolean']>;
  exchangeConditionType?: Maybe<ExchangeConditionType>;
  exchangeLimit?: Maybe<Scalars['Int']>;
  stackingDiscountSwitch?: Maybe<Scalars['Boolean']>;
  stackingPromotionTypes?: Maybe<Array<Maybe<PromotionType>>>;
  pointsProductSkus?: Maybe<Array<Maybe<PointsProductSku>>>;
  pointExchange?: Maybe<PointExchange>;
};

export type PointsProductFilterParameter = {
  id?: Maybe<IdOperators>;
  createdAt?: Maybe<DateOperators>;
  updatedAt?: Maybe<DateOperators>;
  productId?: Maybe<IdOperators>;
  productType?: Maybe<StringOperators>;
  startTime?: Maybe<DateOperators>;
  endTime?: Maybe<DateOperators>;
  status?: Maybe<StringOperators>;
  allowPurchaseAtOriginalPrice?: Maybe<BooleanOperators>;
  exchangeConditionType?: Maybe<StringOperators>;
  exchangeLimit?: Maybe<NumberOperators>;
  stackingDiscountSwitch?: Maybe<BooleanOperators>;
};

export type PointsProductInput = {
  id?: Maybe<Scalars['ID']>;
  productId: Scalars['ID'];
  productType: PointsProductType;
  startTime: Scalars['DateTime'];
  endTime: Scalars['DateTime'];
  status?: Maybe<ActivityStatus>;
  allowPurchaseAtOriginalPrice?: Maybe<Scalars['Boolean']>;
  exchangeConditionType?: Maybe<ExchangeConditionType>;
  exchangeLimit?: Maybe<Scalars['Int']>;
  stackingDiscountSwitch?: Maybe<Scalars['Boolean']>;
  stackingPromotionTypes?: Maybe<Array<Maybe<PromotionType>>>;
  pointsProductSkus: Array<Maybe<PointsProductSkuInput>>;
};

export type PointsProductList = PaginatedList & {
  __typename?: 'PointsProductList';
  items: Array<PointsProduct>;
  totalItems: Scalars['Int'];
};

export type PointsProductListOptions = {
  /** Skips the first n results, for use in pagination */
  skip?: Maybe<Scalars['Int']>;
  /** Takes n results, for use in pagination */
  take?: Maybe<Scalars['Int']>;
  /** Specifies which properties to sort the results by */
  sort?: Maybe<PointsProductSortParameter>;
  /** Allows the results to be filtered */
  filter?: Maybe<PointsProductFilterParameter>;
  /** Specifies whether multiple "filter" arguments should be combines with a logical AND or OR operation. Defaults to AND. */
  filterOperator?: Maybe<LogicalOperator>;
};

export type PointsProductSku = Node & {
  __typename?: 'PointsProductSku';
  id: Scalars['ID'];
  createdAt?: Maybe<Scalars['DateTime']>;
  updatedAt?: Maybe<Scalars['DateTime']>;
  productVariant?: Maybe<ProductVariant>;
  productVariantId?: Maybe<Scalars['ID']>;
  pointsProduct?: Maybe<PointsProduct>;
  pointsProductId?: Maybe<Scalars['ID']>;
  enabled?: Maybe<Scalars['Boolean']>;
  points?: Maybe<Scalars['Int']>;
  cash?: Maybe<Scalars['Int']>;
  exchangeTotal?: Maybe<Scalars['Int']>;
  exchangeCount?: Maybe<Scalars['Int']>;
  exchangeableCount?: Maybe<Scalars['Int']>;
};

export type PointsProductSkuInput = {
  id?: Maybe<Scalars['ID']>;
  productVariantId: Scalars['ID'];
  enabled: Scalars['Boolean'];
  points?: Maybe<Scalars['Int']>;
  cash?: Maybe<Scalars['Int']>;
  exchangeTotal?: Maybe<Scalars['Int']>;
};

export type PointsProductSortParameter = {
  id?: Maybe<SortOrder>;
  createdAt?: Maybe<SortOrder>;
  updatedAt?: Maybe<SortOrder>;
  productId?: Maybe<SortOrder>;
  startTime?: Maybe<SortOrder>;
  endTime?: Maybe<SortOrder>;
  exchangeLimit?: Maybe<SortOrder>;
};

export enum PointsProductType {
  Normal = 'normal',
  Coupon = 'coupon',
  MembershipCard = 'membershipCard'
}

export enum PointsSourceType {
  Order = 'order',
  AfterSale = 'afterSale',
  YouZanSync = 'youZanSync',
  PointsExchange = 'pointsExchange',
  PointsRefund = 'pointsRefund',
  VoluntaryRefund = 'voluntaryRefund',
  CheckinReward = 'checkinReward',
  AdminManualOperation = 'adminManualOperation'
}

export type Popup = Node & {
  __typename?: 'Popup';
  id: Scalars['ID'];
  createdAt?: Maybe<Scalars['DateTime']>;
  updatedAt?: Maybe<Scalars['DateTime']>;
  title?: Maybe<Scalars['String']>;
  pictureResource?: Maybe<PictureResource>;
  deliveryStartTime?: Maybe<Scalars['Time']>;
  deliveryEndTime?: Maybe<Scalars['Time']>;
  pushFrequency?: Maybe<PushFrequency>;
};

export type PopupInput = {
  id?: Maybe<Scalars['ID']>;
  title?: Maybe<Scalars['String']>;
  pictureResource?: Maybe<PictureResourceInput>;
  deliveryStartTime?: Maybe<Scalars['Time']>;
  deliveryEndTime?: Maybe<Scalars['Time']>;
  pushFrequency?: Maybe<PushFrequency>;
};

export type PreferentialContent = {
  __typename?: 'PreferentialContent';
  preferentialType: PreferentialType;
  minimum?: Maybe<Scalars['Int']>;
  discount?: Maybe<Scalars['Int']>;
  maximumOffer?: Maybe<Scalars['Int']>;
  includingDiscountProducts?: Maybe<Scalars['Boolean']>;
};

export type PreferentialContentInput = {
  preferentialType: PreferentialType;
  minimum?: Maybe<Scalars['Int']>;
  discount?: Maybe<Scalars['Int']>;
  maximumOffer?: Maybe<Scalars['Int']>;
  includingDiscountProducts?: Maybe<Scalars['Boolean']>;
};

export enum PreferentialType {
  Satisfy = 'satisfy',
  ThresholdFree = 'thresholdFree'
}

export type PresentedCoupon = {
  __typename?: 'PresentedCoupon';
  couponId: Scalars['ID'];
  presentedCount: Scalars['Int'];
};

export type PresentedCouponInput = {
  couponId: Scalars['ID'];
  presentedCount: Scalars['Int'];
};

/** The price range where the result has more than one price */
export type PriceRange = {
  __typename?: 'PriceRange';
  min: Scalars['Money'];
  max: Scalars['Money'];
};

export enum PrizeType {
  Points = 'points',
  Coupon = 'coupon'
}

export type Product = Node & {
  __typename?: 'Product';
  id: Scalars['ID'];
  createdAt: Scalars['DateTime'];
  updatedAt: Scalars['DateTime'];
  languageCode: LanguageCode;
  name: Scalars['String'];
  slug: Scalars['String'];
  description: Scalars['String'];
  featuredAsset?: Maybe<Asset>;
  assets: Array<Asset>;
  /** Returns all ProductVariants */
  variants: Array<ProductVariant>;
  /** Returns a paginated, sortable, filterable list of ProductVariants */
  variantList: ProductVariantList;
  optionGroups: Array<ProductOptionGroup>;
  facetValues: Array<FacetValue>;
  translations: Array<ProductTranslation>;
  collections: Array<Collection>;
  enabled: Scalars['Boolean'];
  participatingActivities?: Maybe<Array<Maybe<Promotion>>>;
  productPurchasePermission?: Maybe<ProductPurchasePermission>;
  memberPriceActivityAmount?: Maybe<MemberPriceActivityAmount>;
  productTotalStock?: Maybe<Scalars['Int']>;
  reviewAvg: Scalars['Float'];
  reviews: ReviewProductList;
  /** Use this in your Storefront to show in product page if user can create a review */
  canReview?: Maybe<Scalars['Boolean']>;
  pointsExchangeInfo?: Maybe<PointsProduct>;
  customFields?: Maybe<ProductCustomFields>;
};


export type ProductVariantListArgs = {
  options?: Maybe<ProductVariantListOptions>;
};


export type ProductReviewsArgs = {
  options?: Maybe<ReviewProductListOptions>;
};

export type ProductActivities = Coupon | PurchasePremium | DiscountActivity | FullDiscountPresent | PackageDiscount | SelectiveGiftActivity | ShoppingCreditsClaimActivity | ShoppingCreditsDeductionActivity;

export type ProductCategory = {
  __typename?: 'ProductCategory';
  banner?: Maybe<PictureResource>;
  title?: Maybe<Scalars['String']>;
  subCategory?: Maybe<Array<Maybe<SubCategory>>>;
};

export type ProductCategoryInput = {
  banner?: Maybe<PictureResourceInput>;
  title?: Maybe<Scalars['String']>;
  subCategory?: Maybe<Array<Maybe<SubCategoryInput>>>;
};

export type ProductCustomFields = {
  __typename?: 'ProductCustomFields';
  particulars?: Maybe<Array<Scalars['String']>>;
  price?: Maybe<Scalars['Int']>;
  markingPrice?: Maybe<Scalars['Int']>;
  hidden?: Maybe<Scalars['Boolean']>;
  freeGift?: Maybe<Scalars['Boolean']>;
  putOnSaleType?: Maybe<Scalars['String']>;
  putOnSaleTime?: Maybe<Scalars['DateTime']>;
  timedTakedown?: Maybe<Scalars['Boolean']>;
  takedownTime?: Maybe<Scalars['DateTime']>;
  smallProgramQRCodeLink?: Maybe<Scalars['String']>;
  salesVolume?: Maybe<Scalars['Int']>;
  limitType?: Maybe<Scalars['String']>;
  limitCount?: Maybe<Scalars['Int']>;
  productType?: Maybe<Scalars['String']>;
  virtualTargetType?: Maybe<Scalars['String']>;
  isSupportAfterSale?: Maybe<Scalars['Boolean']>;
  isHiddenCart?: Maybe<Scalars['Boolean']>;
  subscriptionPlan?: Maybe<SubscriptionPlan>;
  unit?: Maybe<Scalars['String']>;
  isVipProduct?: Maybe<Scalars['Boolean']>;
};

export enum ProductExclusionGroupType {
  MustCombineWithNonGroup = 'mustCombineWithNonGroup',
  CanCombineWithGroup = 'canCombineWithGroup',
  NoCombineWithGroup = 'noCombineWithGroup'
}

export type ProductFilterParameter = {
  id?: Maybe<IdOperators>;
  createdAt?: Maybe<DateOperators>;
  updatedAt?: Maybe<DateOperators>;
  languageCode?: Maybe<StringOperators>;
  name?: Maybe<StringOperators>;
  slug?: Maybe<StringOperators>;
  description?: Maybe<StringOperators>;
  enabled?: Maybe<BooleanOperators>;
  productTotalStock?: Maybe<NumberOperators>;
  reviewAvg?: Maybe<NumberOperators>;
  canReview?: Maybe<BooleanOperators>;
  particulars?: Maybe<StringListOperators>;
  price?: Maybe<NumberOperators>;
  markingPrice?: Maybe<NumberOperators>;
  hidden?: Maybe<BooleanOperators>;
  freeGift?: Maybe<BooleanOperators>;
  putOnSaleType?: Maybe<StringOperators>;
  putOnSaleTime?: Maybe<DateOperators>;
  timedTakedown?: Maybe<BooleanOperators>;
  takedownTime?: Maybe<DateOperators>;
  smallProgramQRCodeLink?: Maybe<StringOperators>;
  salesVolume?: Maybe<NumberOperators>;
  limitType?: Maybe<StringOperators>;
  limitCount?: Maybe<NumberOperators>;
  productType?: Maybe<StringOperators>;
  virtualTargetType?: Maybe<StringOperators>;
  isSupportAfterSale?: Maybe<BooleanOperators>;
  isHiddenCart?: Maybe<BooleanOperators>;
  unit?: Maybe<StringOperators>;
  isVipProduct?: Maybe<BooleanOperators>;
};

export type ProductGroup = {
  __typename?: 'ProductGroup';
  menuCollection: Array<Maybe<MenuCollection>>;
  groupType: ProductGroupType;
};

export type ProductGroupInput = {
  menuCollection: Array<Maybe<MenuCollectionInput>>;
  groupType: ProductGroupType;
};

export enum ProductGroupType {
  TopGroup = 'topGroup',
  LeftGroup = 'leftGroup'
}

export type ProductItems = {
  __typename?: 'ProductItems';
  showType?: Maybe<ProductItemsType>;
  ids?: Maybe<Array<Maybe<Scalars['ID']>>>;
};

export type ProductItemsInput = {
  showType?: Maybe<ProductItemsType>;
  ids?: Maybe<Array<Maybe<Scalars['ID']>>>;
};

export enum ProductItemsType {
  OneColumn = 'oneColumn',
  TowColumn = 'towColumn'
}

export type ProductList = PaginatedList & {
  __typename?: 'ProductList';
  items: Array<Product>;
  totalItems: Scalars['Int'];
};

export type ProductListOptions = {
  /** Skips the first n results, for use in pagination */
  skip?: Maybe<Scalars['Int']>;
  /** Takes n results, for use in pagination */
  take?: Maybe<Scalars['Int']>;
  /** Specifies which properties to sort the results by */
  sort?: Maybe<ProductSortParameter>;
  /** Allows the results to be filtered */
  filter?: Maybe<ProductFilterParameter>;
  /** Specifies whether multiple "filter" arguments should be combines with a logical AND or OR operation. Defaults to AND. */
  filterOperator?: Maybe<LogicalOperator>;
};

export type ProductOption = Node & {
  __typename?: 'ProductOption';
  id: Scalars['ID'];
  createdAt: Scalars['DateTime'];
  updatedAt: Scalars['DateTime'];
  languageCode: LanguageCode;
  code: Scalars['String'];
  name: Scalars['String'];
  groupId: Scalars['ID'];
  group: ProductOptionGroup;
  translations: Array<ProductOptionTranslation>;
  customFields?: Maybe<ProductOptionCustomFields>;
};

export type ProductOptionCustomFields = {
  __typename?: 'ProductOptionCustomFields';
  priority?: Maybe<Scalars['Int']>;
};

export type ProductOptionGroup = Node & {
  __typename?: 'ProductOptionGroup';
  id: Scalars['ID'];
  createdAt: Scalars['DateTime'];
  updatedAt: Scalars['DateTime'];
  languageCode: LanguageCode;
  code: Scalars['String'];
  name: Scalars['String'];
  options: Array<ProductOption>;
  translations: Array<ProductOptionGroupTranslation>;
  customFields?: Maybe<ProductOptionGroupCustomFields>;
};

export type ProductOptionGroupCustomFields = {
  __typename?: 'ProductOptionGroupCustomFields';
  priority?: Maybe<Scalars['Int']>;
};

export type ProductOptionGroupTranslation = {
  __typename?: 'ProductOptionGroupTranslation';
  id: Scalars['ID'];
  createdAt: Scalars['DateTime'];
  updatedAt: Scalars['DateTime'];
  languageCode: LanguageCode;
  name: Scalars['String'];
};

export type ProductOptionTranslation = {
  __typename?: 'ProductOptionTranslation';
  id: Scalars['ID'];
  createdAt: Scalars['DateTime'];
  updatedAt: Scalars['DateTime'];
  languageCode: LanguageCode;
  name: Scalars['String'];
};

export type ProductProbability = {
  __typename?: 'ProductProbability';
  blindBoxItemId?: Maybe<Scalars['ID']>;
  targetProbability?: Maybe<Scalars['Int']>;
};

export type ProductProbabilityInput = {
  blindBoxItemId?: Maybe<Scalars['ID']>;
  targetProbability?: Maybe<Scalars['Int']>;
};

export type ProductPurchasePermission = Node & {
  __typename?: 'ProductPurchasePermission';
  id: Scalars['ID'];
  productId?: Maybe<Scalars['ID']>;
  isMembershipPlanPurchase?: Maybe<Scalars['Boolean']>;
  membershipPlans?: Maybe<Array<Maybe<MembershipPlan>>>;
  guideMembershipPlanId?: Maybe<Scalars['ID']>;
  guideMembershipPlan?: Maybe<MembershipPlan>;
};

export type ProductPurchasePermissionInput = {
  productId: Scalars['ID'];
  isMembershipPlanPurchase: Scalars['Boolean'];
  guideMembershipPlanId?: Maybe<Scalars['ID']>;
  membershipPlanIds: Array<Maybe<Scalars['ID']>>;
};

export type ProductRestrictions = Node & {
  __typename?: 'ProductRestrictions';
  id: Scalars['ID'];
  createdAt?: Maybe<Scalars['DateTime']>;
  updatedAt?: Maybe<Scalars['DateTime']>;
  isGlobalRestriction?: Maybe<Scalars['Boolean']>;
  products?: Maybe<Array<Maybe<Product>>>;
  restrictedRegions: Array<RestrictedRegion>;
  restrictionType: RestrictionType;
};

export type ProductRestrictionsInput = {
  id?: Maybe<Scalars['ID']>;
  isGlobalRestriction?: Maybe<Scalars['Boolean']>;
  productIds?: Maybe<Array<Maybe<Scalars['ID']>>>;
  restrictedRegions: Array<RestrictedRegionInput>;
  restrictionType: RestrictionType;
};

export type ProductSortParameter = {
  id?: Maybe<SortOrder>;
  createdAt?: Maybe<SortOrder>;
  updatedAt?: Maybe<SortOrder>;
  name?: Maybe<SortOrder>;
  slug?: Maybe<SortOrder>;
  description?: Maybe<SortOrder>;
  productTotalStock?: Maybe<SortOrder>;
  reviewAvg?: Maybe<SortOrder>;
  price?: Maybe<SortOrder>;
  markingPrice?: Maybe<SortOrder>;
  hidden?: Maybe<SortOrder>;
  freeGift?: Maybe<SortOrder>;
  putOnSaleType?: Maybe<SortOrder>;
  putOnSaleTime?: Maybe<SortOrder>;
  timedTakedown?: Maybe<SortOrder>;
  takedownTime?: Maybe<SortOrder>;
  smallProgramQRCodeLink?: Maybe<SortOrder>;
  salesVolume?: Maybe<SortOrder>;
  limitType?: Maybe<SortOrder>;
  limitCount?: Maybe<SortOrder>;
  productType?: Maybe<SortOrder>;
  virtualTargetType?: Maybe<SortOrder>;
  isSupportAfterSale?: Maybe<SortOrder>;
  isHiddenCart?: Maybe<SortOrder>;
  subscriptionPlan?: Maybe<SortOrder>;
  unit?: Maybe<SortOrder>;
  isVipProduct?: Maybe<SortOrder>;
};

export type ProductStatistics = {
  __typename?: 'ProductStatistics';
  productId?: Maybe<Scalars['Int']>;
  visitorsCount?: Maybe<Scalars['Int']>;
  pageViews?: Maybe<Scalars['Int']>;
  addCartPeopleCount?: Maybe<Scalars['Int']>;
  addCartCount?: Maybe<Scalars['Int']>;
  numberOfPayers?: Maybe<Scalars['Int']>;
  conversionRateOfSingleProduct?: Maybe<Scalars['Float']>;
  numberOfPaidItems?: Maybe<Scalars['Int']>;
  paymentAmount?: Maybe<Scalars['Float']>;
  numberOfRefundItems?: Maybe<Scalars['Int']>;
  numberOfSuccessfulRefunds?: Maybe<Scalars['Int']>;
  numberOfSuccessfulRefundPrice?: Maybe<Scalars['Float']>;
  numberOfSuccessfulRefundOrders?: Maybe<Scalars['Int']>;
  numberOfSuccessfulRefundPeople?: Maybe<Scalars['Int']>;
};

export type ProductTranslation = {
  __typename?: 'ProductTranslation';
  id: Scalars['ID'];
  createdAt: Scalars['DateTime'];
  updatedAt: Scalars['DateTime'];
  languageCode: LanguageCode;
  name: Scalars['String'];
  slug: Scalars['String'];
  description: Scalars['String'];
};

export enum ProductType {
  Ordinary = 'ordinary',
  Virtual = 'virtual'
}

export type ProductVariant = Node & {
  __typename?: 'ProductVariant';
  id: Scalars['ID'];
  product: Product;
  productId: Scalars['ID'];
  createdAt: Scalars['DateTime'];
  updatedAt: Scalars['DateTime'];
  languageCode: LanguageCode;
  sku: Scalars['String'];
  name: Scalars['String'];
  featuredAsset?: Maybe<Asset>;
  assets: Array<Asset>;
  price: Scalars['Money'];
  currencyCode: CurrencyCode;
  priceWithTax: Scalars['Money'];
  stockLevel: Scalars['String'];
  taxRateApplied: TaxRate;
  taxCategory: TaxCategory;
  options: Array<ProductOption>;
  facetValues: Array<FacetValue>;
  translations: Array<ProductVariantTranslation>;
  trackInventory: GlobalFlag;
  stockOnHand: Scalars['Int'];
  stockAllocated: Scalars['Int'];
  outOfStockThreshold: Scalars['Int'];
  isThereAnyStock: Scalars['Boolean'];
  availableStock: Scalars['Int'];
  useGlobalOutOfStockThreshold: Scalars['Boolean'];
  virtualTarget?: Maybe<VirtualTarget>;
  exclusionGroupsType?: Maybe<ProductExclusionGroupType>;
  exclusionGroupsTypeName?: Maybe<Scalars['String']>;
  isCanPurchase?: Maybe<Scalars['Boolean']>;
  pointExchange?: Maybe<PointExchange>;
  customFields?: Maybe<ProductVariantCustomFields>;
};

export type ProductVariantCustomFields = {
  __typename?: 'ProductVariantCustomFields';
  costPrice?: Maybe<Scalars['Int']>;
  virtualTargetType?: Maybe<Scalars['String']>;
  virtualTargetId?: Maybe<Scalars['String']>;
  isShowBuyNow?: Maybe<Scalars['Boolean']>;
};

export type ProductVariantDataStatistics = {
  __typename?: 'ProductVariantDataStatistics';
  items: Array<ProductVariantStatistics>;
  totalItems: Scalars['Int'];
};

export type ProductVariantFilterParameter = {
  id?: Maybe<IdOperators>;
  productId?: Maybe<IdOperators>;
  createdAt?: Maybe<DateOperators>;
  updatedAt?: Maybe<DateOperators>;
  languageCode?: Maybe<StringOperators>;
  sku?: Maybe<StringOperators>;
  name?: Maybe<StringOperators>;
  price?: Maybe<NumberOperators>;
  currencyCode?: Maybe<StringOperators>;
  priceWithTax?: Maybe<NumberOperators>;
  stockLevel?: Maybe<StringOperators>;
  trackInventory?: Maybe<StringOperators>;
  stockOnHand?: Maybe<NumberOperators>;
  stockAllocated?: Maybe<NumberOperators>;
  outOfStockThreshold?: Maybe<NumberOperators>;
  isThereAnyStock?: Maybe<BooleanOperators>;
  availableStock?: Maybe<NumberOperators>;
  useGlobalOutOfStockThreshold?: Maybe<BooleanOperators>;
  exclusionGroupsType?: Maybe<StringOperators>;
  exclusionGroupsTypeName?: Maybe<StringOperators>;
  isCanPurchase?: Maybe<BooleanOperators>;
  costPrice?: Maybe<NumberOperators>;
  virtualTargetType?: Maybe<StringOperators>;
  virtualTargetId?: Maybe<StringOperators>;
  isShowBuyNow?: Maybe<BooleanOperators>;
};

export type ProductVariantList = PaginatedList & {
  __typename?: 'ProductVariantList';
  items: Array<ProductVariant>;
  totalItems: Scalars['Int'];
};

export type ProductVariantListOptions = {
  /** Skips the first n results, for use in pagination */
  skip?: Maybe<Scalars['Int']>;
  /** Takes n results, for use in pagination */
  take?: Maybe<Scalars['Int']>;
  /** Specifies which properties to sort the results by */
  sort?: Maybe<ProductVariantSortParameter>;
  /** Allows the results to be filtered */
  filter?: Maybe<ProductVariantFilterParameter>;
  /** Specifies whether multiple "filter" arguments should be combines with a logical AND or OR operation. Defaults to AND. */
  filterOperator?: Maybe<LogicalOperator>;
};

export type ProductVariantSortParameter = {
  id?: Maybe<SortOrder>;
  productId?: Maybe<SortOrder>;
  createdAt?: Maybe<SortOrder>;
  updatedAt?: Maybe<SortOrder>;
  sku?: Maybe<SortOrder>;
  name?: Maybe<SortOrder>;
  price?: Maybe<SortOrder>;
  priceWithTax?: Maybe<SortOrder>;
  stockLevel?: Maybe<SortOrder>;
  stockOnHand?: Maybe<SortOrder>;
  stockAllocated?: Maybe<SortOrder>;
  outOfStockThreshold?: Maybe<SortOrder>;
  availableStock?: Maybe<SortOrder>;
  exclusionGroupsTypeName?: Maybe<SortOrder>;
  costPrice?: Maybe<SortOrder>;
  virtualTargetType?: Maybe<SortOrder>;
  virtualTargetId?: Maybe<SortOrder>;
  isShowBuyNow?: Maybe<SortOrder>;
};

export type ProductVariantStatistics = {
  __typename?: 'ProductVariantStatistics';
  productVariantId?: Maybe<Scalars['ID']>;
  addCartPeopleCount?: Maybe<Scalars['Int']>;
  addCartCount?: Maybe<Scalars['Int']>;
  orderPeopleCount?: Maybe<Scalars['Int']>;
  orderCount?: Maybe<Scalars['Int']>;
  payPeopleCount?: Maybe<Scalars['Int']>;
  payCount?: Maybe<Scalars['Int']>;
  payPrice?: Maybe<Scalars['Float']>;
  productVariantName?: Maybe<Scalars['String']>;
  costPrice?: Maybe<Scalars['Float']>;
  price?: Maybe<Scalars['Float']>;
  deletedAt?: Maybe<Scalars['DateTime']>;
};

export type ProductVariantTranslation = {
  __typename?: 'ProductVariantTranslation';
  id: Scalars['ID'];
  createdAt: Scalars['DateTime'];
  updatedAt: Scalars['DateTime'];
  languageCode: LanguageCode;
  name: Scalars['String'];
};

export type ProgramLinkInput = {
  type: ShareType;
  id: Scalars['ID'];
  path?: Maybe<Scalars['String']>;
  isPreview?: Maybe<Scalars['Boolean']>;
};

export type PromCoupon = {
  __typename?: 'PromCoupon';
  couponId?: Maybe<Scalars['ID']>;
  selected?: Maybe<Scalars['Boolean']>;
  price?: Maybe<Scalars['Int']>;
  autoSelected?: Maybe<Scalars['Boolean']>;
  couponType?: Maybe<CouponType>;
};

export type PromLineResult = {
  __typename?: 'PromLineResult';
  promInstanceId?: Maybe<Scalars['ID']>;
  tags?: Maybe<Array<Maybe<Scalars['String']>>>;
  orderLines?: Maybe<Array<Maybe<PromOrderLine>>>;
  priority?: Maybe<Scalars['Int']>;
  type?: Maybe<Scalars['String']>;
  shouldGroup?: Maybe<Scalars['Boolean']>;
  description?: Maybe<Scalars['String']>;
  promTime?: Maybe<Scalars['String']>;
  promExpireTime?: Maybe<Scalars['DateTime']>;
  promContent?: Maybe<Scalars['String']>;
  promOverview?: Maybe<Scalars['String']>;
  discountType?: Maybe<Scalars['String']>;
  discountAmount?: Maybe<Scalars['Int']>;
  discountCount?: Maybe<Scalars['Int']>;
  meetCondition?: Maybe<Scalars['Boolean']>;
  superimposeType?: Maybe<SuperimposeType>;
  superimposeTypes?: Maybe<Array<Maybe<Scalars['String']>>>;
  gifts?: Maybe<Gift>;
  coupons?: Maybe<Array<Maybe<PromCoupon>>>;
  lackOfExchangeGoodsCoupons?: Maybe<Array<Maybe<PromCoupon>>>;
  totalPoints?: Maybe<Scalars['Int']>;
  orderLineBlindBoxMap?: Maybe<Array<Maybe<OrderLineBlindBoxMap>>>;
  shoppingCredits?: Maybe<Scalars['Int']>;
};

export type PromOrderLine = {
  __typename?: 'PromOrderLine';
  skuId?: Maybe<Scalars['ID']>;
  orderLineId?: Maybe<Scalars['ID']>;
  discountCount?: Maybe<Scalars['Int']>;
  discountAmount?: Maybe<Scalars['Int']>;
  displayInThisGroup?: Maybe<Scalars['Boolean']>;
  discount?: Maybe<Scalars['Int']>;
  points?: Maybe<Scalars['Int']>;
  cash?: Maybe<Scalars['Int']>;
  claimShoppingCredits?: Maybe<Scalars['Int']>;
};

export type PromResult = {
  __typename?: 'PromResult';
  orderId?: Maybe<Scalars['ID']>;
  discountAmount?: Maybe<Scalars['Int']>;
  promLineResults?: Maybe<Array<Maybe<PromLineResult>>>;
  orderLinePromResults?: Maybe<Array<Maybe<OrderLinePromResult>>>;
  leftOrderLines?: Maybe<Array<Maybe<LeftOrderLine>>>;
  discountByTypes?: Maybe<Array<Maybe<DiscountByType>>>;
  gifts?: Maybe<Array<Maybe<Gift>>>;
  coupons?: Maybe<Array<Maybe<PromCoupon>>>;
  disableMember?: Maybe<Scalars['Boolean']>;
  disableShoppingCredits?: Maybe<Scalars['Boolean']>;
  disableCoupon?: Maybe<Scalars['Boolean']>;
  surcharge?: Maybe<SurchargeResult>;
  orderTotalPrice?: Maybe<Scalars['Int']>;
  orderTotalPoints?: Maybe<Scalars['Int']>;
  shoppingCreditsClaim?: Maybe<Scalars['Int']>;
  shoppingCreditsDeduction?: Maybe<Scalars['Int']>;
};

export type Promotion = Node & {
  __typename?: 'Promotion';
  id: Scalars['ID'];
  createdAt: Scalars['DateTime'];
  updatedAt: Scalars['DateTime'];
  startsAt?: Maybe<Scalars['DateTime']>;
  endsAt?: Maybe<Scalars['DateTime']>;
  couponCode?: Maybe<Scalars['String']>;
  perCustomerUsageLimit?: Maybe<Scalars['Int']>;
  name: Scalars['String'];
  description: Scalars['String'];
  enabled: Scalars['Boolean'];
  conditions: Array<ConfigurableOperation>;
  actions: Array<ConfigurableOperation>;
  translations: Array<PromotionTranslation>;
  customFields?: Maybe<PromotionCustomFields>;
};

export enum PromotionConditionType {
  Amount = 'amount',
  Quantity = 'quantity'
}

export type PromotionCustomFields = {
  __typename?: 'PromotionCustomFields';
  type?: Maybe<Scalars['String']>;
  stackingDiscountSwitch?: Maybe<Scalars['Boolean']>;
  stackingPromotionTypes?: Maybe<Array<Scalars['String']>>;
  isAutomatic?: Maybe<Scalars['Boolean']>;
  activityName?: Maybe<Scalars['String']>;
};

export type PromotionList = PaginatedList & {
  __typename?: 'PromotionList';
  items: Array<Promotion>;
  totalItems: Scalars['Int'];
};

export type PromotionTranslation = {
  __typename?: 'PromotionTranslation';
  id: Scalars['ID'];
  createdAt: Scalars['DateTime'];
  updatedAt: Scalars['DateTime'];
  languageCode: LanguageCode;
  name: Scalars['String'];
  description: Scalars['String'];
};

export enum PromotionType {
  Member = 'member',
  PurchaseAtAPremium = 'purchaseAtAPremium',
  Coupon = 'coupon',
  DiscountActivity = 'discountActivity',
  FullDiscountPresent = 'fullDiscountPresent',
  AutomaticPromotion = 'automaticPromotion',
  PackageDiscount = 'packageDiscount',
  ActuallyPaid = 'actuallyPaid',
  MemberPrice = 'memberPrice',
  SelectiveGift = 'selectiveGift',
  Subscription = 'subscription',
  PaymentReward = 'paymentReward',
  PointsExchange = 'pointsExchange',
  BlindBox = 'blindBox',
  ShoppingCreditsClaim = 'shoppingCreditsClaim',
  ShoppingCreditsDeduction = 'shoppingCreditsDeduction'
}

export type Province = Region & Node & {
  __typename?: 'Province';
  id: Scalars['ID'];
  createdAt: Scalars['DateTime'];
  updatedAt: Scalars['DateTime'];
  languageCode: LanguageCode;
  code: Scalars['String'];
  type: Scalars['String'];
  name: Scalars['String'];
  enabled: Scalars['Boolean'];
  parent?: Maybe<Region>;
  parentId?: Maybe<Scalars['ID']>;
  translations: Array<RegionTranslation>;
  customFields?: Maybe<Scalars['JSON']>;
};

export type ProvinceList = PaginatedList & {
  __typename?: 'ProvinceList';
  items: Array<Province>;
  totalItems: Scalars['Int'];
};

export enum PurchasePattern {
  Ordinary = 'ordinary',
  Gift = 'gift',
  PurchasePremium = 'purchasePremium'
}

export type PurchasePremium = Node & {
  __typename?: 'PurchasePremium';
  id: Scalars['ID'];
  createdAt: Scalars['DateTime'];
  updatedAt: Scalars['DateTime'];
  name: Scalars['String'];
  displayName: Scalars['String'];
  remarks?: Maybe<Scalars['String']>;
  validityPeriod?: Maybe<ValidityPeriod>;
  state?: Maybe<CouponState>;
  minimum?: Maybe<Scalars['Int']>;
  applicableProduct?: Maybe<ApplicableProduct>;
  purchasePremiumProducts?: Maybe<Array<Maybe<PurchasePremiumProduct>>>;
  stackingDiscountSwitch?: Maybe<Scalars['Boolean']>;
  stackingPromotionTypes?: Maybe<Array<Maybe<PromotionType>>>;
  introduce?: Maybe<Scalars['String']>;
  activityContent?: Maybe<Array<Maybe<Scalars['String']>>>;
  activitySynopsis?: Maybe<Scalars['String']>;
  activitySuperposition?: Maybe<Scalars['String']>;
  promotion?: Maybe<Promotion>;
  statisticsData?: Maybe<StatisticsData>;
  whetherRestrictUsers?: Maybe<Scalars['Boolean']>;
  groupType?: Maybe<GroupType>;
  memberPlanIds?: Maybe<Array<Maybe<Scalars['ID']>>>;
};

export type PurchasePremiumFilterParameter = {
  id?: Maybe<IdOperators>;
  createdAt?: Maybe<DateOperators>;
  updatedAt?: Maybe<DateOperators>;
  name?: Maybe<StringOperators>;
  displayName?: Maybe<StringOperators>;
  remarks?: Maybe<StringOperators>;
  state?: Maybe<StringOperators>;
  minimum?: Maybe<NumberOperators>;
  stackingDiscountSwitch?: Maybe<BooleanOperators>;
  introduce?: Maybe<StringOperators>;
  activitySynopsis?: Maybe<StringOperators>;
  activitySuperposition?: Maybe<StringOperators>;
  whetherRestrictUsers?: Maybe<BooleanOperators>;
  groupType?: Maybe<StringOperators>;
};

export type PurchasePremiumInput = {
  id?: Maybe<Scalars['ID']>;
  name: Scalars['String'];
  remarks?: Maybe<Scalars['String']>;
  displayName: Scalars['String'];
  validityPeriod: ValidityPeriodInput;
  minimum: Scalars['Int'];
  applicableProduct: ApplicableProductInput;
  goodsForExchanges: Array<GoodsForExchangeInput>;
  stackingDiscountSwitch: Scalars['Boolean'];
  stackingPromotionTypes: Array<Maybe<PromotionType>>;
  introduce: Scalars['String'];
  whetherRestrictUsers?: Maybe<Scalars['Boolean']>;
  groupType?: Maybe<GroupType>;
  memberPlanIds?: Maybe<Array<Maybe<Scalars['ID']>>>;
};

export type PurchasePremiumList = PaginatedList & {
  __typename?: 'PurchasePremiumList';
  items: Array<PurchasePremium>;
  totalItems: Scalars['Int'];
};

export type PurchasePremiumListOptions = {
  /** Skips the first n results, for use in pagination */
  skip?: Maybe<Scalars['Int']>;
  /** Takes n results, for use in pagination */
  take?: Maybe<Scalars['Int']>;
  /** Specifies which properties to sort the results by */
  sort?: Maybe<PurchasePremiumSortParameter>;
  /** Allows the results to be filtered */
  filter?: Maybe<PurchasePremiumFilterParameter>;
  /** Specifies whether multiple "filter" arguments should be combines with a logical AND or OR operation. Defaults to AND. */
  filterOperator?: Maybe<LogicalOperator>;
};

export type PurchasePremiumProduct = Node & {
  __typename?: 'PurchasePremiumProduct';
  id: Scalars['ID'];
  createdAt: Scalars['DateTime'];
  updatedAt: Scalars['DateTime'];
  sort?: Maybe<Scalars['Int']>;
  price?: Maybe<Scalars['Int']>;
  enabled?: Maybe<Scalars['Boolean']>;
  product?: Maybe<Product>;
};

export type PurchasePremiumProductList = PaginatedList & {
  __typename?: 'PurchasePremiumProductList';
  items: Array<PurchasePremiumProduct>;
  totalItems: Scalars['Int'];
};

export type PurchasePremiumSortParameter = {
  id?: Maybe<SortOrder>;
  createdAt?: Maybe<SortOrder>;
  updatedAt?: Maybe<SortOrder>;
  name?: Maybe<SortOrder>;
  displayName?: Maybe<SortOrder>;
  remarks?: Maybe<SortOrder>;
  minimum?: Maybe<SortOrder>;
  introduce?: Maybe<SortOrder>;
  activitySynopsis?: Maybe<SortOrder>;
  activitySuperposition?: Maybe<SortOrder>;
};

export enum PushFrequency {
  Once = 'once',
  EveryTime = 'everyTime'
}

export enum PutOnSaleType {
  Immediate = 'immediate',
  Scheduled = 'scheduled',
  Manual = 'manual'
}

export type Query = {
  __typename?: 'Query';
  /** The active Channel */
  activeChannel: Channel;
  /** The active Customer */
  activeCustomer?: Maybe<Customer>;
  /**
   * The active Order. Will be `null` until an Order is created via `addItemToOrder`. Once an Order reaches the
   * state of `PaymentAuthorized` or `PaymentSettled`, then that Order is no longer considered "active" and this
   * query will once again return `null`.
   */
  activeOrder?: Maybe<Order>;
  /** An array of supported Countries */
  availableCountries: Array<Country>;
  /** A list of Collections available to the shop */
  collections: CollectionList;
  /** Returns a Collection either by its id or slug. If neither 'id' nor 'slug' is specified, an error will result. */
  collection?: Maybe<Collection>;
  /** Returns a list of eligible shipping methods based on the current active Order */
  eligibleShippingMethods: Array<ShippingMethodQuote>;
  /** Returns a list of payment methods and their eligibility based on the current active Order */
  eligiblePaymentMethods: Array<PaymentMethodQuote>;
  /** A list of Facets available to the shop */
  facets: FacetList;
  /** Returns a Facet by its id */
  facet?: Maybe<Facet>;
  /** Returns information about the current authenticated User */
  me?: Maybe<CurrentUser>;
  /** Returns the possible next states that the activeOrder can transition to */
  nextOrderStates: Array<Scalars['String']>;
  /**
   * Returns an Order based on the id. Note that in the Shop API, only orders belonging to the
   * currently-authenticated User may be queried.
   */
  order?: Maybe<Order>;
  /**
   * Returns an Order based on the order `code`. For guest Orders (i.e. Orders placed by non-authenticated Customers)
   * this query will only return the Order within 2 hours of the Order being placed. This allows an Order confirmation
   * screen to be shown immediately after completion of a guest checkout, yet prevents security risks of allowing
   * general anonymous access to Order data.
   */
  orderByCode?: Maybe<Order>;
  /** Get a Product either by id or slug. If neither 'id' nor 'slug' is specified, an error will result. */
  product?: Maybe<Product>;
  /** Get a list of Products */
  products: ProductList;
  /** Search Products based on the criteria set by the `SearchInput` */
  search: SearchResponse;
  getVirtualCurrencyBalance?: Maybe<Scalars['Int']>;
  getVirtualCurrencyHistory?: Maybe<VirtualCurrencyHistoryList>;
  /** A list of available products to user review */
  availableProductsToReview: ProductList;
  availableOrdersToReview?: Maybe<OrderList>;
  myReviewProduct?: Maybe<ReviewProduct>;
  myReviewsProduct: ReviewProductList;
  reviews: ReviewProductList;
  review?: Maybe<ReviewProduct>;
  blindBoxes: BlindBoxList;
  blindBox?: Maybe<BlindBox>;
  blindBoxActivities: BlindBoxActivityList;
  blindBoxActivity?: Maybe<BlindBoxActivity>;
  blindBoxActivityLimitConfig?: Maybe<BlindBoxActivityLimitConfig>;
  assistGifts: AssistGiftList;
  assistGift?: Maybe<AssistGift>;
  assistGiftConfigs: AssistGiftConfigList;
  assistGiftConfig?: Maybe<AssistGiftConfig>;
  blindBoxActivityPreviewProduct?: Maybe<BlindBoxPreviewProduct>;
  blindBoxUserRemainingCount?: Maybe<CustomerBlindBoxLimit>;
  blindBoxUserAssistRemainingCount?: Maybe<CustomerAssistLimit>;
  wishBlindBoxUserList?: Maybe<Array<Maybe<BlindBoxOpenRecord>>>;
  getBlindBoxOpenRecordByOrderCode?: Maybe<BlindBoxOpenRecord>;
  getBlindBoxBuyByOrderId?: Maybe<BlindBoxBuy>;
  getBlindBoxBuys?: Maybe<BlindBoxBuyList>;
  getBlindBoxBuy?: Maybe<BlindBoxBuy>;
  getAssistGiftRecord?: Maybe<AssistGiftRecord>;
  getNearestAssistBlindBox?: Maybe<BlindBoxBuy>;
  blindBoxActivityIsBooking?: Maybe<Scalars['Boolean']>;
  customerIsAssist?: Maybe<Scalars['Boolean']>;
  totalBlindBoxStatistics?: Maybe<BlindBoxTotalStatistics>;
  floatingWindow?: Maybe<FloatingWindow>;
  getFloatingWindowCouponRecord?: Maybe<Scalars['Boolean']>;
  checkinRewardsConfig?: Maybe<CheckinRewardsConfig>;
  checkinConfig?: Maybe<CheckinConfig>;
  consecutiveDays?: Maybe<ConsecutiveResult>;
  customerPrizeRecords?: Maybe<CustomerPrizeResult>;
  customerCheckinRecords?: Maybe<Array<Maybe<CustomerCheckinRecords>>>;
  pointsHistory?: Maybe<PointsHistoryList>;
  pointsConfig?: Maybe<PointsConfig>;
  getPointsProducts?: Maybe<PointsProductList>;
  pointsProducts?: Maybe<PointsProductList>;
  pointsProduct?: Maybe<PointsProduct>;
  activeProducts?: Maybe<ProductList>;
  exclusionGroups?: Maybe<ExclusionGroupList>;
  exclusionGroup?: Maybe<ExclusionGroup>;
  checkSKUIdInOtherExclusionGroup?: Maybe<Scalars['Boolean']>;
  couponBundle?: Maybe<CouponBundle>;
  couponBundles?: Maybe<CouponBundleList>;
  firstCustomerBenefit?: Maybe<FirstCustomerBenefit>;
  firstCustomerBenefits?: Maybe<FirstCustomerBenefitList>;
  productsSortByProductIds: ProductList;
  getProductVariants: ProductVariantList;
  productVariantToMembershipId?: Maybe<ProductVariant>;
  getRegionsJson?: Maybe<Array<Maybe<LocationData>>>;
  areAllEligibleProductsInactiveOrSoldOut?: Maybe<Scalars['Boolean']>;
  getOrderTypeByPaymentCode?: Maybe<OrderTypeResult>;
  desDecode?: Maybe<ShareData>;
  generateH5Link?: Maybe<H5LineInfo>;
  getUMengConfig?: Maybe<UMengConfig>;
  generateSmallProgramLink?: Maybe<Scalars['String']>;
  generateSmallProgramQRCodeLink?: Maybe<Scalars['String']>;
  banners?: Maybe<BannerList>;
  banner?: Maybe<Banner>;
  customPages?: Maybe<CustomPageList>;
  customPage?: Maybe<CustomPage>;
  seriesProducts?: Maybe<ProductList>;
  hotWords?: Maybe<HotWordList>;
  hotWord?: Maybe<HotWord>;
  announcements?: Maybe<AnnouncementList>;
  announcement?: Maybe<Announcement>;
  getAnnouncement?: Maybe<Announcement>;
  couponAvailableCount?: Maybe<Scalars['Int']>;
  coupons?: Maybe<CouponList>;
  getAvailableCouponList?: Maybe<CouponList>;
  coupon?: Maybe<Coupon>;
  getCouponHold?: Maybe<CouponHold>;
  getActiveCoupons?: Maybe<CouponUsability>;
  userCoupons?: Maybe<UserCouponList>;
  userCoupon?: Maybe<UserCoupon>;
  couponHolder?: Maybe<UserCoupon>;
  couponDataStatistic?: Maybe<CouponDataStatistic>;
  purchasePremiums?: Maybe<PurchasePremiumList>;
  purchasePremium?: Maybe<PurchasePremium>;
  distributorsGroups?: Maybe<DistributorGroupList>;
  distributorsGroupsByDay?: Maybe<DistributorGroupList>;
  distributors?: Maybe<DistributorList>;
  distributorsByDay?: Maybe<DistributorList>;
  distributor?: Maybe<Distributor>;
  distributorSharing?: Maybe<Scalars['String']>;
  getH5ShareLink?: Maybe<Scalars['String']>;
  getDistributor?: Maybe<Distributor>;
  discountActivities?: Maybe<DiscountActivityList>;
  discountActivity?: Maybe<DiscountActivity>;
  fullDiscountPresents?: Maybe<FullDiscountPresentList>;
  fullDiscountPresent?: Maybe<FullDiscountPresent>;
  selectiveGiftActivities?: Maybe<SelectiveGiftActivityList>;
  selectiveGiftActivity?: Maybe<SelectiveGiftActivity>;
  freeGifts?: Maybe<FreeGiftList>;
  freeGift?: Maybe<FreeGift>;
  productActivitiesFindOne?: Maybe<ProductActivities>;
  productActivitiesFindAll?: Maybe<Array<Maybe<ProductActivities>>>;
  activityUsableProducts?: Maybe<ProductList>;
  settings?: Maybe<Array<Maybe<Setting>>>;
  freeGiftByOrder?: Maybe<Array<Maybe<FreeGift>>>;
  orderPromotionResult?: Maybe<Array<Maybe<OrderPromotionResult>>>;
  promotionResultByOrderId?: Maybe<OrderPromotionResult>;
  getRefundableAmount?: Maybe<Scalars['Int']>;
  getRefundableAmountByOrderId?: Maybe<Array<Maybe<OrderLinesRefundable>>>;
  markUpByOrder?: Maybe<Array<Maybe<PurchasePremium>>>;
  merchantVoluntaryRefundByOrder?: Maybe<Array<Maybe<MerchantVoluntaryRefund>>>;
  dataPanel?: Maybe<Panel>;
  groupCustomers?: Maybe<GroupCustomerList>;
  groupCustomer?: Maybe<GroupCustomer>;
  groupCustomerCount?: Maybe<Scalars['Int']>;
  operationPlans?: Maybe<OperationPlanList>;
  operationPlan?: Maybe<OperationPlan>;
  operationPlanCustomers?: Maybe<OperationPlanCustomerList>;
  getSubscribeMessageTemplateId?: Maybe<TemplateInfo>;
  packageDiscounts?: Maybe<PackageDiscountList>;
  packageDiscount?: Maybe<PackageDiscount>;
  transactionCustomerStatistics?: Maybe<CustomerStatistic>;
  getMatomoSiteId?: Maybe<Scalars['String']>;
  customPageDataStatistics?: Maybe<CustomPageStatisticsList>;
  memberPrices?: Maybe<MemberPriceList>;
  memberPrice?: Maybe<MemberPrice>;
  memberPriceProducts?: Maybe<MemberPriceProductList>;
  memberPriceProductVariants?: Maybe<MemberPriceProductVariantList>;
  getOrderIsIncludeMemberPrice?: Maybe<Scalars['Boolean']>;
  giftCardOrders?: Maybe<GiftCardOrderList>;
  giftCardReturnOrders?: Maybe<GiftCardReturnList>;
  paymentRewardActivities?: Maybe<PaymentRewardActivityList>;
  paymentRewardActivity?: Maybe<PaymentRewardActivity>;
  shareSettings?: Maybe<Array<Maybe<ShareSetting>>>;
  getShoppingCreditsConfig?: Maybe<ShoppingCreditsConfig>;
  shoppingCreditsClaimActivity?: Maybe<ShoppingCreditsClaimActivity>;
  shoppingCreditsClaimActivities?: Maybe<ShoppingCreditsClaimActivityList>;
  shoppingCreditsDeductionActivities?: Maybe<ShoppingCreditsDeductionActivityList>;
  shoppingCreditsDeductionActivity?: Maybe<ShoppingCreditsDeductionActivity>;
  shoppingCreditsClaimAndDeductionAmount?: Maybe<Array<Maybe<OrderShoppingCredits>>>;
  personalCenter?: Maybe<PersonalCenter>;
  activityCountdowns?: Maybe<ActivityCountdownList>;
  activityCountdown?: Maybe<ActivityCountdown>;
  activityCountdownByProduct?: Maybe<ActivityCountdown>;
  activityCountdownByCart?: Maybe<CartActivityCountdown>;
  checkProductInSameExclusionGroup?: Maybe<Scalars['Boolean']>;
  checkProductCanCheckout?: Maybe<CheckExclusionResult>;
  getActiveFirstCustomerBenefit?: Maybe<FirstCustomerBenefit>;
  getFirstCustomerBenefitRecord?: Maybe<Array<Maybe<AvailableCoupon>>>;
  getReceivedCouponBundles?: Maybe<Array<Maybe<AvailableCoupon>>>;
  distributorBindingMembers?: Maybe<DistributorBindingMemberList>;
  distributorCenter?: Maybe<DistributorCenterData>;
  distributorPerformance?: Maybe<DistributorStatistics>;
  distributorsPromoteOrders?: Maybe<DistributorOrderList>;
  distributorBindingCustomers?: Maybe<DistributorBindingList>;
  distributorBindingCustomerDetail?: Maybe<DistributorBinding>;
  distributorShareProducts?: Maybe<DistributorProductRecordList>;
  orderPaymentRewardCoupons?: Maybe<OrderPaymentRewardCouponList>;
  orders: OrderList;
  subscriptionsTotal?: Maybe<SubscriptionTotal>;
  subscriptionPlan?: Maybe<Array<Maybe<SubscriptionPlan>>>;
  subscriptions?: Maybe<SubscriptionList>;
  subscriptionByOrderId?: Maybe<Subscription>;
  subscription?: Maybe<Subscription>;
  subscriptionOperations?: Maybe<Array<Maybe<SubscriptionOperation>>>;
  subscriptionsUnderway?: Maybe<Array<Maybe<Subscription>>>;
  orderRefunds?: Maybe<OrderRefundList>;
  orderRefund?: Maybe<OrderRefund>;
  logistics?: Maybe<Logistics>;
  getActiveOrderByType?: Maybe<Order>;
  newProducts: ProductList;
  getPurchaseQuantity?: Maybe<Scalars['Int']>;
  getExchangeableQuantity?: Maybe<Scalars['Int']>;
  eligibleShippingMethodsByOrderType: Array<ShippingMethodQuote>;
  getShoppingCart?: Maybe<ShoppingCart>;
  getNumber?: Maybe<Scalars['Int']>;
  getOrder?: Maybe<Order>;
  getOrderNull?: Maybe<Order>;
  getSubscriptRefundableAmount?: Maybe<Scalars['Float']>;
  weChatConfigs?: Maybe<WeChatConfigList>;
  getOpenIdByJsCode?: Maybe<Scalars['String']>;
  compActivity?: Maybe<CompActivity>;
  compRecords?: Maybe<CompRecordList>;
  compRecord?: Maybe<CompRecord>;
  compHandlers?: Maybe<Array<Maybe<HandlerInfo>>>;
  subscriptionCompRecords?: Maybe<CompRecordList>;
  afterSale?: Maybe<AfterSale>;
  afterSales?: Maybe<AfterSaleList>;
  afterSaleByOrderId?: Maybe<AfterSaleList>;
  channelAddresses?: Maybe<ChannelAddressList>;
  pendingOrdersNumber?: Maybe<PendingOrdersNum>;
  newOrders?: Maybe<OrderList>;
  getUserMember?: Maybe<Member>;
  membershipPlans?: Maybe<MembershipPlanList>;
  membershipPlan?: Maybe<MembershipPlan>;
  membershipOrders?: Maybe<MembershipOrderList>;
  membershipOrder?: Maybe<MembershipOrder>;
  members?: Maybe<MemberList>;
  member?: Maybe<Member>;
  maxDiscountMembershipPlan?: Maybe<MembershipPlan>;
  giftCards?: Maybe<GiftCardList>;
  giftCard?: Maybe<GiftCard>;
  userGiftCard?: Maybe<GiftCardOrder>;
  forumTags: ForumTagList;
  forumTag?: Maybe<ForumTag>;
  forumActivities: ForumActivityList;
  forumActivity?: Maybe<ForumActivity>;
  forumCustomers: ForumCustomerList;
  forumCustomer?: Maybe<ForumCustomer>;
  forumPosts: ForumPostList;
  forumPost?: Maybe<ForumPost>;
  getUnAuditForumPosts: ForumPostList;
  getHotForumPosts?: Maybe<ForumPostList>;
  getNewForumPosts?: Maybe<ForumPostList>;
  getForumTopicByPid?: Maybe<ForumPost>;
  getForumReviews?: Maybe<ForumReviewList>;
  getHotForumPostsByTag?: Maybe<ForumPostList>;
  getNewForumPostsByTag?: Maybe<ForumPostList>;
  getHotForumPostsByActivity?: Maybe<ForumPostList>;
  getNewForumPostsByActivity?: Maybe<ForumPostList>;
  getHotForumPostsByTagHash?: Maybe<ForumPostList>;
  forumNotifications?: Maybe<ForumNotificationList>;
  hasUnreadNotifications?: Maybe<Scalars['Boolean']>;
  getMyForumPostCount?: Maybe<MyForumPostCount>;
  getMyForumPosts?: Maybe<ForumPostList>;
  forumReviews: ForumReviewList;
  forumReview?: Maybe<ForumReview>;
  getForumCustomer?: Maybe<ForumCustomer>;
  wangDianTong?: Maybe<Scalars['String']>;
  wangDianTongConfig?: Maybe<WangDianTongConfig>;
};


export type QueryCollectionsArgs = {
  options?: Maybe<CollectionListOptions>;
};


export type QueryCollectionArgs = {
  id?: Maybe<Scalars['ID']>;
  slug?: Maybe<Scalars['String']>;
};


export type QueryFacetsArgs = {
  options?: Maybe<FacetListOptions>;
};


export type QueryFacetArgs = {
  id: Scalars['ID'];
};


export type QueryOrderArgs = {
  id: Scalars['ID'];
};


export type QueryOrderByCodeArgs = {
  code: Scalars['String'];
};


export type QueryProductArgs = {
  id?: Maybe<Scalars['ID']>;
  slug?: Maybe<Scalars['String']>;
};


export type QueryProductsArgs = {
  options?: Maybe<ProductListOptions>;
};


export type QuerySearchArgs = {
  input: SearchInput;
};


export type QueryGetVirtualCurrencyBalanceArgs = {
  virtualCurrencyCode: VirtualCurrencyCode;
};


export type QueryGetVirtualCurrencyHistoryArgs = {
  virtualCurrencyCode: VirtualCurrencyCode;
  options?: Maybe<VirtualCurrencyHistoryListOptions>;
};


export type QueryAvailableProductsToReviewArgs = {
  options?: Maybe<ProductListOptions>;
};


export type QueryAvailableOrdersToReviewArgs = {
  options?: Maybe<OrderListOptions>;
};


export type QueryMyReviewProductArgs = {
  id: Scalars['ID'];
};


export type QueryMyReviewsProductArgs = {
  options?: Maybe<ReviewProductListOptions>;
};


export type QueryReviewsArgs = {
  productId: Scalars['ID'];
  options?: Maybe<ReviewProductListOptions>;
};


export type QueryReviewArgs = {
  reviewId: Scalars['ID'];
};


export type QueryBlindBoxesArgs = {
  options?: Maybe<BlindBoxListOptions>;
};


export type QueryBlindBoxArgs = {
  blindBoxId: Scalars['ID'];
};


export type QueryBlindBoxActivitiesArgs = {
  options?: Maybe<BlindBoxActivityListOptions>;
};


export type QueryBlindBoxActivityArgs = {
  blindBoxActivityId: Scalars['ID'];
};


export type QueryAssistGiftsArgs = {
  options?: Maybe<AssistGiftListOptions>;
};


export type QueryAssistGiftArgs = {
  assistGiftId: Scalars['ID'];
};


export type QueryAssistGiftConfigsArgs = {
  options?: Maybe<AssistGiftConfigListOptions>;
};


export type QueryAssistGiftConfigArgs = {
  assistGiftConfigId?: Maybe<Scalars['ID']>;
};


export type QueryBlindBoxActivityPreviewProductArgs = {
  blindBoxId: Scalars['ID'];
  blindBoxActivityId: Scalars['ID'];
};


export type QueryGetBlindBoxOpenRecordByOrderCodeArgs = {
  orderCode: Scalars['String'];
};


export type QueryGetBlindBoxBuyByOrderIdArgs = {
  blindBoxBuyId: Scalars['ID'];
};


export type QueryGetBlindBoxBuysArgs = {
  options?: Maybe<BlindBoxBuyListOptions>;
  blindBoxName?: Maybe<Scalars['String']>;
  distributorName?: Maybe<Scalars['String']>;
  customerName?: Maybe<Scalars['String']>;
  customerPhone?: Maybe<Scalars['String']>;
};


export type QueryGetBlindBoxBuyArgs = {
  blindBoxBuyId: Scalars['ID'];
};


export type QueryBlindBoxActivityIsBookingArgs = {
  blindBoxActivityId: Scalars['ID'];
};


export type QueryCustomerIsAssistArgs = {
  blindBoxBuyId: Scalars['ID'];
};


export type QueryTotalBlindBoxStatisticsArgs = {
  startTime?: Maybe<Scalars['DateTime']>;
  endTime?: Maybe<Scalars['DateTime']>;
};


export type QueryCustomerCheckinRecordsArgs = {
  input?: Maybe<CustomerCheckinRecordsInput>;
};


export type QueryPointsHistoryArgs = {
  options?: Maybe<PointsHistoryListOptions>;
};


export type QueryGetPointsProductsArgs = {
  isAllowExchange?: Maybe<Scalars['Boolean']>;
  options?: Maybe<PointsProductListOptions>;
};


export type QueryPointsProductsArgs = {
  productName?: Maybe<Scalars['String']>;
  options?: Maybe<PointsProductListOptions>;
};


export type QueryPointsProductArgs = {
  id: Scalars['ID'];
};


export type QueryActiveProductsArgs = {
  options?: Maybe<ProductListOptions>;
  promotionType?: Maybe<PromotionType>;
  isExchange?: Maybe<Scalars['Boolean']>;
};


export type QueryExclusionGroupsArgs = {
  options?: Maybe<ExclusionGroupListOptions>;
};


export type QueryExclusionGroupArgs = {
  id: Scalars['ID'];
};


export type QueryCheckSkuIdInOtherExclusionGroupArgs = {
  exclusionProductInputs?: Maybe<Array<Maybe<ExclusionProductInput>>>;
  exclusionGroupId?: Maybe<Scalars['ID']>;
};


export type QueryCouponBundleArgs = {
  id: Scalars['ID'];
};


export type QueryCouponBundlesArgs = {
  options?: Maybe<CouponBundleListOptions>;
};


export type QueryFirstCustomerBenefitArgs = {
  id: Scalars['ID'];
};


export type QueryFirstCustomerBenefitsArgs = {
  startTime?: Maybe<Scalars['DateTime']>;
  endTime?: Maybe<Scalars['DateTime']>;
  options?: Maybe<FirstCustomerBenefitListOptions>;
};


export type QueryProductsSortByProductIdsArgs = {
  options?: Maybe<ProductListOptions>;
};


export type QueryGetProductVariantsArgs = {
  productId?: Maybe<Scalars['ID']>;
  options?: Maybe<ProductVariantListOptions>;
};


export type QueryProductVariantToMembershipIdArgs = {
  membershipId: Scalars['ID'];
};


export type QueryGetRegionsJsonArgs = {
  regionNames: Array<Maybe<Scalars['String']>>;
};


export type QueryAreAllEligibleProductsInactiveOrSoldOutArgs = {
  promotionId: Scalars['ID'];
  promotionType: PromotionType;
};


export type QueryGetOrderTypeByPaymentCodeArgs = {
  paymentCode: Scalars['String'];
};


export type QueryDesDecodeArgs = {
  input: Scalars['String'];
};


export type QueryGenerateH5LinkArgs = {
  input?: Maybe<ProgramLinkInput>;
};


export type QueryGenerateSmallProgramLinkArgs = {
  input?: Maybe<ProgramLinkInput>;
};


export type QueryGenerateSmallProgramQrCodeLinkArgs = {
  input?: Maybe<ProgramLinkInput>;
};


export type QueryBannersArgs = {
  options?: Maybe<BannerListOptions>;
};


export type QueryBannerArgs = {
  bannerId: Scalars['String'];
  options?: Maybe<BannerListOptions>;
};


export type QueryCustomPagesArgs = {
  options?: Maybe<CustomPageListOptions>;
};


export type QueryCustomPageArgs = {
  customPageId?: Maybe<Scalars['ID']>;
  options?: Maybe<CustomPageListOptions>;
  isPreview?: Maybe<Scalars['Boolean']>;
};


export type QuerySeriesProductsArgs = {
  collectionId: Scalars['ID'];
  options?: Maybe<ProductListOptions>;
};


export type QueryHotWordsArgs = {
  options?: Maybe<HotWordListOptions>;
};


export type QueryHotWordArgs = {
  options?: Maybe<HotWordListOptions>;
  hotWordId: Scalars['ID'];
};


export type QueryAnnouncementsArgs = {
  options?: Maybe<AnnouncementListOptions>;
};


export type QueryAnnouncementArgs = {
  announcementId: Scalars['ID'];
  options?: Maybe<AnnouncementListOptions>;
};


export type QueryCouponAvailableCountArgs = {
  couponId: Scalars['ID'];
};


export type QueryCouponsArgs = {
  options?: Maybe<CouponListOptions>;
};


export type QueryGetAvailableCouponListArgs = {
  customerId: Scalars['ID'];
  couponName?: Maybe<Scalars['String']>;
  options?: Maybe<CouponListOptions>;
};


export type QueryCouponArgs = {
  options?: Maybe<CouponListOptions>;
  couponId: Scalars['ID'];
};


export type QueryGetCouponHoldArgs = {
  options?: Maybe<CouponListOptions>;
  couponId: Scalars['ID'];
};


export type QueryGetActiveCouponsArgs = {
  orderId?: Maybe<Scalars['ID']>;
};


export type QueryUserCouponsArgs = {
  options?: Maybe<UserCouponListOptions>;
  customerId?: Maybe<Scalars['ID']>;
  customerPhone?: Maybe<Scalars['String']>;
  couponName?: Maybe<Scalars['String']>;
  couponType?: Maybe<CouponType>;
};


export type QueryUserCouponArgs = {
  options?: Maybe<UserCouponListOptions>;
  userCouponId: Scalars['ID'];
  removeNewTag?: Maybe<Scalars['Boolean']>;
};


export type QueryCouponHolderArgs = {
  couponId: Scalars['ID'];
  options?: Maybe<UserCouponListOptions>;
};


export type QueryCouponDataStatisticArgs = {
  couponId: Scalars['ID'];
};


export type QueryPurchasePremiumsArgs = {
  options?: Maybe<PurchasePremiumListOptions>;
};


export type QueryPurchasePremiumArgs = {
  purchasePremiumId: Scalars['ID'];
  options?: Maybe<PurchasePremiumListOptions>;
};


export type QueryDistributorsGroupsArgs = {
  startDateTime?: Maybe<Scalars['DateTime']>;
  endDateTime?: Maybe<Scalars['DateTime']>;
  name?: Maybe<Scalars['String']>;
  skip?: Maybe<Scalars['Int']>;
  take?: Maybe<Scalars['Int']>;
  sortName?: Maybe<Scalars['String']>;
  sortType?: Maybe<Scalars['String']>;
  isStatistics?: Maybe<Scalars['Boolean']>;
  options?: Maybe<DistributorGroupListOptions>;
};


export type QueryDistributorsGroupsByDayArgs = {
  startDateTime?: Maybe<Scalars['DateTime']>;
  endDateTime?: Maybe<Scalars['DateTime']>;
  name?: Maybe<Scalars['String']>;
  skip?: Maybe<Scalars['Int']>;
  take?: Maybe<Scalars['Int']>;
  sortName?: Maybe<Scalars['String']>;
  sortType?: Maybe<Scalars['String']>;
  options?: Maybe<DistributorGroupListOptions>;
};


export type QueryDistributorsArgs = {
  name?: Maybe<Scalars['String']>;
  phone?: Maybe<Scalars['String']>;
  startDateTime?: Maybe<Scalars['DateTime']>;
  endDateTime?: Maybe<Scalars['DateTime']>;
  distributorGroupIds?: Maybe<Array<Maybe<Scalars['ID']>>>;
  skip?: Maybe<Scalars['Int']>;
  take?: Maybe<Scalars['Int']>;
  sortName?: Maybe<Scalars['String']>;
  sortType?: Maybe<Scalars['String']>;
  options?: Maybe<DistributorListOptions>;
};


export type QueryDistributorsByDayArgs = {
  skip?: Maybe<Scalars['Int']>;
  take?: Maybe<Scalars['Int']>;
  name?: Maybe<Scalars['String']>;
  phone?: Maybe<Scalars['String']>;
  distributorGroupIds?: Maybe<Array<Maybe<Scalars['ID']>>>;
  startDateTime?: Maybe<Scalars['DateTime']>;
  endDateTime?: Maybe<Scalars['DateTime']>;
  options?: Maybe<DistributorListOptions>;
};


export type QueryDistributorArgs = {
  options?: Maybe<DistributorListOptions>;
  distributorId: Scalars['ID'];
};


export type QueryDistributorSharingArgs = {
  input: DistributorSharingInput;
};


export type QueryGetH5ShareLinkArgs = {
  input: DistributorSharingInput;
};


export type QueryDiscountActivitiesArgs = {
  options?: Maybe<DiscountActivityListOptions>;
};


export type QueryDiscountActivityArgs = {
  discountActivityId: Scalars['ID'];
  options?: Maybe<DiscountActivityListOptions>;
};


export type QueryFullDiscountPresentsArgs = {
  options?: Maybe<FullDiscountPresentListOptions>;
};


export type QueryFullDiscountPresentArgs = {
  id: Scalars['ID'];
  options?: Maybe<FullDiscountPresentListOptions>;
};


export type QuerySelectiveGiftActivitiesArgs = {
  options?: Maybe<SelectiveGiftActivityListOptions>;
};


export type QuerySelectiveGiftActivityArgs = {
  id: Scalars['ID'];
  options?: Maybe<SelectiveGiftActivityListOptions>;
};


export type QueryFreeGiftsArgs = {
  options?: Maybe<FreeGiftListOptions>;
};


export type QueryFreeGiftArgs = {
  id: Scalars['ID'];
  options?: Maybe<FreeGiftListOptions>;
};


export type QueryProductActivitiesFindOneArgs = {
  id: Scalars['ID'];
};


export type QueryProductActivitiesFindAllArgs = {
  productId: Scalars['ID'];
};


export type QueryActivityUsableProductsArgs = {
  promotionType: PromotionType;
  collectionId?: Maybe<Scalars['ID']>;
  startTime: Scalars['DateTime'];
  endTime: Scalars['DateTime'];
  isUsable?: Maybe<Scalars['Boolean']>;
  options?: Maybe<ProductListOptions>;
};


export type QuerySettingsArgs = {
  keyNames?: Maybe<Array<Maybe<SettingKey>>>;
};


export type QueryFreeGiftByOrderArgs = {
  orderId: Scalars['ID'];
};


export type QueryOrderPromotionResultArgs = {
  orderIds: Array<Scalars['ID']>;
};


export type QueryPromotionResultByOrderIdArgs = {
  orderId: Scalars['ID'];
};


export type QueryGetRefundableAmountArgs = {
  orderId: Scalars['ID'];
  lineIds?: Maybe<Array<Maybe<Scalars['ID']>>>;
  isInclusionShippingPrice?: Maybe<Scalars['Boolean']>;
  isUpdate?: Maybe<Scalars['Boolean']>;
};


export type QueryGetRefundableAmountByOrderIdArgs = {
  orderId: Scalars['ID'];
};


export type QueryMarkUpByOrderArgs = {
  orderId: Scalars['ID'];
};


export type QueryMerchantVoluntaryRefundByOrderArgs = {
  orderId: Scalars['ID'];
};


export type QueryDataPanelArgs = {
  membershipPlanId: Scalars['ID'];
  startTime?: Maybe<Scalars['DateTime']>;
  endTime?: Maybe<Scalars['DateTime']>;
};


export type QueryGroupCustomersArgs = {
  options?: Maybe<GroupCustomerListOptions>;
};


export type QueryGroupCustomerArgs = {
  id: Scalars['ID'];
};


export type QueryGroupCustomerCountArgs = {
  input?: Maybe<GroupCustomerInput>;
};


export type QueryOperationPlansArgs = {
  options?: Maybe<OperationPlanListOptions>;
};


export type QueryOperationPlanArgs = {
  id: Scalars['ID'];
};


export type QueryOperationPlanCustomersArgs = {
  id: Scalars['ID'];
  options?: Maybe<OperationPlanCustomerListOptions>;
};


export type QueryPackageDiscountsArgs = {
  options?: Maybe<PackageDiscountListOptions>;
};


export type QueryPackageDiscountArgs = {
  id: Scalars['ID'];
  options?: Maybe<PackageDiscountListOptions>;
};


export type QueryTransactionCustomerStatisticsArgs = {
  startDateTime?: Maybe<Scalars['DateTime']>;
  endDateTime?: Maybe<Scalars['DateTime']>;
};


export type QueryCustomPageDataStatisticsArgs = {
  startTime?: Maybe<Scalars['DateTime']>;
  endTime?: Maybe<Scalars['DateTime']>;
  take?: Maybe<Scalars['Int']>;
  skip?: Maybe<Scalars['Int']>;
  sortName?: Maybe<Scalars['String']>;
  sortType?: Maybe<Scalars['String']>;
  pageName?: Maybe<Scalars['String']>;
};


export type QueryMemberPricesArgs = {
  options?: Maybe<MemberPriceListOptions>;
};


export type QueryMemberPriceArgs = {
  memberPriceId: Scalars['ID'];
  options?: Maybe<MemberPriceListOptions>;
};


export type QueryMemberPriceProductsArgs = {
  memberPriceId: Scalars['ID'];
  collectionId?: Maybe<Scalars['ID']>;
  productName?: Maybe<Scalars['String']>;
  options?: Maybe<MemberPriceProductListOptions>;
};


export type QueryMemberPriceProductVariantsArgs = {
  memberPriceProductId: Scalars['ID'];
  options?: Maybe<MemberPriceProductVariantListOptions>;
};


export type QueryGetOrderIsIncludeMemberPriceArgs = {
  orderId: Scalars['ID'];
};


export type QueryGiftCardOrdersArgs = {
  giftCardId?: Maybe<Scalars['ID']>;
  phone?: Maybe<Scalars['String']>;
  distributorId?: Maybe<Scalars['ID']>;
  options?: Maybe<GiftCardOrderListOptions>;
};


export type QueryGiftCardReturnOrdersArgs = {
  options?: Maybe<GiftCardReturnListOptions>;
  giftCardId?: Maybe<Scalars['ID']>;
  giftCardOrderCode?: Maybe<Scalars['String']>;
  phone?: Maybe<Scalars['String']>;
  distributorId?: Maybe<Scalars['ID']>;
};


export type QueryPaymentRewardActivitiesArgs = {
  options?: Maybe<PaymentRewardActivityListOptions>;
};


export type QueryPaymentRewardActivityArgs = {
  id: Scalars['ID'];
  options?: Maybe<PaymentRewardActivityListOptions>;
};


export type QueryShareSettingsArgs = {
  pageKeys?: Maybe<Array<Maybe<PageKey>>>;
};


export type QueryShoppingCreditsClaimActivityArgs = {
  shoppingCreditsClaimActivityId: Scalars['ID'];
  options?: Maybe<ShoppingCreditsClaimActivityListOptions>;
};


export type QueryShoppingCreditsClaimActivitiesArgs = {
  options?: Maybe<ShoppingCreditsClaimActivityListOptions>;
};


export type QueryShoppingCreditsDeductionActivitiesArgs = {
  options?: Maybe<ShoppingCreditsDeductionActivityListOptions>;
};


export type QueryShoppingCreditsDeductionActivityArgs = {
  shoppingCreditsDeductionActivityId: Scalars['ID'];
  options?: Maybe<ShoppingCreditsDeductionActivityListOptions>;
};


export type QueryShoppingCreditsClaimAndDeductionAmountArgs = {
  orderId: Scalars['ID'];
};


export type QueryActivityCountdownsArgs = {
  option?: Maybe<ActivityCountdownListOptions>;
  activityType?: Maybe<PromotionType>;
  activityStatus?: Maybe<ActivityStatus>;
  activityName?: Maybe<Scalars['String']>;
};


export type QueryActivityCountdownArgs = {
  activityCountdownId: Scalars['ID'];
};


export type QueryActivityCountdownByProductArgs = {
  productId: Scalars['ID'];
};


export type QueryCheckProductInSameExclusionGroupArgs = {
  productIds: Array<Scalars['ID']>;
};


export type QueryCheckProductCanCheckoutArgs = {
  skuIds: Array<Scalars['ID']>;
};


export type QueryGetReceivedCouponBundlesArgs = {
  couponBundleId: Scalars['ID'];
};


export type QueryDistributorBindingMembersArgs = {
  search?: Maybe<Scalars['String']>;
  startDateTime?: Maybe<Scalars['DateTime']>;
  endDateTime?: Maybe<Scalars['DateTime']>;
  state?: Maybe<MemberStateInput>;
  skip?: Maybe<Scalars['Int']>;
  take?: Maybe<Scalars['Int']>;
};


export type QueryDistributorCenterArgs = {
  startDateTime?: Maybe<Scalars['DateTime']>;
  endDateTime?: Maybe<Scalars['DateTime']>;
};


export type QueryDistributorPerformanceArgs = {
  startDateTime?: Maybe<Scalars['DateTime']>;
  endDateTime?: Maybe<Scalars['DateTime']>;
};


export type QueryDistributorsPromoteOrdersArgs = {
  customerId?: Maybe<Scalars['ID']>;
  search?: Maybe<Scalars['String']>;
  startDateTime?: Maybe<Scalars['DateTime']>;
  endDateTime?: Maybe<Scalars['DateTime']>;
  options?: Maybe<DistributorOrderListOptions>;
};


export type QueryDistributorBindingCustomersArgs = {
  customerType?: Maybe<CustomerType>;
  search?: Maybe<Scalars['String']>;
  startDateTime?: Maybe<Scalars['DateTime']>;
  endDateTime?: Maybe<Scalars['DateTime']>;
  options?: Maybe<DistributorBindingListOptions>;
};


export type QueryDistributorBindingCustomerDetailArgs = {
  customerId: Scalars['ID'];
};


export type QueryDistributorShareProductsArgs = {
  search?: Maybe<Scalars['String']>;
  startDateTime?: Maybe<Scalars['DateTime']>;
  endDateTime?: Maybe<Scalars['DateTime']>;
  options?: Maybe<DistributorProductRecordListOptions>;
};


export type QueryOrderPaymentRewardCouponsArgs = {
  orderId: Scalars['ID'];
  options?: Maybe<OrderPaymentRewardCouponListOptions>;
};


export type QueryOrdersArgs = {
  options?: Maybe<OrderListOptions>;
};


export type QuerySubscriptionsTotalArgs = {
  customerName?: Maybe<Scalars['String']>;
  customerPhone?: Maybe<Scalars['String']>;
  productName?: Maybe<Scalars['String']>;
};


export type QuerySubscriptionsArgs = {
  options?: Maybe<SubscriptionListOptions>;
  customerName?: Maybe<Scalars['String']>;
  customerPhone?: Maybe<Scalars['String']>;
  productName?: Maybe<Scalars['String']>;
};


export type QuerySubscriptionByOrderIdArgs = {
  orderId: Scalars['ID'];
};


export type QuerySubscriptionArgs = {
  subscriptionId: Scalars['String'];
};


export type QuerySubscriptionOperationsArgs = {
  subscriptionId: Scalars['String'];
  operationType: OperationSubscritionType;
};


export type QueryOrderRefundsArgs = {
  options?: Maybe<OrderRefundListOptions>;
};


export type QueryOrderRefundArgs = {
  orderId?: Maybe<Scalars['String']>;
};


export type QueryLogisticsArgs = {
  orderId: Scalars['ID'];
};


export type QueryGetActiveOrderByTypeArgs = {
  type: OrderPurchaseType;
  isRemoveMarkUp?: Maybe<Scalars['Boolean']>;
  isUseMember?: Maybe<Scalars['Boolean']>;
  isUseShoppingCredit?: Maybe<Scalars['Boolean']>;
};


export type QueryNewProductsArgs = {
  options?: Maybe<ProductListOptions>;
  sku?: Maybe<Scalars['String']>;
  collectionId?: Maybe<Scalars['ID']>;
  state?: Maybe<SalesStatus>;
  isFilterPreSale?: Maybe<Scalars['Boolean']>;
  isFilterEmptySku?: Maybe<Scalars['Boolean']>;
};


export type QueryGetPurchaseQuantityArgs = {
  productId: Scalars['ID'];
  type?: Maybe<OrderPurchaseType>;
  isIncludeCurrentOrder?: Maybe<Scalars['Boolean']>;
};


export type QueryGetExchangeableQuantityArgs = {
  productId: Scalars['ID'];
  type?: Maybe<OrderPurchaseType>;
  isIncludeCurrentOrder?: Maybe<Scalars['Boolean']>;
};


export type QueryEligibleShippingMethodsByOrderTypeArgs = {
  type: OrderPurchaseType;
};


export type QueryGetShoppingCartArgs = {
  isRemoveMarkUp?: Maybe<Scalars['Boolean']>;
  isUseMember?: Maybe<Scalars['Boolean']>;
  isUseShoppingCredit?: Maybe<Scalars['Boolean']>;
};


export type QueryGetOrderArgs = {
  type?: Maybe<OrderPurchaseType>;
  isRemoveMarkUp?: Maybe<Scalars['Boolean']>;
};


export type QueryGetOrderNullArgs = {
  type?: Maybe<OrderPurchaseType>;
  isRemoveMarkUp?: Maybe<Scalars['Boolean']>;
};


export type QueryGetSubscriptRefundableAmountArgs = {
  subscriptionId: Scalars['String'];
};


export type QueryWeChatConfigsArgs = {
  options?: Maybe<WeChatConfigListOptions>;
};


export type QueryGetOpenIdByJsCodeArgs = {
  jsCode: Scalars['String'];
};


export type QueryCompRecordsArgs = {
  options?: Maybe<CompRecordListOptions>;
};


export type QueryCompRecordArgs = {
  compRecordId: Scalars['String'];
};


export type QuerySubscriptionCompRecordsArgs = {
  subscriptionId?: Maybe<Scalars['String']>;
  orderId?: Maybe<Scalars['String']>;
  options?: Maybe<CompRecordListOptions>;
};


export type QueryAfterSaleArgs = {
  afterSaleId: Scalars['ID'];
  options?: Maybe<AfterSaleListOptions>;
};


export type QueryAfterSalesArgs = {
  options?: Maybe<AfterSaleListOptions>;
  orderCode?: Maybe<Scalars['String']>;
  productName?: Maybe<Scalars['String']>;
};


export type QueryAfterSaleByOrderIdArgs = {
  orderId: Scalars['ID'];
  options?: Maybe<AfterSaleListOptions>;
};


export type QueryChannelAddressesArgs = {
  options?: Maybe<ChannelAddressListOptions>;
};


export type QueryNewOrdersArgs = {
  shippingPhone?: Maybe<Scalars['String']>;
  shippingLastName?: Maybe<Scalars['String']>;
  logisticsCode?: Maybe<Scalars['String']>;
  options?: Maybe<OrderListOptions>;
  extraState?: Maybe<ExtraState>;
  productName?: Maybe<Scalars['String']>;
  distributorName?: Maybe<Scalars['String']>;
  distributorPhone?: Maybe<Scalars['String']>;
  customerPhone?: Maybe<Scalars['String']>;
  orderProductType?: Maybe<OrderProductType>;
  orderGenre?: Maybe<OrderGenre>;
  subscriptionCode?: Maybe<Scalars['String']>;
  orderReferralSource?: Maybe<OrderReferralSource>;
};


export type QueryMembershipPlansArgs = {
  options?: Maybe<MembershipPlanListOptions>;
};


export type QueryMembershipPlanArgs = {
  membershipPlanId: Scalars['ID'];
  options?: Maybe<MembershipPlanListOptions>;
};


export type QueryMembershipOrdersArgs = {
  options?: Maybe<MembershipOrderListOptions>;
};


export type QueryMembershipOrderArgs = {
  memberOrderId: Scalars['ID'];
  options?: Maybe<MembershipOrderListOptions>;
};


export type QueryMembersArgs = {
  options?: Maybe<MemberListOptions>;
  memberPlanId?: Maybe<Scalars['ID']>;
  code?: Maybe<Scalars['String']>;
  phoneNumber?: Maybe<Scalars['String']>;
};


export type QueryMemberArgs = {
  memberId: Scalars['ID'];
  options?: Maybe<MemberListOptions>;
};


export type QueryGiftCardsArgs = {
  options?: Maybe<GiftCardListOptions>;
};


export type QueryGiftCardArgs = {
  giftCardId: Scalars['ID'];
  options?: Maybe<GiftCardListOptions>;
};


export type QueryForumTagsArgs = {
  options?: Maybe<ForumTagListOptions>;
};


export type QueryForumTagArgs = {
  forumTagId: Scalars['ID'];
};


export type QueryForumActivitiesArgs = {
  options?: Maybe<ForumActivityListOptions>;
};


export type QueryForumActivityArgs = {
  forumActivityId: Scalars['ID'];
};


export type QueryForumCustomersArgs = {
  options?: Maybe<ForumCustomerListOptions>;
};


export type QueryForumCustomerArgs = {
  forumCustomerId: Scalars['ID'];
};


export type QueryForumPostsArgs = {
  options?: Maybe<ForumPostListOptions>;
  forumTagId?: Maybe<Scalars['ID']>;
  forumActivityId?: Maybe<Scalars['ID']>;
};


export type QueryForumPostArgs = {
  forumPostId: Scalars['ID'];
};


export type QueryGetUnAuditForumPostsArgs = {
  options?: Maybe<ForumPostListOptions>;
};


export type QueryGetHotForumPostsArgs = {
  options?: Maybe<ForumPostListOptions>;
  forumPostHotDateType: ForumPostHotDateType;
  isWish?: Maybe<Scalars['Boolean']>;
};


export type QueryGetNewForumPostsArgs = {
  options?: Maybe<ForumPostListOptions>;
};


export type QueryGetForumTopicByPidArgs = {
  id: Scalars['String'];
};


export type QueryGetForumReviewsArgs = {
  pid: Scalars['String'];
  level?: Maybe<Scalars['Int']>;
  options?: Maybe<ForumReviewListOptions>;
};


export type QueryGetHotForumPostsByTagArgs = {
  options?: Maybe<ForumPostListOptions>;
  forumTagId: Scalars['ID'];
  forumPostHotDateType?: Maybe<ForumPostHotDateType>;
  isWish?: Maybe<Scalars['Boolean']>;
};


export type QueryGetNewForumPostsByTagArgs = {
  options?: Maybe<ForumPostListOptions>;
  forumTagId: Scalars['ID'];
};


export type QueryGetHotForumPostsByActivityArgs = {
  options?: Maybe<ForumPostListOptions>;
  activityId: Scalars['ID'];
  forumPostHotDateType?: Maybe<ForumPostHotDateType>;
  isWish?: Maybe<Scalars['Boolean']>;
};


export type QueryGetNewForumPostsByActivityArgs = {
  options?: Maybe<ForumPostListOptions>;
  activityId: Scalars['ID'];
  isWish?: Maybe<Scalars['Boolean']>;
};


export type QueryGetHotForumPostsByTagHashArgs = {
  options?: Maybe<ForumPostListOptions>;
  forumTagHash?: Maybe<Scalars['String']>;
  forumPostHotDateType?: Maybe<ForumPostHotDateType>;
  isWish?: Maybe<Scalars['Boolean']>;
};


export type QueryForumNotificationsArgs = {
  notificationType?: Maybe<NotificationType>;
  options?: Maybe<ForumNotificationListOptions>;
};


export type QueryHasUnreadNotificationsArgs = {
  notificationType?: Maybe<NotificationType>;
};


export type QueryGetMyForumPostsArgs = {
  options?: Maybe<ForumPostListOptions>;
};


export type QueryForumReviewsArgs = {
  options?: Maybe<ForumReviewListOptions>;
};


export type QueryForumReviewArgs = {
  forumReviewId: Scalars['ID'];
};

export type QuerySubscriptionInput = {
  id: Scalars['ID'];
  createdAt?: Maybe<Scalars['DateTime']>;
  updatedAt?: Maybe<Scalars['DateTime']>;
  expireNumberOfPeriod?: Maybe<Scalars['Int']>;
  channelId?: Maybe<Scalars['ID']>;
  currentNumberOfPeriod?: Maybe<Scalars['Int']>;
  cutOffDays?: Maybe<Scalars['Int']>;
  discountType?: Maybe<DiscountType>;
  discountAmount?: Maybe<Scalars['Int']>;
  frequencyUnit?: Maybe<FrequencyUnit>;
  state?: Maybe<SubscriptionState>;
  frequency?: Maybe<Scalars['Int']>;
  firstShippingDate?: Maybe<Scalars['DateTime']>;
  nextShippingDate?: Maybe<Scalars['DateTime']>;
};

export type RealTimeDataStatistics = {
  __typename?: 'RealTimeDataStatistics';
  orderNumberToBeShipped?: Maybe<Scalars['Int']>;
  numberOfPendingRefunds?: Maybe<Scalars['Int']>;
  salesThisMonth?: Maybe<Scalars['Float']>;
  orderNumberPaidToday?: Maybe<Scalars['Int']>;
  ordersPaidYesterday?: Maybe<Scalars['Int']>;
  proportionOfOrders?: Maybe<Scalars['Float']>;
  orderNumberPaidCompareYesterday?: Maybe<Scalars['Float']>;
  salesToday?: Maybe<Scalars['Float']>;
  salesYesterday?: Maybe<Scalars['Float']>;
  proportionOfSales?: Maybe<Scalars['Float']>;
  salesCompareYesterday?: Maybe<Scalars['Float']>;
  numberOfPayersToday?: Maybe<Scalars['Int']>;
  numberOfPayersYesterday?: Maybe<Scalars['Int']>;
  proportionOfPayers?: Maybe<Scalars['Float']>;
  visitorsCount?: Maybe<Scalars['Int']>;
  visitorsCountYesterdayNumber?: Maybe<Scalars['Int']>;
  proportionOfVisitors?: Maybe<Scalars['Float']>;
  visitorsCountCompareYesterday?: Maybe<Scalars['Float']>;
};

export type ReceiveAssistGiftResponse = {
  __typename?: 'ReceiveAssistGiftResponse';
  isReceived?: Maybe<Scalars['Boolean']>;
  assistGiftRecord?: Maybe<AssistGiftRecord>;
  gift?: Maybe<UserCoupon>;
};

export enum RedirectType {
  Url = 'url',
  ProductDetails = 'productDetails',
  ShopHome = 'shopHome',
  Blank = 'blank'
}

export type RefreshCustomerVerificationResult = Success | NativeAuthStrategyError;

export type Refund = Node & {
  __typename?: 'Refund';
  id: Scalars['ID'];
  createdAt: Scalars['DateTime'];
  updatedAt: Scalars['DateTime'];
  items: Scalars['Money'];
  shipping: Scalars['Money'];
  adjustment: Scalars['Money'];
  total: Scalars['Money'];
  method?: Maybe<Scalars['String']>;
  state: Scalars['String'];
  transactionId?: Maybe<Scalars['String']>;
  reason?: Maybe<Scalars['String']>;
  lines: Array<RefundLine>;
  paymentId: Scalars['ID'];
  metadata?: Maybe<Scalars['JSON']>;
};

export type RefundLine = {
  __typename?: 'RefundLine';
  orderLine: OrderLine;
  orderLineId: Scalars['ID'];
  quantity: Scalars['Int'];
  refund: Refund;
  refundId: Scalars['ID'];
};

export type RefundOrderCustom = {
  orderId: Scalars['ID'];
  images?: Maybe<Array<Maybe<Scalars['String']>>>;
  cancelType?: Maybe<Scalars['String']>;
  cancelExplain?: Maybe<Scalars['String']>;
  cancelCause?: Maybe<Scalars['String']>;
  refundAmount?: Maybe<Scalars['Float']>;
};

export type RefundResult = {
  __typename?: 'RefundResult';
  state?: Maybe<Scalars['String']>;
  transactionId?: Maybe<Scalars['String']>;
};

export enum RefundState {
  Pending = 'Pending',
  Settled = 'Settled',
  Failed = 'Failed',
  Cancel = 'Cancel'
}

export enum RefundableType {
  RefundOnly = 'refundOnly',
  RefundAndReturn = 'refundAndReturn',
  VoluntaryRefund = 'voluntaryRefund'
}

export type Region = {
  id: Scalars['ID'];
  createdAt: Scalars['DateTime'];
  updatedAt: Scalars['DateTime'];
  languageCode: LanguageCode;
  code: Scalars['String'];
  type: Scalars['String'];
  name: Scalars['String'];
  enabled: Scalars['Boolean'];
  parent?: Maybe<Region>;
  parentId?: Maybe<Scalars['ID']>;
  translations: Array<RegionTranslation>;
};

export type RegionTranslation = {
  __typename?: 'RegionTranslation';
  id: Scalars['ID'];
  createdAt: Scalars['DateTime'];
  updatedAt: Scalars['DateTime'];
  languageCode: LanguageCode;
  name: Scalars['String'];
};

export type RegisterCustomerAccountResult = Success | MissingPasswordError | PasswordValidationError | NativeAuthStrategyError;

export type RegisterCustomerCustomFieldsInput = {
  distributorId?: Maybe<Scalars['ID']>;
  points?: Maybe<Scalars['Int']>;
  isModified?: Maybe<Scalars['Boolean']>;
  isSettleCommission?: Maybe<Scalars['Boolean']>;
  headPortrait?: Maybe<Scalars['String']>;
  gender?: Maybe<Scalars['String']>;
  dateBirth?: Maybe<Scalars['DateTime']>;
  wechatCode?: Maybe<Scalars['String']>;
  area?: Maybe<Scalars['String']>;
};

export type RegisterCustomerInput = {
  emailAddress: Scalars['String'];
  title?: Maybe<Scalars['String']>;
  firstName?: Maybe<Scalars['String']>;
  lastName?: Maybe<Scalars['String']>;
  phoneNumber?: Maybe<Scalars['String']>;
  password?: Maybe<Scalars['String']>;
  customFields?: Maybe<RegisterCustomerCustomFieldsInput>;
};

export type RelationCustomFieldConfig = CustomField & {
  __typename?: 'RelationCustomFieldConfig';
  name: Scalars['String'];
  type: Scalars['String'];
  list: Scalars['Boolean'];
  label?: Maybe<Array<LocalizedString>>;
  description?: Maybe<Array<LocalizedString>>;
  readonly?: Maybe<Scalars['Boolean']>;
  internal?: Maybe<Scalars['Boolean']>;
  nullable?: Maybe<Scalars['Boolean']>;
  entity: Scalars['String'];
  scalarFields: Array<Scalars['String']>;
  ui?: Maybe<Scalars['JSON']>;
};

export type RemoveOrderItemsResult = Order | OrderModificationError;

export type RequestPasswordResetResult = Success | NativeAuthStrategyError;

export type RequestUpdateCustomerEmailAddressResult = Success | InvalidCredentialsError | EmailAddressConflictError | NativeAuthStrategyError;

export type ResetPasswordResult = CurrentUser | PasswordResetTokenInvalidError | PasswordResetTokenExpiredError | PasswordValidationError | NativeAuthStrategyError | NotVerifiedError;

export type RestrictedRegion = {
  __typename?: 'RestrictedRegion';
  province?: Maybe<Scalars['String']>;
  city?: Maybe<Scalars['String']>;
  district?: Maybe<Scalars['String']>;
};

export type RestrictedRegionInput = {
  province: Scalars['String'];
  city?: Maybe<Scalars['String']>;
  district?: Maybe<Scalars['String']>;
};

export enum RestrictionType {
  Region = 'region',
  RegionExclude = 'regionExclude'
}

export type ReturnInfo = {
  __typename?: 'ReturnInfo';
  errorCode?: Maybe<Scalars['String']>;
  message?: Maybe<Scalars['String']>;
};

export type ReturnLogisticsInput = {
  logisticCode: Scalars['String'];
  company: Scalars['String'];
  afterSaleId: Scalars['ID'];
};

export type ReturnTheCard = {
  memberId: Scalars['ID'];
  cardReturnMethod: CardReturnMethod;
  amountReturned?: Maybe<Scalars['Int']>;
  isReturnCoupon?: Maybe<Scalars['Boolean']>;
};

export type ReturnTheGiftCard = {
  giftCardOrderId: Scalars['ID'];
  returnAmount: Scalars['Int'];
  isReturnCoupon: Scalars['Boolean'];
  isReturnMember: Scalars['Boolean'];
};

export type ReviewProduct = Node & {
  __typename?: 'ReviewProduct';
  id: Scalars['ID'];
  title: Scalars['String'];
  createdAt?: Maybe<Scalars['DateTime']>;
  updatedAt?: Maybe<Scalars['DateTime']>;
  description: Scalars['String'];
  stars: Scalars['Int'];
  includePic: Scalars['Boolean'];
  reviewImgs?: Maybe<Array<Maybe<Scalars['String']>>>;
  product?: Maybe<Product>;
  order?: Maybe<Order>;
  customer?: Maybe<Customer>;
  customerName?: Maybe<Scalars['String']>;
  customerNameIsPublic: Scalars['Boolean'];
  automaticReview?: Maybe<Scalars['Boolean']>;
  shopReply?: Maybe<Scalars['String']>;
  reviewType?: Maybe<ReviewType>;
  replyDate?: Maybe<Scalars['DateTime']>;
};

export type ReviewProductFilterParameter = {
  id?: Maybe<IdOperators>;
  title?: Maybe<StringOperators>;
  createdAt?: Maybe<DateOperators>;
  updatedAt?: Maybe<DateOperators>;
  description?: Maybe<StringOperators>;
  stars?: Maybe<NumberOperators>;
  includePic?: Maybe<BooleanOperators>;
  customerName?: Maybe<StringOperators>;
  customerNameIsPublic?: Maybe<BooleanOperators>;
  automaticReview?: Maybe<BooleanOperators>;
  shopReply?: Maybe<StringOperators>;
  reviewType?: Maybe<StringOperators>;
  replyDate?: Maybe<DateOperators>;
};

export type ReviewProductList = PaginatedList & {
  __typename?: 'ReviewProductList';
  items: Array<ReviewProduct>;
  totalItems: Scalars['Int'];
};

export type ReviewProductListOptions = {
  /** Skips the first n results, for use in pagination */
  skip?: Maybe<Scalars['Int']>;
  /** Takes n results, for use in pagination */
  take?: Maybe<Scalars['Int']>;
  /** Specifies which properties to sort the results by */
  sort?: Maybe<ReviewProductSortParameter>;
  /** Allows the results to be filtered */
  filter?: Maybe<ReviewProductFilterParameter>;
  /** Specifies whether multiple "filter" arguments should be combines with a logical AND or OR operation. Defaults to AND. */
  filterOperator?: Maybe<LogicalOperator>;
};

export type ReviewProductSortParameter = {
  id?: Maybe<SortOrder>;
  title?: Maybe<SortOrder>;
  createdAt?: Maybe<SortOrder>;
  updatedAt?: Maybe<SortOrder>;
  description?: Maybe<SortOrder>;
  stars?: Maybe<SortOrder>;
  customerName?: Maybe<SortOrder>;
  shopReply?: Maybe<SortOrder>;
  replyDate?: Maybe<SortOrder>;
};

export enum ReviewType {
  OrderReview = 'orderReview',
  SubscriptionReview = 'subscriptionReview'
}

export enum RewardType {
  Calendar = 'calendar',
  Cycle = 'cycle'
}

export type RightsCoupon = {
  __typename?: 'RightsCoupon';
  enable: Scalars['Boolean'];
  presentedCoupon?: Maybe<Array<PresentedCoupon>>;
};

export type RightsCouponInput = {
  enable: Scalars['Boolean'];
  presentedCoupon?: Maybe<Array<Maybe<PresentedCouponInput>>>;
};

export type RightsDiscount = {
  __typename?: 'RightsDiscount';
  enable: Scalars['Boolean'];
  discountRate?: Maybe<Scalars['Int']>;
  restrictedUse?: Maybe<Scalars['Int']>;
};

export type RightsDiscountInput = {
  enable: Scalars['Boolean'];
  discountRate?: Maybe<Scalars['Int']>;
  restrictedUse?: Maybe<Scalars['Int']>;
};

export type RightsPoints = {
  __typename?: 'RightsPoints';
  enable: Scalars['Boolean'];
  pointsMultiple?: Maybe<Scalars['Float']>;
};

export type RightsPointsInput = {
  enable: Scalars['Boolean'];
  pointsMultiple?: Maybe<Scalars['Float']>;
};

export type Role = Node & {
  __typename?: 'Role';
  id: Scalars['ID'];
  createdAt: Scalars['DateTime'];
  updatedAt: Scalars['DateTime'];
  code: Scalars['String'];
  description: Scalars['String'];
  permissions: Array<Permission>;
  channels: Array<Channel>;
};

export type RoleList = PaginatedList & {
  __typename?: 'RoleList';
  items: Array<Role>;
  totalItems: Scalars['Int'];
};

export enum RuleType {
  Ladder = 'ladder',
  Cycle = 'cycle'
}

export type RuleValue = {
  __typename?: 'RuleValue';
  minimum: Scalars['Int'];
  discountValue?: Maybe<DiscountValue>;
  freeGiftValues?: Maybe<Array<Maybe<FreeGiftValue>>>;
  maximumOffer: Scalars['Int'];
};

export type RuleValueInput = {
  minimum: Scalars['Int'];
  discountValue: DiscountValueInput;
  freeGiftValues?: Maybe<Array<FreeGiftValueInput>>;
  maximumOffer: Scalars['Int'];
};

export type SalesStatistics = {
  __typename?: 'SalesStatistics';
  salesPrice?: Maybe<Scalars['Float']>;
  refundAmount?: Maybe<Scalars['Float']>;
  salesVolume?: Maybe<Scalars['Float']>;
};

export enum SalesStatus {
  All = 'all',
  Warehouse = 'warehouse',
  SoldOut = 'soldOut',
  Selling = 'selling'
}

export type SearchInput = {
  term?: Maybe<Scalars['String']>;
  facetValueFilters?: Maybe<Array<FacetValueFilterInput>>;
  collectionId?: Maybe<Scalars['ID']>;
  collectionSlug?: Maybe<Scalars['String']>;
  groupByProduct?: Maybe<Scalars['Boolean']>;
  take?: Maybe<Scalars['Int']>;
  skip?: Maybe<Scalars['Int']>;
  sort?: Maybe<SearchResultSortParameter>;
  inStock?: Maybe<Scalars['Boolean']>;
};

export type SearchReindexResponse = {
  __typename?: 'SearchReindexResponse';
  success: Scalars['Boolean'];
};

export type SearchResponse = {
  __typename?: 'SearchResponse';
  items: Array<SearchResult>;
  totalItems: Scalars['Int'];
  facetValues: Array<FacetValueResult>;
  collections: Array<CollectionResult>;
};

export type SearchResult = {
  __typename?: 'SearchResult';
  sku: Scalars['String'];
  slug: Scalars['String'];
  productId: Scalars['ID'];
  productName: Scalars['String'];
  productAsset?: Maybe<SearchResultAsset>;
  productVariantId: Scalars['ID'];
  productVariantName: Scalars['String'];
  productVariantAsset?: Maybe<SearchResultAsset>;
  price: SearchResultPrice;
  priceWithTax: SearchResultPrice;
  currencyCode: CurrencyCode;
  description: Scalars['String'];
  facetIds: Array<Scalars['ID']>;
  facetValueIds: Array<Scalars['ID']>;
  /** An array of ids of the Collections in which this result appears */
  collectionIds: Array<Scalars['ID']>;
  /** A relevance score for the result. Differs between database implementations */
  score: Scalars['Float'];
  inStock: Scalars['Boolean'];
};

export type SearchResultAsset = {
  __typename?: 'SearchResultAsset';
  id: Scalars['ID'];
  preview: Scalars['String'];
  focalPoint?: Maybe<Coordinate>;
};

/** The price of a search result product, either as a range or as a single price */
export type SearchResultPrice = PriceRange | SinglePrice;

export type SearchResultSortParameter = {
  name?: Maybe<SortOrder>;
  price?: Maybe<SortOrder>;
};

export type SelectiveGiftActivity = Node & {
  __typename?: 'SelectiveGiftActivity';
  id: Scalars['ID'];
  createdAt: Scalars['DateTime'];
  updatedAt: Scalars['DateTime'];
  displayName: Scalars['String'];
  name: Scalars['String'];
  type?: Maybe<PromotionConditionType>;
  remarks?: Maybe<Scalars['String']>;
  status?: Maybe<ActivityStatus>;
  startTime?: Maybe<Scalars['DateTime']>;
  endTime?: Maybe<Scalars['DateTime']>;
  introduce?: Maybe<Scalars['String']>;
  ruleType?: Maybe<RuleType>;
  ruleValues: Array<RuleValue>;
  applicableProduct?: Maybe<ApplicableProduct>;
  stackingDiscountSwitch?: Maybe<Scalars['Boolean']>;
  stackingPromotionTypes?: Maybe<Array<Maybe<PromotionType>>>;
  whetherRestrictUsers?: Maybe<Scalars['Boolean']>;
  groupType?: Maybe<GroupType>;
  memberPlanIds?: Maybe<Array<Maybe<Scalars['ID']>>>;
  promotion?: Maybe<Promotion>;
  activityContent?: Maybe<Array<Maybe<Scalars['String']>>>;
  activitySynopsis?: Maybe<Scalars['String']>;
  activitySuperposition?: Maybe<Scalars['String']>;
  activityGifts?: Maybe<Array<Maybe<Product>>>;
  statisticsData?: Maybe<StatisticsData>;
};

export type SelectiveGiftActivityFilterParameter = {
  id?: Maybe<IdOperators>;
  createdAt?: Maybe<DateOperators>;
  updatedAt?: Maybe<DateOperators>;
  displayName?: Maybe<StringOperators>;
  name?: Maybe<StringOperators>;
  type?: Maybe<StringOperators>;
  remarks?: Maybe<StringOperators>;
  status?: Maybe<StringOperators>;
  startTime?: Maybe<DateOperators>;
  endTime?: Maybe<DateOperators>;
  introduce?: Maybe<StringOperators>;
  ruleType?: Maybe<StringOperators>;
  stackingDiscountSwitch?: Maybe<BooleanOperators>;
  whetherRestrictUsers?: Maybe<BooleanOperators>;
  groupType?: Maybe<StringOperators>;
  activitySynopsis?: Maybe<StringOperators>;
  activitySuperposition?: Maybe<StringOperators>;
};

export type SelectiveGiftActivityInput = {
  id?: Maybe<Scalars['ID']>;
  displayName: Scalars['String'];
  name: Scalars['String'];
  type: PromotionConditionType;
  remarks?: Maybe<Scalars['String']>;
  status?: Maybe<ActivityStatus>;
  startTime: Scalars['DateTime'];
  endTime: Scalars['DateTime'];
  introduce?: Maybe<Scalars['String']>;
  ruleType: RuleType;
  ruleValues: Array<RuleValueInput>;
  applicableProduct: ApplicableProductInput;
  stackingDiscountSwitch: Scalars['Boolean'];
  stackingPromotionTypes?: Maybe<Array<Maybe<PromotionType>>>;
  whetherRestrictUsers: Scalars['Boolean'];
  memberPlanIds?: Maybe<Array<Maybe<Scalars['ID']>>>;
};

export type SelectiveGiftActivityList = PaginatedList & {
  __typename?: 'SelectiveGiftActivityList';
  items: Array<SelectiveGiftActivity>;
  totalItems: Scalars['Int'];
};

export type SelectiveGiftActivityListOptions = {
  /** Skips the first n results, for use in pagination */
  skip?: Maybe<Scalars['Int']>;
  /** Takes n results, for use in pagination */
  take?: Maybe<Scalars['Int']>;
  /** Specifies which properties to sort the results by */
  sort?: Maybe<SelectiveGiftActivitySortParameter>;
  /** Allows the results to be filtered */
  filter?: Maybe<SelectiveGiftActivityFilterParameter>;
  /** Specifies whether multiple "filter" arguments should be combines with a logical AND or OR operation. Defaults to AND. */
  filterOperator?: Maybe<LogicalOperator>;
};

export type SelectiveGiftActivitySortParameter = {
  id?: Maybe<SortOrder>;
  createdAt?: Maybe<SortOrder>;
  updatedAt?: Maybe<SortOrder>;
  displayName?: Maybe<SortOrder>;
  name?: Maybe<SortOrder>;
  remarks?: Maybe<SortOrder>;
  startTime?: Maybe<SortOrder>;
  endTime?: Maybe<SortOrder>;
  introduce?: Maybe<SortOrder>;
  activitySynopsis?: Maybe<SortOrder>;
  activitySuperposition?: Maybe<SortOrder>;
};

export type Seller = Node & {
  __typename?: 'Seller';
  id: Scalars['ID'];
  createdAt: Scalars['DateTime'];
  updatedAt: Scalars['DateTime'];
  name: Scalars['String'];
  customFields?: Maybe<Scalars['JSON']>;
};

export enum SendCardType {
  MemberCard = 'memberCard',
  GiftCard = 'giftCard'
}

export type SetCustomerForOrderResult = Order | AlreadyLoggedInError | EmailAddressConflictError | NoActiveOrderError | GuestCheckoutError;

export type SetOrderShippingMethodResult = Order | OrderModificationError | IneligibleShippingMethodError | NoActiveOrderError;

export type Setting = Node & {
  __typename?: 'Setting';
  id: Scalars['ID'];
  createdAt: Scalars['DateTime'];
  updatedAt: Scalars['DateTime'];
  key: SettingKey;
  value: Scalars['String'];
};

export type SettingInput = {
  key: SettingKey;
  value: Scalars['String'];
};

export enum SettingKey {
  ForumRecommendProductGroup = 'forumRecommendProductGroup',
  PersonalCenterProductGroup = 'personalCenterProductGroup',
  ShoppingCartProductGroup = 'shoppingCartProductGroup',
  SearchResultProductGroup = 'searchResultProductGroup',
  MemberCardProductGroup = 'memberCardProductGroup',
  ProductDetailProductGroup = 'productDetailProductGroup',
  CouponPageProductGroup = 'couponPageProductGroup',
  CanBackProductDetailPage = 'canBackProductDetailPage',
  IsEnableYouZanMemberSync = 'isEnableYouZanMemberSync',
  IsEnableYouZanCouponSync = 'isEnableYouZanCouponSync',
  YouZanMaxEffectiveTime = 'youZanMaxEffectiveTime',
  MemberCardDiscountLimitSourceType = 'memberCardDiscountLimitSourceType',
  ShoppingMoneyGrantTiming = 'shoppingMoneyGrantTiming',
  ExternalOrderRewardRate = 'externalOrderRewardRate',
}

export type SettingList = PaginatedList & {
  __typename?: 'SettingList';
  items: Array<Setting>;
  totalItems: Scalars['Int'];
};

export type SettlementProduct = {
  productVariantId: Scalars['ID'];
  quantity: Scalars['Int'];
};

export type ShareData = {
  __typename?: 'ShareData';
  shareType: ShareType;
  shareValue: Scalars['ID'];
  distributorId?: Maybe<Scalars['String']>;
  isDetail?: Maybe<Scalars['Boolean']>;
  path?: Maybe<Scalars['String']>;
  isPreview?: Maybe<Scalars['Boolean']>;
};

export type ShareSetting = Node & {
  __typename?: 'ShareSetting';
  id: Scalars['ID'];
  pageKey?: Maybe<PageKey>;
  shareTitle?: Maybe<Scalars['String']>;
  shareImg?: Maybe<Scalars['String']>;
  channelId?: Maybe<Scalars['ID']>;
};

export type ShareSettingInput = {
  pageKey?: Maybe<PageKey>;
  shareTitle?: Maybe<Scalars['String']>;
  shareImg?: Maybe<Scalars['String']>;
};

export enum ShareType {
  Coupon = 'coupon',
  PurchasePremium = 'purchasePremium',
  Distribution = 'distribution',
  HomePage = 'homePage',
  CommodityGroupPage = 'commodityGroupPage',
  CustomPage = 'customPage',
  MemberShipPlan = 'memberShipPlan',
  GiftCard = 'giftCard',
  Product = 'product',
  DiscountActivity = 'discountActivity',
  FullDiscountActivity = 'fullDiscountActivity',
  PackageDiscountActivity = 'packageDiscountActivity',
  SelectiveGiftActivity = 'selectiveGiftActivity',
  None = 'none',
  SearchResult = 'searchResult',
  CouponBundle = 'couponBundle',
  MemberCenter = 'memberCenter',
  MembershipPlanListPage = 'membershipPlanListPage',
  CheckinPage = 'checkinPage',
  BlindBoxActivity = 'blindBoxActivity',
  PointsMall = 'pointsMall',
  Forum = 'forum',
  ForumTag = 'forumTag',
  ForumNote = 'forumNote',
  ShoppingCreditsClaim = 'shoppingCreditsClaim',
  ShoppingCreditsDeduction = 'shoppingCreditsDeduction'
}

export enum SharingType {
  QrCode = 'QRCode',
  Link = 'link',
  H5 = 'H5',
  Scheme = 'Scheme'
}

export enum ShippingFeeType {
  Order = 'order',
  BlindBoxFreeShipping = 'blindBoxFreeShipping',
  BlindBoxPayShipping = 'blindBoxPayShipping'
}

export type ShippingInfo = {
  __typename?: 'ShippingInfo';
  company: Scalars['String'];
  sn: Scalars['String'];
};

export type ShippingInfoInput = {
  company: Scalars['String'];
  sn: Scalars['String'];
};

export type ShippingLine = {
  __typename?: 'ShippingLine';
  id: Scalars['ID'];
  shippingMethod: ShippingMethod;
  price: Scalars['Money'];
  priceWithTax: Scalars['Money'];
  discountedPrice: Scalars['Money'];
  discountedPriceWithTax: Scalars['Money'];
  discounts: Array<Discount>;
};

export type ShippingMethod = Node & {
  __typename?: 'ShippingMethod';
  id: Scalars['ID'];
  createdAt: Scalars['DateTime'];
  updatedAt: Scalars['DateTime'];
  languageCode: LanguageCode;
  code: Scalars['String'];
  name: Scalars['String'];
  description: Scalars['String'];
  fulfillmentHandlerCode: Scalars['String'];
  checker: ConfigurableOperation;
  calculator: ConfigurableOperation;
  translations: Array<ShippingMethodTranslation>;
  customFields?: Maybe<ShippingMethodCustomFields>;
};

export type ShippingMethodCustomFields = {
  __typename?: 'ShippingMethodCustomFields';
  priority?: Maybe<Scalars['Int']>;
};

export type ShippingMethodList = PaginatedList & {
  __typename?: 'ShippingMethodList';
  items: Array<ShippingMethod>;
  totalItems: Scalars['Int'];
};

export type ShippingMethodQuote = {
  __typename?: 'ShippingMethodQuote';
  id: Scalars['ID'];
  price: Scalars['Money'];
  priceWithTax: Scalars['Money'];
  code: Scalars['String'];
  name: Scalars['String'];
  description: Scalars['String'];
  /** Any optional metadata returned by the ShippingCalculator in the ShippingCalculationResult */
  metadata?: Maybe<Scalars['JSON']>;
  customFields?: Maybe<ShippingMethodCustomFields>;
};

export type ShippingMethodTranslation = {
  __typename?: 'ShippingMethodTranslation';
  id: Scalars['ID'];
  createdAt: Scalars['DateTime'];
  updatedAt: Scalars['DateTime'];
  languageCode: LanguageCode;
  name: Scalars['String'];
  description: Scalars['String'];
};

export type ShoppingCart = {
  __typename?: 'ShoppingCart';
  shoppingTrolley?: Maybe<Order>;
  outrightPurchase?: Maybe<Order>;
};

export type ShoppingCreditsClaimActivity = Node & {
  __typename?: 'ShoppingCreditsClaimActivity';
  id: Scalars['ID'];
  createdAt: Scalars['DateTime'];
  updatedAt: Scalars['DateTime'];
  name: Scalars['String'];
  remarks?: Maybe<Scalars['String']>;
  displayName: Scalars['String'];
  status?: Maybe<ActivityStatus>;
  startTime?: Maybe<Scalars['DateTime']>;
  endTime?: Maybe<Scalars['DateTime']>;
  introduce?: Maybe<Scalars['String']>;
  ruleType?: Maybe<RuleType>;
  ruleValues: Array<RuleValue>;
  applicableProduct?: Maybe<ApplicableProduct>;
  stackingDiscountSwitch?: Maybe<Scalars['Boolean']>;
  stackingPromotionTypes?: Maybe<Array<Maybe<PromotionType>>>;
  smallProgramQRCodeLink?: Maybe<Scalars['String']>;
  promotion?: Maybe<Promotion>;
  promotionId?: Maybe<Scalars['ID']>;
  activityContent?: Maybe<Array<Maybe<Scalars['String']>>>;
  activitySynopsis?: Maybe<ActivitySynopsis>;
  activitySuperposition?: Maybe<Scalars['String']>;
};

export type ShoppingCreditsClaimActivityFilterParameter = {
  id?: Maybe<IdOperators>;
  createdAt?: Maybe<DateOperators>;
  updatedAt?: Maybe<DateOperators>;
  name?: Maybe<StringOperators>;
  remarks?: Maybe<StringOperators>;
  displayName?: Maybe<StringOperators>;
  status?: Maybe<StringOperators>;
  startTime?: Maybe<DateOperators>;
  endTime?: Maybe<DateOperators>;
  introduce?: Maybe<StringOperators>;
  ruleType?: Maybe<StringOperators>;
  stackingDiscountSwitch?: Maybe<BooleanOperators>;
  smallProgramQRCodeLink?: Maybe<StringOperators>;
  promotionId?: Maybe<IdOperators>;
  activitySuperposition?: Maybe<StringOperators>;
};

export type ShoppingCreditsClaimActivityInput = {
  id?: Maybe<Scalars['ID']>;
  name: Scalars['String'];
  remarks?: Maybe<Scalars['String']>;
  displayName?: Maybe<Scalars['String']>;
  startTime?: Maybe<Scalars['DateTime']>;
  endTime?: Maybe<Scalars['DateTime']>;
  introduce?: Maybe<Scalars['String']>;
  ruleType: RuleType;
  ruleValues?: Maybe<Array<Maybe<RuleValueInput>>>;
  applicableProduct?: Maybe<ApplicableProductInput>;
  stackingDiscountSwitch?: Maybe<Scalars['Boolean']>;
  stackingPromotionTypes?: Maybe<Array<Maybe<PromotionType>>>;
};

export type ShoppingCreditsClaimActivityList = PaginatedList & {
  __typename?: 'ShoppingCreditsClaimActivityList';
  items: Array<ShoppingCreditsClaimActivity>;
  totalItems: Scalars['Int'];
};

export type ShoppingCreditsClaimActivityListOptions = {
  /** Skips the first n results, for use in pagination */
  skip?: Maybe<Scalars['Int']>;
  /** Takes n results, for use in pagination */
  take?: Maybe<Scalars['Int']>;
  /** Specifies which properties to sort the results by */
  sort?: Maybe<ShoppingCreditsClaimActivitySortParameter>;
  /** Allows the results to be filtered */
  filter?: Maybe<ShoppingCreditsClaimActivityFilterParameter>;
  /** Specifies whether multiple "filter" arguments should be combines with a logical AND or OR operation. Defaults to AND. */
  filterOperator?: Maybe<LogicalOperator>;
};

export type ShoppingCreditsClaimActivitySortParameter = {
  id?: Maybe<SortOrder>;
  createdAt?: Maybe<SortOrder>;
  updatedAt?: Maybe<SortOrder>;
  name?: Maybe<SortOrder>;
  remarks?: Maybe<SortOrder>;
  displayName?: Maybe<SortOrder>;
  startTime?: Maybe<SortOrder>;
  endTime?: Maybe<SortOrder>;
  introduce?: Maybe<SortOrder>;
  smallProgramQRCodeLink?: Maybe<SortOrder>;
  promotionId?: Maybe<SortOrder>;
  activitySuperposition?: Maybe<SortOrder>;
};

export type ShoppingCreditsConfig = Node & {
  __typename?: 'ShoppingCreditsConfig';
  id: Scalars['ID'];
  createdAt?: Maybe<Scalars['DateTime']>;
  updatedAt?: Maybe<Scalars['DateTime']>;
  customPageId?: Maybe<Scalars['ID']>;
  customPage?: Maybe<CustomPage>;
  shoppingCreditsRule?: Maybe<Scalars['String']>;
  channelId?: Maybe<Scalars['ID']>;
};

export type ShoppingCreditsConfigInput = {
  customPageId?: Maybe<Scalars['ID']>;
  shoppingCreditsRule?: Maybe<Scalars['String']>;
};

export type ShoppingCreditsDeductionActivity = Node & {
  __typename?: 'ShoppingCreditsDeductionActivity';
  id: Scalars['ID'];
  createdAt: Scalars['DateTime'];
  updatedAt: Scalars['DateTime'];
  name: Scalars['String'];
  remarks?: Maybe<Scalars['String']>;
  displayName: Scalars['String'];
  status?: Maybe<ActivityStatus>;
  startTime?: Maybe<Scalars['DateTime']>;
  endTime?: Maybe<Scalars['DateTime']>;
  introduce?: Maybe<Scalars['String']>;
  minimum?: Maybe<Scalars['Int']>;
  deductionRate?: Maybe<Scalars['Int']>;
  applicableProduct?: Maybe<ApplicableProduct>;
  stackingDiscountSwitch?: Maybe<Scalars['Boolean']>;
  stackingPromotionTypes?: Maybe<Array<Maybe<PromotionType>>>;
  smallProgramQRCodeLink?: Maybe<Scalars['String']>;
  promotion?: Maybe<Promotion>;
  promotionId?: Maybe<Scalars['ID']>;
  activityContent?: Maybe<Array<Maybe<Scalars['String']>>>;
  activitySynopsis?: Maybe<Scalars['String']>;
  activitySuperposition?: Maybe<Scalars['String']>;
};

export type ShoppingCreditsDeductionActivityFilterParameter = {
  id?: Maybe<IdOperators>;
  createdAt?: Maybe<DateOperators>;
  updatedAt?: Maybe<DateOperators>;
  name?: Maybe<StringOperators>;
  remarks?: Maybe<StringOperators>;
  displayName?: Maybe<StringOperators>;
  status?: Maybe<StringOperators>;
  startTime?: Maybe<DateOperators>;
  endTime?: Maybe<DateOperators>;
  introduce?: Maybe<StringOperators>;
  minimum?: Maybe<NumberOperators>;
  deductionRate?: Maybe<NumberOperators>;
  stackingDiscountSwitch?: Maybe<BooleanOperators>;
  smallProgramQRCodeLink?: Maybe<StringOperators>;
  promotionId?: Maybe<IdOperators>;
  activitySynopsis?: Maybe<StringOperators>;
  activitySuperposition?: Maybe<StringOperators>;
};

export type ShoppingCreditsDeductionActivityInput = {
  id?: Maybe<Scalars['ID']>;
  name: Scalars['String'];
  remarks?: Maybe<Scalars['String']>;
  displayName?: Maybe<Scalars['String']>;
  startTime?: Maybe<Scalars['DateTime']>;
  endTime?: Maybe<Scalars['DateTime']>;
  introduce?: Maybe<Scalars['String']>;
  minimum?: Maybe<Scalars['Int']>;
  deductionRate?: Maybe<Scalars['Int']>;
  applicableProduct?: Maybe<ApplicableProductInput>;
  stackingDiscountSwitch?: Maybe<Scalars['Boolean']>;
  stackingPromotionTypes?: Maybe<Array<Maybe<PromotionType>>>;
};

export type ShoppingCreditsDeductionActivityList = PaginatedList & {
  __typename?: 'ShoppingCreditsDeductionActivityList';
  items: Array<ShoppingCreditsDeductionActivity>;
  totalItems: Scalars['Int'];
};

export type ShoppingCreditsDeductionActivityListOptions = {
  /** Skips the first n results, for use in pagination */
  skip?: Maybe<Scalars['Int']>;
  /** Takes n results, for use in pagination */
  take?: Maybe<Scalars['Int']>;
  /** Specifies which properties to sort the results by */
  sort?: Maybe<ShoppingCreditsDeductionActivitySortParameter>;
  /** Allows the results to be filtered */
  filter?: Maybe<ShoppingCreditsDeductionActivityFilterParameter>;
  /** Specifies whether multiple "filter" arguments should be combines with a logical AND or OR operation. Defaults to AND. */
  filterOperator?: Maybe<LogicalOperator>;
};

export type ShoppingCreditsDeductionActivitySortParameter = {
  id?: Maybe<SortOrder>;
  createdAt?: Maybe<SortOrder>;
  updatedAt?: Maybe<SortOrder>;
  name?: Maybe<SortOrder>;
  remarks?: Maybe<SortOrder>;
  displayName?: Maybe<SortOrder>;
  startTime?: Maybe<SortOrder>;
  endTime?: Maybe<SortOrder>;
  introduce?: Maybe<SortOrder>;
  minimum?: Maybe<SortOrder>;
  deductionRate?: Maybe<SortOrder>;
  smallProgramQRCodeLink?: Maybe<SortOrder>;
  promotionId?: Maybe<SortOrder>;
  activitySynopsis?: Maybe<SortOrder>;
  activitySuperposition?: Maybe<SortOrder>;
};

export enum ShoppingMoneyGrantTiming {
  PaymentSuccess = 'paymentSuccess',
  ConfirmReceipt = 'confirmReceipt'
}

/** The price value where the result has a single price */
export type SinglePrice = {
  __typename?: 'SinglePrice';
  value: Scalars['Money'];
};

export enum SortOrder {
  Asc = 'ASC',
  Desc = 'DESC'
}

export enum SourceType {
  OrderBuy = 'orderBuy',
  OwnMall = 'ownMall',
  OwnMallGive = 'ownMallGive',
  GiftCardOrder = 'giftCardOrder',
  MemberOrder = 'memberOrder',
  Order = 'order',
  CouponBundle = 'couponBundle',
  FirstCustomerBenefit = 'firstCustomerBenefit',
  CheckinReward = 'checkinReward',
  BlindBoxAssistReward = 'blindBoxAssistReward',
  CustomerLevel = 'customerLevel'
}

export type StatisticsData = {
  __typename?: 'StatisticsData';
  totalPayment?: Maybe<Scalars['Int']>;
  totalOrders?: Maybe<Scalars['Int']>;
  totalDiscountAmount?: Maybe<Scalars['Int']>;
  customerCount?: Maybe<Scalars['Int']>;
  averageOrderValue?: Maybe<Scalars['Int']>;
};

export type StringCustomFieldConfig = CustomField & {
  __typename?: 'StringCustomFieldConfig';
  name: Scalars['String'];
  type: Scalars['String'];
  list: Scalars['Boolean'];
  length?: Maybe<Scalars['Int']>;
  label?: Maybe<Array<LocalizedString>>;
  description?: Maybe<Array<LocalizedString>>;
  readonly?: Maybe<Scalars['Boolean']>;
  internal?: Maybe<Scalars['Boolean']>;
  nullable?: Maybe<Scalars['Boolean']>;
  pattern?: Maybe<Scalars['String']>;
  options?: Maybe<Array<StringFieldOption>>;
  ui?: Maybe<Scalars['JSON']>;
};

export type StringFieldOption = {
  __typename?: 'StringFieldOption';
  value: Scalars['String'];
  label?: Maybe<Array<LocalizedString>>;
};

/** Operators for filtering on a list of String fields */
export type StringListOperators = {
  inList: Scalars['String'];
};

/** Operators for filtering on a String field */
export type StringOperators = {
  eq?: Maybe<Scalars['String']>;
  notEq?: Maybe<Scalars['String']>;
  contains?: Maybe<Scalars['String']>;
  notContains?: Maybe<Scalars['String']>;
  in?: Maybe<Array<Scalars['String']>>;
  notIn?: Maybe<Array<Scalars['String']>>;
  regex?: Maybe<Scalars['String']>;
  isNull?: Maybe<Scalars['Boolean']>;
};

export type SubCategory = {
  __typename?: 'SubCategory';
  banner?: Maybe<PictureResource>;
  title?: Maybe<Scalars['String']>;
};

export type SubCategoryInput = {
  banner?: Maybe<PictureResourceInput>;
  title?: Maybe<Scalars['String']>;
};

export type Subscription = Node & {
  __typename?: 'Subscription';
  id: Scalars['ID'];
  code?: Maybe<Scalars['String']>;
  createdAt?: Maybe<Scalars['DateTime']>;
  updatedAt?: Maybe<Scalars['DateTime']>;
  expireNumberOfPeriod?: Maybe<Scalars['Int']>;
  channels?: Maybe<Array<Channel>>;
  currentNumberOfPeriod?: Maybe<Scalars['Int']>;
  cutOffDays?: Maybe<Scalars['Int']>;
  cutOffUnit?: Maybe<FrequencyUnit>;
  discountType?: Maybe<DiscountType>;
  discountAmount?: Maybe<Scalars['Int']>;
  frequencyUnit?: Maybe<FrequencyUnit>;
  state?: Maybe<SubscriptionState>;
  customer?: Maybe<Customer>;
  orders?: Maybe<Array<Maybe<Order>>>;
  subscriptionProducts?: Maybe<Array<SubscriptionProduct>>;
  subscriptionOperations?: Maybe<Array<Maybe<SubscriptionOperation>>>;
  subscriptionPlan: SubscriptionPlan;
  frequency?: Maybe<Scalars['Int']>;
  firstShippingDate?: Maybe<Scalars['DateTime']>;
  nextShippingDate?: Maybe<Scalars['DateTime']>;
  lastShippingDate?: Maybe<Scalars['DateTime']>;
  nextPeriodShippingDate?: Maybe<Scalars['DateTime']>;
  totalMoney?: Maybe<Scalars['Float']>;
  shippingAddress?: Maybe<OrderAddress>;
};

export type SubscriptionFilterParameter = {
  id?: Maybe<IdOperators>;
  code?: Maybe<StringOperators>;
  createdAt?: Maybe<DateOperators>;
  updatedAt?: Maybe<DateOperators>;
  expireNumberOfPeriod?: Maybe<NumberOperators>;
  currentNumberOfPeriod?: Maybe<NumberOperators>;
  cutOffDays?: Maybe<NumberOperators>;
  cutOffUnit?: Maybe<StringOperators>;
  discountType?: Maybe<StringOperators>;
  discountAmount?: Maybe<NumberOperators>;
  frequencyUnit?: Maybe<StringOperators>;
  state?: Maybe<StringOperators>;
  frequency?: Maybe<NumberOperators>;
  firstShippingDate?: Maybe<DateOperators>;
  nextShippingDate?: Maybe<DateOperators>;
  lastShippingDate?: Maybe<DateOperators>;
  nextPeriodShippingDate?: Maybe<DateOperators>;
  totalMoney?: Maybe<NumberOperators>;
};

export type SubscriptionInput = {
  id: Scalars['ID'];
  createdAt?: Maybe<Scalars['DateTime']>;
  updatedAt?: Maybe<Scalars['DateTime']>;
  expireNumberOfPeriod?: Maybe<Scalars['Int']>;
  channelId?: Maybe<Scalars['ID']>;
  currentNumberOfPeriod?: Maybe<Scalars['Int']>;
  cutOffDays?: Maybe<Scalars['Int']>;
  discountType?: Maybe<DiscountType>;
  discountAmount?: Maybe<Scalars['Int']>;
  frequencyUnit?: Maybe<FrequencyUnit>;
  state?: Maybe<SubscriptionState>;
  frequency?: Maybe<Scalars['Int']>;
  firstShippingDate?: Maybe<Scalars['DateTime']>;
  nextShippingDate?: Maybe<Scalars['DateTime']>;
};

export type SubscriptionInterval = {
  __typename?: 'SubscriptionInterval';
  unit?: Maybe<FrequencyUnit>;
  frequency?: Maybe<Array<Maybe<Scalars['Int']>>>;
};

export type SubscriptionIntervalInput = {
  unit?: Maybe<FrequencyUnit>;
  frequency?: Maybe<Array<Maybe<Scalars['Int']>>>;
};

export type SubscriptionList = PaginatedList & {
  __typename?: 'SubscriptionList';
  items: Array<Subscription>;
  totalItems: Scalars['Int'];
};

export type SubscriptionListOptions = {
  /** Skips the first n results, for use in pagination */
  skip?: Maybe<Scalars['Int']>;
  /** Takes n results, for use in pagination */
  take?: Maybe<Scalars['Int']>;
  /** Specifies which properties to sort the results by */
  sort?: Maybe<SubscriptionSortParameter>;
  /** Allows the results to be filtered */
  filter?: Maybe<SubscriptionFilterParameter>;
  /** Specifies whether multiple "filter" arguments should be combines with a logical AND or OR operation. Defaults to AND. */
  filterOperator?: Maybe<LogicalOperator>;
};

export type SubscriptionOperation = Node & {
  __typename?: 'SubscriptionOperation';
  id: Scalars['ID'];
  createdAt?: Maybe<Scalars['DateTime']>;
  updatedAt?: Maybe<Scalars['DateTime']>;
  operationType?: Maybe<OperationSubscritionType>;
  operationValue?: Maybe<OperationSubscritionValue>;
  subscription?: Maybe<Subscription>;
};

export type SubscriptionPlan = Node & {
  __typename?: 'SubscriptionPlan';
  id: Scalars['ID'];
  channels?: Maybe<Array<Channel>>;
  createdAt?: Maybe<Scalars['DateTime']>;
  updatedAt?: Maybe<Scalars['DateTime']>;
  enabled?: Maybe<Scalars['Boolean']>;
  name?: Maybe<Scalars['String']>;
  channelId?: Maybe<Scalars['String']>;
  payUpFront?: Maybe<Scalars['Boolean']>;
  fixedStartDate?: Maybe<Scalars['DateTime']>;
  autoRenew?: Maybe<Scalars['Boolean']>;
  cutOffDays?: Maybe<Scalars['Int']>;
  subscriptionInterval?: Maybe<SubscriptionInterval>;
  cutOffUnit?: Maybe<FrequencyUnit>;
  periodAndDiscount?: Maybe<Array<PeriodAndDiscount>>;
};

export type SubscriptionPlanList = PaginatedList & {
  __typename?: 'SubscriptionPlanList';
  items: Array<SubscriptionPlan>;
  totalItems: Scalars['Int'];
};

export type SubscriptionProduct = Node & {
  __typename?: 'SubscriptionProduct';
  id: Scalars['ID'];
  channels?: Maybe<Array<Maybe<Channel>>>;
  productVariant?: Maybe<ProductVariant>;
  product?: Maybe<Product>;
  subscription?: Maybe<Subscription>;
  quantity?: Maybe<Scalars['Int']>;
};

export type SubscriptionSortParameter = {
  id?: Maybe<SortOrder>;
  code?: Maybe<SortOrder>;
  createdAt?: Maybe<SortOrder>;
  updatedAt?: Maybe<SortOrder>;
  expireNumberOfPeriod?: Maybe<SortOrder>;
  currentNumberOfPeriod?: Maybe<SortOrder>;
  cutOffDays?: Maybe<SortOrder>;
  discountAmount?: Maybe<SortOrder>;
  frequency?: Maybe<SortOrder>;
  firstShippingDate?: Maybe<SortOrder>;
  nextShippingDate?: Maybe<SortOrder>;
  lastShippingDate?: Maybe<SortOrder>;
  nextPeriodShippingDate?: Maybe<SortOrder>;
  totalMoney?: Maybe<SortOrder>;
};

export enum SubscriptionState {
  Unpaid = 'unpaid',
  Underway = 'underway',
  Completion = 'completion',
  Evaluate = 'evaluate',
  Cancel = 'cancel'
}

export type SubscriptionTotal = {
  __typename?: 'SubscriptionTotal';
  total?: Maybe<Scalars['Int']>;
  totalMoney?: Maybe<Scalars['Float']>;
};

export type SubscritionCancelInput = {
  subscriptionId: Scalars['ID'];
  totalMoney?: Maybe<Scalars['Int']>;
  cancelExplain?: Maybe<Scalars['String']>;
  cancelCause?: Maybe<Scalars['String']>;
  img?: Maybe<Array<Maybe<Scalars['String']>>>;
};

/** Indicates that an operation succeeded, where we do not want to return any more specific information. */
export type Success = {
  __typename?: 'Success';
  success: Scalars['Boolean'];
};

export enum SuperimposeType {
  All = 'all',
  Limit = 'limit'
}

export type Surcharge = Node & {
  __typename?: 'Surcharge';
  id: Scalars['ID'];
  createdAt: Scalars['DateTime'];
  updatedAt: Scalars['DateTime'];
  description: Scalars['String'];
  sku?: Maybe<Scalars['String']>;
  taxLines: Array<TaxLine>;
  price: Scalars['Money'];
  priceWithTax: Scalars['Money'];
  taxRate: Scalars['Float'];
};

export type SurchargeDetails = {
  __typename?: 'SurchargeDetails';
  orderLineId?: Maybe<Scalars['ID']>;
  amount?: Maybe<Scalars['Int']>;
};

export type SurchargeResult = {
  __typename?: 'SurchargeResult';
  amount?: Maybe<Scalars['Int']>;
  details?: Maybe<Array<Maybe<SurchargeDetails>>>;
};

export enum SwitchingState {
  Disable = 'disable',
  Enable = 'enable',
  Shelf = 'shelf',
  TakeOffTheShelf = 'takeOffTheShelf'
}

export enum SymbolType {
  In = 'in',
  Out = 'out',
  Frz = 'frz'
}

export type Tag = Node & {
  __typename?: 'Tag';
  id: Scalars['ID'];
  createdAt: Scalars['DateTime'];
  updatedAt: Scalars['DateTime'];
  value: Scalars['String'];
};

export type TagList = PaginatedList & {
  __typename?: 'TagList';
  items: Array<Tag>;
  totalItems: Scalars['Int'];
};

export type TaxCategory = Node & {
  __typename?: 'TaxCategory';
  id: Scalars['ID'];
  createdAt: Scalars['DateTime'];
  updatedAt: Scalars['DateTime'];
  name: Scalars['String'];
  isDefault: Scalars['Boolean'];
  customFields?: Maybe<Scalars['JSON']>;
};

export type TaxLine = {
  __typename?: 'TaxLine';
  description: Scalars['String'];
  taxRate: Scalars['Float'];
};

export type TaxRate = Node & {
  __typename?: 'TaxRate';
  id: Scalars['ID'];
  createdAt: Scalars['DateTime'];
  updatedAt: Scalars['DateTime'];
  name: Scalars['String'];
  enabled: Scalars['Boolean'];
  value: Scalars['Float'];
  category: TaxCategory;
  zone: Zone;
  customerGroup?: Maybe<CustomerGroup>;
  customFields?: Maybe<Scalars['JSON']>;
};

export type TaxRateList = PaginatedList & {
  __typename?: 'TaxRateList';
  items: Array<TaxRate>;
  totalItems: Scalars['Int'];
};

export type TemplateInfo = {
  __typename?: 'TemplateInfo';
  couponGrants?: Maybe<Array<Maybe<Scalars['String']>>>;
  blindBoxActivityBooking?: Maybe<Array<Maybe<Scalars['String']>>>;
};

export enum TemplateType {
  CouponGrants = 'couponGrants',
  BlindBoxActivityBooking = 'blindBoxActivityBooking',
  ShoppingCreditsNotification = 'shoppingCreditsNotification'
}

export type TextCustomFieldConfig = CustomField & {
  __typename?: 'TextCustomFieldConfig';
  name: Scalars['String'];
  type: Scalars['String'];
  list: Scalars['Boolean'];
  label?: Maybe<Array<LocalizedString>>;
  description?: Maybe<Array<LocalizedString>>;
  readonly?: Maybe<Scalars['Boolean']>;
  internal?: Maybe<Scalars['Boolean']>;
  nullable?: Maybe<Scalars['Boolean']>;
  ui?: Maybe<Scalars['JSON']>;
};


export type Traces = {
  __typename?: 'Traces';
  acceptStation?: Maybe<Scalars['String']>;
  acceptTime?: Maybe<Scalars['DateTime']>;
};

export enum TrackingPageType {
  ActivePage = 'activePage',
  ShoppingCartPage = 'shoppingCartPage',
  PersonalCenterPage = 'personalCenterPage',
  MemberCardPage = 'memberCardPage'
}

export type TransactionCustomerStatistic = {
  __typename?: 'TransactionCustomerStatistic';
  payCustomerCountRatio?: Maybe<Scalars['Float']>;
  customerPrice?: Maybe<Scalars['Float']>;
  conversionRateOfVisitsToPayments?: Maybe<Scalars['Float']>;
  payCustomerCount?: Maybe<Scalars['Int']>;
  payAmount?: Maybe<Scalars['Float']>;
};

export type TransitionOrderToStateResult = Order | OrderStateTransitionError;

export enum TrendSource {
  PageViews = 'pageViews',
  VisitorsCount = 'visitorsCount',
  TotalPayment = 'totalPayment',
  PaymentCustomerCount = 'paymentCustomerCount'
}

export type TrendStatistics = {
  __typename?: 'TrendStatistics';
  toDay?: Maybe<Array<Maybe<TrendStatisticsItem>>>;
  yesterday?: Maybe<Array<Maybe<TrendStatisticsItem>>>;
};

export type TrendStatisticsItem = {
  __typename?: 'TrendStatisticsItem';
  statisticsTime?: Maybe<Scalars['DateTime']>;
  value?: Maybe<Scalars['Int']>;
};

export enum TrendType {
  DayParting = 'dayParting',
  Cumulative = 'cumulative'
}

export type UMengConfig = Node & {
  __typename?: 'UMengConfig';
  id: Scalars['ID'];
  appKey?: Maybe<Scalars['String']>;
  apiSecurity?: Maybe<Scalars['String']>;
  apiKey?: Maybe<Scalars['String']>;
  channelId?: Maybe<Scalars['String']>;
};

export type UMengConfigInput = {
  id?: Maybe<Scalars['ID']>;
  appKey: Scalars['String'];
  apiSecurity: Scalars['String'];
  apiKey: Scalars['String'];
};

export type UpdateAddressCustomFieldsInput = {
  district?: Maybe<Scalars['String']>;
};

export type UpdateAddressInput = {
  id: Scalars['ID'];
  fullName?: Maybe<Scalars['String']>;
  company?: Maybe<Scalars['String']>;
  streetLine1?: Maybe<Scalars['String']>;
  streetLine2?: Maybe<Scalars['String']>;
  city?: Maybe<Scalars['String']>;
  province?: Maybe<Scalars['String']>;
  postalCode?: Maybe<Scalars['String']>;
  countryCode?: Maybe<Scalars['String']>;
  phoneNumber?: Maybe<Scalars['String']>;
  defaultShippingAddress?: Maybe<Scalars['Boolean']>;
  defaultBillingAddress?: Maybe<Scalars['Boolean']>;
  customFields?: Maybe<UpdateAddressCustomFieldsInput>;
};

export type UpdateCustomerCustomFieldsInput = {
  distributorId?: Maybe<Scalars['ID']>;
  points?: Maybe<Scalars['Int']>;
  isModified?: Maybe<Scalars['Boolean']>;
  isSettleCommission?: Maybe<Scalars['Boolean']>;
  headPortrait?: Maybe<Scalars['String']>;
  gender?: Maybe<Scalars['String']>;
  dateBirth?: Maybe<Scalars['DateTime']>;
  wechatCode?: Maybe<Scalars['String']>;
  area?: Maybe<Scalars['String']>;
};

export type UpdateCustomerEmailAddressResult = Success | IdentifierChangeTokenInvalidError | IdentifierChangeTokenExpiredError | NativeAuthStrategyError;

export type UpdateCustomerInput = {
  title?: Maybe<Scalars['String']>;
  firstName?: Maybe<Scalars['String']>;
  lastName?: Maybe<Scalars['String']>;
  phoneNumber?: Maybe<Scalars['String']>;
  customFields?: Maybe<UpdateCustomerCustomFieldsInput>;
};

export type UpdateCustomerPasswordResult = Success | InvalidCredentialsError | PasswordValidationError | NativeAuthStrategyError;

export type UpdateOrderCustomFieldsInput = {
  remark?: Maybe<Scalars['String']>;
  orderPromotionResultId?: Maybe<Scalars['ID']>;
  merchantRemarks?: Maybe<Scalars['String']>;
  buyType?: Maybe<Scalars['String']>;
  isHidden?: Maybe<Scalars['Boolean']>;
  isReturnPoints?: Maybe<Scalars['Boolean']>;
  closeReasonType?: Maybe<Scalars['String']>;
  receiverName?: Maybe<Scalars['String']>;
  receiverPhone?: Maybe<Scalars['String']>;
  shippingFeeType?: Maybe<Scalars['String']>;
  subscriptionPlanId?: Maybe<Scalars['ID']>;
  subscriptionId?: Maybe<Scalars['ID']>;
  currentPeriods?: Maybe<Scalars['Int']>;
  periods?: Maybe<Scalars['Int']>;
  deliveryInterval?: Maybe<Scalars['Int']>;
  firstShippingDate?: Maybe<Scalars['DateTime']>;
  logisticsId?: Maybe<Scalars['ID']>;
  timeoutPeriodToBePaid?: Maybe<Scalars['DateTime']>;
  timeoutPeriodToBeReceived?: Maybe<Scalars['DateTime']>;
  isAvailableAfterSale?: Maybe<Scalars['Boolean']>;
  confirmReceiptTime?: Maybe<Scalars['DateTime']>;
  signingTime?: Maybe<Scalars['DateTime']>;
  timeToPlaceOrder?: Maybe<Scalars['DateTime']>;
  originSubTotal?: Maybe<Scalars['Int']>;
  newOrderCode?: Maybe<Scalars['String']>;
};

export type UpdateOrderInput = {
  customFields?: Maybe<UpdateOrderCustomFieldsInput>;
};

export type UpdateOrderItemsResult = Order | OrderModificationError | OrderLimitError | NegativeQuantityError | InsufficientStockError;

export type UpdateReviewProductInput = {
  id: Scalars['ID'];
  title?: Maybe<Scalars['String']>;
  description?: Maybe<Scalars['String']>;
  stars?: Maybe<Scalars['Int']>;
  reviewImgs?: Maybe<Array<Maybe<Scalars['String']>>>;
  customerNameIsPublic?: Maybe<Scalars['Boolean']>;
};


export type UpsertSubscriptionPlanInput = {
  id?: Maybe<Scalars['ID']>;
  name: Scalars['String'];
  enabled: Scalars['Boolean'];
  payUpFront: Scalars['Boolean'];
  fixedStartDate?: Maybe<Scalars['DateTime']>;
  autoRenew?: Maybe<Scalars['Boolean']>;
  cutOffDays?: Maybe<Scalars['Int']>;
  subscriptionInterval?: Maybe<SubscriptionIntervalInput>;
  cutOffUnit?: Maybe<FrequencyUnit>;
  periodAndDiscount?: Maybe<Array<Maybe<PeriodAndDiscountInput>>>;
};

export type User = Node & {
  __typename?: 'User';
  id: Scalars['ID'];
  createdAt: Scalars['DateTime'];
  updatedAt: Scalars['DateTime'];
  identifier: Scalars['String'];
  verified: Scalars['Boolean'];
  roles: Array<Role>;
  lastLogin?: Maybe<Scalars['DateTime']>;
  authenticationMethods: Array<AuthenticationMethod>;
  customFields?: Maybe<Scalars['JSON']>;
};

export type UserCoupon = Node & {
  __typename?: 'UserCoupon';
  id: Scalars['ID'];
  createdAt?: Maybe<Scalars['DateTime']>;
  updatedAt?: Maybe<Scalars['DateTime']>;
  claimAt?: Maybe<Scalars['DateTime']>;
  useAt?: Maybe<Scalars['DateTime']>;
  isNew?: Maybe<Scalars['Boolean']>;
  validFromAt?: Maybe<Scalars['DateTime']>;
  maturityAt?: Maybe<Scalars['DateTime']>;
  maturityType?: Maybe<ValidityPeriodType>;
  coupon?: Maybe<Coupon>;
  order?: Maybe<Order>;
  state?: Maybe<UserCouponState>;
  customer?: Maybe<Customer>;
};

export type UserCouponFilterParameter = {
  id?: Maybe<IdOperators>;
  createdAt?: Maybe<DateOperators>;
  updatedAt?: Maybe<DateOperators>;
  claimAt?: Maybe<DateOperators>;
  useAt?: Maybe<DateOperators>;
  isNew?: Maybe<BooleanOperators>;
  validFromAt?: Maybe<DateOperators>;
  maturityAt?: Maybe<DateOperators>;
  maturityType?: Maybe<StringOperators>;
  state?: Maybe<StringOperators>;
};

export type UserCouponList = PaginatedList & {
  __typename?: 'UserCouponList';
  items: Array<UserCoupon>;
  totalItems: Scalars['Int'];
};

export type UserCouponListOptions = {
  /** Skips the first n results, for use in pagination */
  skip?: Maybe<Scalars['Int']>;
  /** Takes n results, for use in pagination */
  take?: Maybe<Scalars['Int']>;
  /** Specifies which properties to sort the results by */
  sort?: Maybe<UserCouponSortParameter>;
  /** Allows the results to be filtered */
  filter?: Maybe<UserCouponFilterParameter>;
  /** Specifies whether multiple "filter" arguments should be combines with a logical AND or OR operation. Defaults to AND. */
  filterOperator?: Maybe<LogicalOperator>;
};

export type UserCouponSortParameter = {
  id?: Maybe<SortOrder>;
  createdAt?: Maybe<SortOrder>;
  updatedAt?: Maybe<SortOrder>;
  claimAt?: Maybe<SortOrder>;
  useAt?: Maybe<SortOrder>;
  validFromAt?: Maybe<SortOrder>;
  maturityAt?: Maybe<SortOrder>;
};

export enum UserCouponState {
  NotStarted = 'notStarted',
  Unused = 'unused',
  Expire = 'expire',
  Lock = 'lock',
  HaveUsed = 'haveUsed'
}

export type ValidityPeriod = {
  __typename?: 'ValidityPeriod';
  type: ValidityPeriodType;
  startTime?: Maybe<Scalars['DateTime']>;
  endTime?: Maybe<Scalars['DateTime']>;
  numberOfDays?: Maybe<Scalars['Int']>;
};

export type ValidityPeriodInput = {
  type: ValidityPeriodType;
  startTime?: Maybe<Scalars['DateTime']>;
  endTime?: Maybe<Scalars['DateTime']>;
  numberOfDays?: Maybe<Scalars['Int']>;
};

export enum ValidityPeriodType {
  LongTime = 'longTime',
  ValidDays = 'validDays',
  TemporalInterval = 'temporalInterval'
}

/**
 * Returned if the verification token (used to verify a Customer's email address) is valid, but has
 * expired according to the `verificationTokenDuration` setting in the AuthOptions.
 */
export type VerificationTokenExpiredError = ErrorResult & {
  __typename?: 'VerificationTokenExpiredError';
  errorCode: ErrorCode;
  message: Scalars['String'];
};

/**
 * Returned if the verification token (used to verify a Customer's email address) is either
 * invalid or does not match any expected tokens.
 */
export type VerificationTokenInvalidError = ErrorResult & {
  __typename?: 'VerificationTokenInvalidError';
  errorCode: ErrorCode;
  message: Scalars['String'];
};

export type VerifyCustomerAccountResult = CurrentUser | VerificationTokenInvalidError | VerificationTokenExpiredError | MissingPasswordError | PasswordValidationError | PasswordAlreadySetError | NativeAuthStrategyError;

export type VirtualCurrencyAccount = Node & {
  __typename?: 'VirtualCurrencyAccount';
  id: Scalars['ID'];
  createdAt?: Maybe<Scalars['DateTime']>;
  updatedAt?: Maybe<Scalars['DateTime']>;
  customer?: Maybe<Customer>;
  customerId?: Maybe<Scalars['ID']>;
  virtualCurrencyCode?: Maybe<VirtualCurrencyCode>;
  amount?: Maybe<Scalars['Int']>;
};

export enum VirtualCurrencyCode {
  ShoppingCredits = 'shoppingCredits'
}

export type VirtualCurrencyHistory = Node & {
  __typename?: 'VirtualCurrencyHistory';
  id: Scalars['ID'];
  createdAt?: Maybe<Scalars['DateTime']>;
  updatedAt?: Maybe<Scalars['DateTime']>;
  customer?: Maybe<Customer>;
  customerId?: Maybe<Scalars['ID']>;
  virtualCurrencyAccount?: Maybe<VirtualCurrencyAccount>;
  virtualCurrencyAccountId?: Maybe<Scalars['ID']>;
  amount?: Maybe<Scalars['Int']>;
  symbolType?: Maybe<SymbolType>;
  beforeAmount?: Maybe<Scalars['Int']>;
  afterAmount?: Maybe<Scalars['Int']>;
  remark?: Maybe<Scalars['String']>;
  sourceType?: Maybe<VirtualCurrencySourceType>;
  sourceValue?: Maybe<Scalars['String']>;
  channelId?: Maybe<Scalars['ID']>;
};

export type VirtualCurrencyHistoryFilterParameter = {
  id?: Maybe<IdOperators>;
  createdAt?: Maybe<DateOperators>;
  updatedAt?: Maybe<DateOperators>;
  customerId?: Maybe<IdOperators>;
  virtualCurrencyAccountId?: Maybe<IdOperators>;
  amount?: Maybe<NumberOperators>;
  symbolType?: Maybe<StringOperators>;
  beforeAmount?: Maybe<NumberOperators>;
  afterAmount?: Maybe<NumberOperators>;
  remark?: Maybe<StringOperators>;
  sourceType?: Maybe<StringOperators>;
  sourceValue?: Maybe<StringOperators>;
  channelId?: Maybe<IdOperators>;
};

export type VirtualCurrencyHistoryList = PaginatedList & {
  __typename?: 'VirtualCurrencyHistoryList';
  items: Array<VirtualCurrencyHistory>;
  totalItems: Scalars['Int'];
};

export type VirtualCurrencyHistoryListOptions = {
  /** Skips the first n results, for use in pagination */
  skip?: Maybe<Scalars['Int']>;
  /** Takes n results, for use in pagination */
  take?: Maybe<Scalars['Int']>;
  /** Specifies which properties to sort the results by */
  sort?: Maybe<VirtualCurrencyHistorySortParameter>;
  /** Allows the results to be filtered */
  filter?: Maybe<VirtualCurrencyHistoryFilterParameter>;
  /** Specifies whether multiple "filter" arguments should be combines with a logical AND or OR operation. Defaults to AND. */
  filterOperator?: Maybe<LogicalOperator>;
};

export type VirtualCurrencyHistorySortParameter = {
  id?: Maybe<SortOrder>;
  createdAt?: Maybe<SortOrder>;
  updatedAt?: Maybe<SortOrder>;
  customerId?: Maybe<SortOrder>;
  virtualCurrencyAccountId?: Maybe<SortOrder>;
  amount?: Maybe<SortOrder>;
  beforeAmount?: Maybe<SortOrder>;
  afterAmount?: Maybe<SortOrder>;
  remark?: Maybe<SortOrder>;
  sourceValue?: Maybe<SortOrder>;
  channelId?: Maybe<SortOrder>;
};

export enum VirtualCurrencySourceType {
  Order = 'order',
  Reclaimed = 'reclaimed',
  MerchantVoluntaryReclaimed = 'merchantVoluntaryReclaimed',
  ExternalSystem = 'externalSystem',
  AdminOperation = 'adminOperation',
  OrderDeduction = 'orderDeduction',
  Returned = 'returned',
  MerchantVoluntaryRefundReturn = 'merchantVoluntaryRefundReturn',
  Expired = 'expired',
  CustomerLevel = 'customerLevel',
  Checkin = 'checkin',
  ExternalOrder = 'externalOrder',
}

export type VirtualTarget = Coupon | MembershipPlan;

export enum VirtualTargetType {
  MemberCard = 'memberCard',
  Coupon = 'coupon'
}

export type WangDianTongConfig = {
  __typename?: 'WangDianTongConfig';
  id: Scalars['ID'];
  createdAt: Scalars['DateTime'];
  updatedAt: Scalars['DateTime'];
  appKey: Scalars['String'];
  appSecret: Scalars['String'];
  sellerId?: Maybe<Scalars['String']>;
  shopId: Scalars['String'];
};

export type WangDianTongConfigInput = {
  id?: Maybe<Scalars['ID']>;
  appKey: Scalars['String'];
  appSecret: Scalars['String'];
  sellerId: Scalars['String'];
  shopId: Scalars['String'];
};

export type WeappDynamicRouteConfig = Node & {
  __typename?: 'WeappDynamicRouteConfig';
  id: Scalars['ID'];
  tabbarConfig?: Maybe<Array<WeappTabbarRouteConfig>>;
  eventTrackingConfig?: Maybe<Array<WeappEventTrackingRouteConfig>>;
};

export type WeappDynamicRouteConfigInput = {
  id?: Maybe<Scalars['ID']>;
  tabbarConfig?: Maybe<Array<WeappTabbarRouteConfigInput>>;
  eventTrackingConfig?: Maybe<Array<WeappEventTrackingRouteConfigInput>>;
}

export type WeappTabbarRouteConfig = {
  iconUrl?: Maybe<Scalars['String']>;
  selectedIconUrl?: Maybe<Scalars['String']>;
  text?: Maybe<Scalars['String']>;
  routeValue?: Maybe<Scalars['String']>;
  path?: Maybe<Scalars['String']>;
  isBadge?: Maybe<Scalars['Boolean']>;
};

export type WeappTabbarRouteConfigInput = {
  iconUrl?: Maybe<Scalars['String']>;
  selectedIconUrl?: Maybe<Scalars['String']>;
  text?: Maybe<Scalars['String']>;
  routeValue?: Maybe<Scalars['String']>;
  path?: Maybe<Scalars['String']>;
  isBadge?: Maybe<Scalars['Boolean']>;
}

export type WeappEventTrackingRouteConfig = {
  text?: Maybe<Scalars['String']>;
  eventScheme?: Maybe<Scalars['String']>;
  routeValue?: Maybe<Scalars['String']>;
}

export type WeappEventTrackingRouteConfigInput = {
  text?: Maybe<Scalars['String']>;
  eventScheme?: Maybe<Scalars['String']>;
  routeValue?: Maybe<Scalars['String']>;
}

export type WeChatConfig = Node & {
  __typename?: 'WeChatConfig';
  id: Scalars['ID'];
  wechatProgram?: Maybe<WechatProgram>;
  wechatPayment?: Maybe<WechatPayment>;
  wechatShop?: Maybe<WechatShop>;
  msgJumpPath?: Maybe<Scalars['String']>;
};

export type WeChatConfigFilterParameter = {
  id?: Maybe<IdOperators>;
  msgJumpPath?: Maybe<StringOperators>;
};

export type WeChatConfigInput = {
  wechatProgram: WechatProgramInput;
  wechatShop?: Maybe<WechatShopInput>;
};

export type WeChatConfigList = PaginatedList & {
  __typename?: 'WeChatConfigList';
  items: Array<WeChatConfig>;
  totalItems: Scalars['Int'];
};

export type WeChatConfigListOptions = {
  /** Skips the first n results, for use in pagination */
  skip?: Maybe<Scalars['Int']>;
  /** Takes n results, for use in pagination */
  take?: Maybe<Scalars['Int']>;
  /** Specifies which properties to sort the results by */
  sort?: Maybe<WeChatConfigSortParameter>;
  /** Allows the results to be filtered */
  filter?: Maybe<WeChatConfigFilterParameter>;
  /** Specifies whether multiple "filter" arguments should be combines with a logical AND or OR operation. Defaults to AND. */
  filterOperator?: Maybe<LogicalOperator>;
};

export type WeChatConfigSortParameter = {
  id?: Maybe<SortOrder>;
  msgJumpPath?: Maybe<SortOrder>;
};

export type WeChatInitPayInfo = {
  __typename?: 'WeChatInitPayInfo';
  isSkipPay?: Maybe<Scalars['Boolean']>;
  status?: Maybe<Scalars['Int']>;
  appId?: Maybe<Scalars['String']>;
  timeStamp?: Maybe<Scalars['Int']>;
  nonceStr?: Maybe<Scalars['String']>;
  package?: Maybe<Scalars['String']>;
  signType?: Maybe<Scalars['String']>;
  paySign?: Maybe<Scalars['String']>;
  orderCode?: Maybe<Scalars['String']>;
};

export enum WeChatPaymentType {
  WechatProgram = 'wechatProgram',
  WechatOfficialAccounts = 'wechatOfficialAccounts'
}

export type WechatPayment = {
  __typename?: 'WechatPayment';
  apiV3Key?: Maybe<Scalars['String']>;
  merchantAppId?: Maybe<Scalars['String']>;
  merchantName?: Maybe<Scalars['String']>;
  apiclientCert?: Maybe<Scalars['String']>;
  apiclientKey?: Maybe<Scalars['String']>;
};

export type WechatProgram = {
  __typename?: 'WechatProgram';
  weChatAppId?: Maybe<Scalars['String']>;
  weChatAppSecret?: Maybe<Scalars['String']>;
};

export type WechatProgramInput = {
  weChatAppId: Scalars['String'];
  weChatAppSecret: Scalars['String'];
};

export type WechatShop = {
  __typename?: 'WechatShop';
  shopAppId?: Maybe<Scalars['String']>;
  shopAppSecret?: Maybe<Scalars['String']>;
};

export type WechatShopInput = {
  shopAppId: Scalars['String'];
  shopAppSecret: Scalars['String'];
};

export type Zone = Node & {
  __typename?: 'Zone';
  id: Scalars['ID'];
  createdAt: Scalars['DateTime'];
  updatedAt: Scalars['DateTime'];
  name: Scalars['String'];
  members: Array<Region>;
  customFields?: Maybe<Scalars['JSON']>;
};

export type PendingOrdersNum = {
  __typename?: 'pendingOrdersNum';
  toBePaidNumber?: Maybe<Scalars['Int']>;
  toBeShippedNumber?: Maybe<Scalars['Int']>;
  toBeReceivedNumber?: Maybe<Scalars['Int']>;
  toBeEvaluatedNumber?: Maybe<Scalars['Int']>;
  toBeAfterSalesNumber?: Maybe<Scalars['Int']>;
};

export enum ExternalOrderRewardType {
  ShoppingCredits = 'shoppingCredits'
}

export type VerifyExternalOrderRewardSuccess = {
  __typename?: 'VerifyExternalOrderRewardSuccess';
  type?: ExternalOrderRewardType;
  count?: Scalars['Int'];
  success:  Scalars['Boolean'];
  code?: VerifyOrderRewardErrorCode;
}

export enum VerifyOrderRewardErrorCode {
  ExternalOrderNotFound = 'EXTERNAL_ORDER_NOT_FOUND',
  ExternalOrderAlreadyRedeemed = 'EXTERNAL_ORDER_ALREADY_REDEEMED',
  ExternalOrderRewardAlreadyReceived = 'EXTERNAL_ORDER_REWARD_ALREADY_RECEIVED'
}

export type VerifyExternalOrderRewardError = {
  extensions: {code: VerifyOrderRewardErrorCode};
  message: Scalars['String'];
};

export type VerifyExternalOrderRewardResult = VerifyExternalOrderRewardSuccess;
