import {MemberService} from '@scmally/member';
import {VirtualCurrencyService} from '@scmally/virtual-currency';
import {LanguageCode} from '@vendure/common/lib/generated-types';
import {OrderService, PromotionOrderAction, RequestContextService, TransactionalConnection} from '@vendure/core';
import {
  BlindBoxOrderService,
  CommonService,
  CouponService,
  CustomerProductVariantService,
  MemberPriceService,
  OrderPromotionResultService,
  PointsConfigService,
  PointsProductService,
} from '../../service';
import {AutomaticActionService} from '../automatic.action.service';
let automaticActionService: AutomaticActionService;
//满足订单条件最大折扣
export const automaticAction = new PromotionOrderAction({
  code: 'automatic_action',
  description: [{languageCode: LanguageCode.en, value: 'automatic_action'}],
  args: {},
  init(injector) {
    automaticActionService = new AutomaticActionService(
      injector.get(TransactionalConnection),
      injector.get(OrderService),
      injector.get(MemberService),
      injector.get(OrderPromotionResultService),
      injector.get(CouponService),
      injector.get(RequestContextService),
      injector.get(MemberPriceService),
      injector.get(PointsProductService),
      injector.get(PointsConfigService),
      injector.get(BlindBoxOrderService),
      injector.get(CustomerProductVariantService),
      injector.get(CommonService),
      injector.get(VirtualCurrencyService),
    );
  },
  async execute(ctx, order, args) {
    const upperBound = ctx.channel.pricesIncludeTax ? order.subTotalWithTax : order.subTotal;
    const discountAmount = (await automaticActionService.getDiscountAmount(ctx, order)) || 0;
    return -Math.min(upperBound, discountAmount);
  },
});
