import {VirtualCurrencyService} from '@scmally/virtual-currency';
import {RequestContextService} from '@vendure/core';
import {FetchCheckerAndActor, PromAction, PromCondition} from 'prom-engine';
import {PromotionType} from '../generated-shop-types';
import {BlindBoxOrderService, CouponService, CustomerProductVariantService, MemberPriceService} from '../service';
import {
  BlindBoxAction,
  BuyXDiscountYAction,
  CouponAction,
  FullMinusAction,
  MarkUpAction,
  MemberAction,
  MemberPriceAction,
  PointsExchangeAction,
  SelectiveGiftAction,
  ShoppingCreditsClaimAction,
} from './automatic-action';
import {PackageDiscountAction} from './automatic-action/package-discount-action';
import {ShoppingCreditsDeductionAction} from './automatic-action/shopping-credits-deduction-action';
import {
  BlindBoxCondition,
  BuyXDiscountYCondition,
  CouponCondition,
  FullMinusCondition,
  MarkUpCondition,
  MemberCondition,
  MemberPriceCondition,
  PackageDiscountCondition,
  PointsExchangeCondition,
  SelectiveGiftCondition,
  ShoppingCreditsClaimCondition,
} from './automatic-conditions';
import {ShoppingCreditsDeductionCondition} from './automatic-conditions/shoppinng-credits-deduction-condition';

export class ActivityFetcher implements FetchCheckerAndActor {
  constructor(
    private couponService: CouponService,
    private requestContextService: RequestContextService,
    private memberPriceService: MemberPriceService,
    private blindBoxOrderService: BlindBoxOrderService,
    private customerProductVariantService: CustomerProductVariantService,
    private virtualCurrencyService: VirtualCurrencyService,
  ) {}
  fetchActor(actor: string): PromAction {
    if (actor === PromotionType.Member) {
      return new MemberAction();
    } else if (actor === PromotionType.FullDiscountPresent || actor === PromotionType.ActuallyPaid) {
      return new FullMinusAction(this.customerProductVariantService);
    } else if (actor === PromotionType.DiscountActivity) {
      return new BuyXDiscountYAction();
    } else if (actor === PromotionType.SelectiveGift) {
      return new SelectiveGiftAction(this.customerProductVariantService);
    } else if (actor === PromotionType.Coupon) {
      return new CouponAction(this.couponService, this.requestContextService);
    } else if (actor === PromotionType.PurchaseAtAPremium) {
      return new MarkUpAction(this.customerProductVariantService);
    } else if (actor === PromotionType.PackageDiscount) {
      return new PackageDiscountAction();
    } else if (actor === PromotionType.MemberPrice) {
      return new MemberPriceAction(this.memberPriceService, this.requestContextService);
    } else if (actor === PromotionType.PointsExchange) {
      return new PointsExchangeAction();
    } else if (actor === PromotionType.BlindBox) {
      return new BlindBoxAction(this.blindBoxOrderService);
    } else if (actor === PromotionType.ShoppingCreditsClaim) {
      return new ShoppingCreditsClaimAction();
    } else if (actor === PromotionType.ShoppingCreditsDeduction) {
      return new ShoppingCreditsDeductionAction(this.virtualCurrencyService);
    }
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    return null as any;
  }

  fetchChecker(checker: string): PromCondition {
    if (checker === PromotionType.Member) {
      return new MemberCondition();
    } else if (checker === PromotionType.FullDiscountPresent || checker === PromotionType.ActuallyPaid) {
      return new FullMinusCondition();
    } else if (checker === PromotionType.DiscountActivity) {
      return new BuyXDiscountYCondition();
    } else if (checker === PromotionType.SelectiveGift) {
      return new SelectiveGiftCondition();
    } else if (checker === PromotionType.Coupon) {
      return new CouponCondition();
    } else if (checker === PromotionType.PurchaseAtAPremium) {
      return new MarkUpCondition();
    } else if (checker === PromotionType.PackageDiscount) {
      return new PackageDiscountCondition();
    } else if (checker === PromotionType.MemberPrice) {
      return new MemberPriceCondition(this.memberPriceService, this.requestContextService);
    } else if (checker === PromotionType.PointsExchange) {
      return new PointsExchangeCondition();
    } else if (checker === PromotionType.BlindBox) {
      return new BlindBoxCondition();
    } else if (checker === PromotionType.ShoppingCreditsClaim) {
      return new ShoppingCreditsClaimCondition();
    } else if (checker === PromotionType.ShoppingCreditsDeduction) {
      return new ShoppingCreditsDeductionCondition();
    }
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    return null as any;
  }
}
