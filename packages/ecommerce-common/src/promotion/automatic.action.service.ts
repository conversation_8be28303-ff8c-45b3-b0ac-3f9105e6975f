import {Inject, Injectable, forwardRef} from '@nestjs/common';
import {MemberService} from '@scmally/member';
import {VirtualCurrencyService} from '@scmally/virtual-currency';
import {VirtualCurrencyCode} from '../generated-admin-types';
import {
  ID,
  Logger,
  Order,
  OrderService,
  Product,
  Promotion,
  RequestContext,
  RequestContextService,
  TransactionalConnection,
  idsAreEqual,
} from '@vendure/core';
import {DateTime} from 'luxon';
import {BuyType, IDType, PromEngine, PromInstance, PromOrder, PromProductScope, SuperimposeType} from 'prom-engine';
import {OrderPromotionResult, ShoppingCreditsClaimActivity, ShoppingCreditsDeductionActivity} from '../entities';
import {
  ActivityStatus,
  OrderCustomFields,
  OrderPromotionResultInput,
  OrderPurchaseType,
  SurchargeDetails,
} from '../generated-admin-types';
import {
  MembershipPlanState,
  OrderBuyType,
  OrderLineCustomFields,
  ProductCustomFields,
  ProductVariantCustomFields,
  PromResult,
  PromotionCustomFields,
  PromotionType,
  PurchasePattern,
  VirtualTargetType,
} from '../generated-shop-types';
import {
  BlindBoxOrderService,
  CommonService,
  CouponService,
  CustomerProductVariantService,
  MemberPriceService,
  OrderPromotionResultService,
  PointsConfigService,
  PointsProductService,
} from '../service';
import {ActivityFetcher} from './activity.fetcher';
import {SurchargeAction} from './automatic-action';
import {ProductType} from './type';
@Injectable()
export class AutomaticActionService {
  promEngine: PromEngine;
  constructor(
    private connection: TransactionalConnection,
    private orderService: OrderService,
    private memberService: MemberService,
    @Inject(forwardRef(() => OrderPromotionResultService))
    private orderPromotionResultService: OrderPromotionResultService,
    @Inject(forwardRef(() => CouponService))
    private couponService: CouponService,
    private requestContextService: RequestContextService,
    @Inject(forwardRef(() => MemberPriceService))
    private memberPriceService: MemberPriceService,
    @Inject(forwardRef(() => PointsProductService))
    private pointsProductService: PointsProductService,
    @Inject(forwardRef(() => PointsConfigService))
    private pointsConfigService: PointsConfigService,
    @Inject(forwardRef(() => BlindBoxOrderService))
    private blindBoxOrderService: BlindBoxOrderService,
    @Inject(forwardRef(() => CustomerProductVariantService))
    private customerProductVariantService: CustomerProductVariantService,
    @Inject(forwardRef(() => CommonService))
    private commonService: CommonService,
    private virtualCurrencyService: VirtualCurrencyService,
  ) {
    this.promEngine = new PromEngine(
      null as never,
      new ActivityFetcher(
        this.couponService,
        this.requestContextService,
        this.memberPriceService,
        this.blindBoxOrderService,
        this.customerProductVariantService,
        this.virtualCurrencyService,
      ),
    );
  }
  async getDiscountAmount(ctx: RequestContext, order: Order, oldOrderPromotionResult?: OrderPromotionResult) {
    const result = await this.getPromResult(ctx, order, oldOrderPromotionResult);
    return Math.floor(result?.discountAmount ?? 0);
  }

  // 获取优惠结果
  async getPromResult(ctx: RequestContext, order: Order, oldOrderPromotionResult?: OrderPromotionResult) {
    if (!order) throw new Error('order not found');
    if (!order.customer) throw new Error('order.customer not found');
    const productIds = order.lines.map(line => line.productVariant.productId);
    const products = await this.connection.getRepository(ctx, Product).findByIds(productIds);
    const promOrder: PromOrder = {
      id: Number(order.id),
      lines: order.lines.map(line => ({
        lineId: Number(line.id),
        skuId: Number(line.productVariant.id),
        productId: Number(line.productVariant.productId),
        isMemberProduct:
          (products.find(product => product.id === line.productVariant.productId)?.customFields as ProductCustomFields)
            .isVipProduct || false,
        count: line.quantity || line.orderPlacedQuantity,
        price: line.linePrice,
        buyType:
          (line.customFields as OrderLineCustomFields).purchasePattern === PurchasePattern.PurchasePremium
            ? BuyType.markUp
            : BuyType.common,
        promId: (line.customFields as OrderLineCustomFields).promInstanceIds as IDType[],
        productType:
          (line.productVariant.customFields as ProductVariantCustomFields).virtualTargetType ===
          VirtualTargetType.Coupon
            ? ProductType.Coupon
            : (line.productVariant.customFields as ProductVariantCustomFields).virtualTargetType ===
              VirtualTargetType.MemberCard
            ? ProductType.Member
            : ProductType.Normal,
      })),
      oldOrderPromotionResult: oldOrderPromotionResult,
    } as PromOrder;
    const preResult = await this.orderPromotionResultService.getResultByOrderId(ctx, order.id);
    if (order.state !== 'AddingItems') {
      // 如果订单已经支付完成，或者订单状态为取消，则不再计算优惠
      if (order.orderPlacedAt || order.state === 'Cancelled') {
        return preResult?.promResult;
      }
      // Logger.debug(`order ${order.id} preResult: ${JSON.stringify(preResult)}`);
      //eslint-disable-next-line @typescript-eslint/no-explicit-any
      const oldPreResult = preResult!.promResult as any;
      const price = order.surcharges.map(s => s.listPrice).reduce((a, b) => a + b, 0);
      const surchargeAction = new SurchargeAction();
      const newPreResult = await surchargeAction.surchargeOrderPrice(promOrder, oldPreResult, order);
      const surchargeDetails = newPreResult.map(line => {
        return {
          orderLineId: line.orderLineId as ID,
          amount: line.discountAmount,
        } as SurchargeDetails;
      });
      if (preResult?.promResult) {
        preResult.promResult.surcharge = {
          amount: price,
          details: surchargeDetails,
        };
      }
      await this.orderPromotionResultService.upsertResult(
        ctx,
        {
          orderId: String(order.id),
          promResult: preResult?.promResult,
        },
        order,
      );
      Logger.debug(
        `order ${order.id} surcharge discountAmount: ${price}, details: ${JSON.stringify(
          surchargeDetails,
        )},gif: ${JSON.stringify(preResult?.promResult?.gifts)}`,
      );
      return preResult?.promResult;
    }
    let proms: PromInstance[] = [];
    let membershipPlanId = '';
    if (!preResult?.promResult?.disableMember) {
      const member = await this.getMemberPromotion(ctx, order.customer.id);
      if (member) {
        proms.push(member);
        membershipPlanId = member.rules.membershipPlanId;
      }
      const memberPriceProm = await this.getMemberPrice(ctx);
      if (memberPriceProm) {
        proms.push(memberPriceProm);
      }
    }
    const automaticProm = await this.getAutomaticPromotion(ctx, membershipPlanId);
    if (automaticProm) {
      proms = proms.concat(automaticProm);
    }

    const couponProms = this.getCouponPromotions(ctx, order.customer.id, membershipPlanId);
    if (couponProms) {
      proms = proms.concat(couponProms);
    }

    const blindBoxProm = this.getBlindBoxPromotion(ctx, order.customer.id, membershipPlanId);
    if (blindBoxProm) {
      proms.push(blindBoxProm);
    }

    const pointsExchangeProm = await this.getPointsExchangePromotion(ctx, order, membershipPlanId);
    if (pointsExchangeProm) {
      proms.push(pointsExchangeProm);
    }

    // 获取购物金发放活动
    const shoppingCreditsProm = await this.getShoppingCreditsPromotion(ctx, order, membershipPlanId);
    if (shoppingCreditsProm?.length > 0) {
      proms = proms.concat(shoppingCreditsProm);
    }

    if (!preResult?.promResult?.disableShoppingCredits) {
      // 获取购物金兑换活动
      const shoppingCreditsDeductionProm = await this.geShoppingCreditsDeductionPromotion(ctx, order, membershipPlanId);
      if (shoppingCreditsDeductionProm?.length > 0) {
        proms = proms.concat(shoppingCreditsDeductionProm);
      }
    }

    proms = this.promSort(proms);

    proms = this.promAddMemberPriceSuperposition(proms);
    const result = (await this.promEngine.execute(
      promOrder,
      proms,
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      preResult?.promResult as any,
      (order.customFields as OrderCustomFields).purchaseType === OrderPurchaseType.ShoppingTrolley,
    )) as unknown as PromResult;
    const orderPromResultInput: OrderPromotionResultInput = {orderId: String(order.id), promResult: result};
    await this.orderPromotionResultService.upsertResult(ctx, orderPromResultInput, order);
    Logger.debug(
      `--------------order ${order.id} automatic promotion discountAmount: ${
        result.discountAmount
      },gift: ${JSON.stringify(result.gifts)}`,
    );
    await this.orderPromotionResultService.checkPurchasePremium(ctx, order.id, order);
    return result;
  }
  async geShoppingCreditsDeductionPromotion(ctx: RequestContext, order: Order, membershipPlanId?: ID) {
    // 获取用户的购物金
    const customerShoppingCredits = await this.virtualCurrencyService.getBalance(
      ctx,
      VirtualCurrencyCode.ShoppingCredits,
      order.customerId,
    );
    if (!customerShoppingCredits || customerShoppingCredits <= 0) {
      return [];
    }
    const shoppingCreditsDeductionProm = await this.connection
      .getRepository(ctx, ShoppingCreditsDeductionActivity)
      .createQueryBuilder('activity')
      .leftJoinAndSelect('activity.channels', 'channel')
      .andWhere('channel.id = :channelId', {channelId: ctx.channelId})
      .andWhere('activity.status = :status', {status: ActivityStatus.Normal})
      .andWhere('activity.startTime <= :now', {now: new Date()})
      .andWhere('activity.endTime >= :now', {now: new Date()})
      .getMany();
    if (!shoppingCreditsDeductionProm) return [];
    const proms = shoppingCreditsDeductionProm.map(prom => {
      return {
        id: Number(prom.promotionId),
        type: PromotionType.ShoppingCreditsDeduction,
        name: prom.displayName,
        // superimposeType: SuperimposeType.all,
        superimposeType: SuperimposeType.limit,
        superimposeTypes: prom.stackingPromotionTypes,
        promProductScope: PromProductScope.all,
        promTime:
          prom.startTime && prom.endTime
            ? `${DateTime.fromJSDate(prom.startTime).toFormat('yyyy-MM-dd')}~${DateTime.fromJSDate(
                prom.endTime,
              ).toFormat('yyyy-MM-dd')}`
            : '长久',
        group: false,
        priority: 9999,
        rules: {
          applicableProduct: prom.applicableProduct,
          minimum: prom.minimum,
          deductionRate: membershipPlanId ? prom.memberDeductionRate ?? prom.deductionRate : prom.deductionRate,
          customerShoppingCredits: customerShoppingCredits,
          whetherRestrictUsers: prom.whetherRestrictUsers,
          memberPlanIds: prom.memberPlanIds,
          groupType: prom.groupType,
          membershipPlanId,
        },
      };
    });
    return proms;
  }

  async getShoppingCreditsPromotion(ctx: RequestContext, order: Order, membershipPlanId?: ID) {
    const shoppingCreditsProm = await this.connection
      .getRepository(ctx, ShoppingCreditsClaimActivity)
      .createQueryBuilder('activity')
      .leftJoinAndSelect('activity.channels', 'channel')
      .andWhere('channel.id = :channelId', {channelId: ctx.channelId})
      .andWhere('activity.status = :status', {status: ActivityStatus.Normal})
      .andWhere('activity.startTime <= :now', {now: new Date()})
      .andWhere('activity.endTime >= :now', {now: new Date()})
      .getMany();
    if (!shoppingCreditsProm) return [];
    const proms = shoppingCreditsProm.map(prom => {
      return {
        id: Number(prom.promotionId),
        type: PromotionType.ShoppingCreditsClaim,
        name: prom.displayName,
        superimposeType: SuperimposeType.limit,
        // superimposeType: SuperimposeType.all,
        superimposeTypes: prom.stackingPromotionTypes,
        promProductScope: PromProductScope.all,
        promTime:
          prom.startTime && prom.endTime
            ? `${DateTime.fromJSDate(prom.startTime).toFormat('yyyy-MM-dd')}~${DateTime.fromJSDate(
                prom.endTime,
              ).toFormat('yyyy-MM-dd')}`
            : '长久',
        group: false,
        priority: 9999,
        rules: {
          ruleType: prom.ruleType,
          ruleValues: prom.ruleValues.map(r => {
            if (r.discountValue)
              return {
                ...r,
                discountValue: {
                  ...r.discountValue,
                  discount: membershipPlanId
                    ? r.discountValue?.memberDiscount ?? r.discountValue?.discount
                    : r.discountValue?.discount,
                },
              };
          }),
          type: prom.type,
          applicableProduct: prom.applicableProduct,
          whetherRestrictUsers: prom.whetherRestrictUsers,
          memberPlanIds: prom.memberPlanIds,
          groupType: prom.groupType,
          membershipPlanId: membershipPlanId,
        },
      };
    });
    return proms;
  }

  async getPointsExchangePromotion(ctx: RequestContext, order: Order, membershipPlanId: ID) {
    if ((order.customFields as OrderCustomFields).buyType !== OrderBuyType.PointsExchange) {
      return;
    }
    if (order.lines.length !== 1) return;
    const orderLine = order.lines[0];
    const productId = orderLine.productVariant.productId;
    const productSkuId = orderLine.productVariant.id;
    const pointsProduct = await this.pointsProductService.getPointsExchangeInfo(ctx, productId);
    const pointsConfig = await this.pointsConfigService.findOne(ctx);
    if (!pointsProduct) return;
    const pointsProductSku = pointsProduct.pointsProductSkus.find(sku =>
      idsAreEqual(sku.productVariantId, productSkuId),
    );
    if (!pointsProductSku) return;
    return {
      id: Number(-1),
      type: PromotionType.PointsExchange,
      name: '积分兑换',
      superimposeType: SuperimposeType.limit,
      superimposeTypes: [],
      promProductScope: PromProductScope.all,
      promTime: '长久',
      group: false,
      priority: 9999,
      rules: {
        points: pointsProductSku.points,
        cash: pointsProductSku.cash,
        exchangeConditionType: pointsProduct.exchangeConditionType,
        pointsConfig: pointsConfig,
      },
    };
  }

  async getMemberPrice(ctx: RequestContext) {
    const userMember = await this.memberService.getUserMember(ctx, true);
    if (!userMember) return;
    const membershipPlan = userMember.membershipPlan;
    const memberPriceId = membershipPlan?.memberPriceId;
    if (!memberPriceId) {
      return;
    }
    let promotionType = Object.values(PromotionType);
    // 过滤会员 - 会员价不和会员优惠同时使用
    promotionType = promotionType.filter(type => type !== PromotionType.Member && type !== PromotionType.MemberPrice);
    return {
      id: Number(-1),
      type: PromotionType.MemberPrice,
      name: '会员价',
      superimposeType: SuperimposeType.limit,
      superimposeTypes: promotionType,
      promProductScope: PromProductScope.all,
      promTime: '长久',
      group: false,
      priority: 9999,
      rules: {
        membershipPlanId: membershipPlan.id,
        memberPriceId: memberPriceId,
      },
    };
  }
  //添加根据叠加包含会员折扣添加会员价
  promAddMemberPriceSuperposition(proms: PromInstance[]): PromInstance[] {
    proms.forEach(prom => {
      // 如果叠加类型包含会员优惠，则添加会员价
      if (prom.superimposeType === SuperimposeType.limit) {
        if (prom.superimposeTypes?.includes(PromotionType.Member)) {
          prom.superimposeTypes = prom.superimposeTypes.concat([PromotionType.MemberPrice]);
        }
      }
    });
    return proms;
  }

  //重新分配优先级
  promSort(proms: PromInstance[]) {
    const priority = [
      PromotionType.BlindBox,
      PromotionType.PointsExchange,
      PromotionType.AutomaticPromotion,
      // 2024-04-08 需求变更 会员优惠计算优先级需高于打包一口价
      PromotionType.PackageDiscount,
      // 新增任选满赠
      PromotionType.DiscountActivity,
      PromotionType.FullDiscountPresent,
      PromotionType.PurchaseAtAPremium,
      // 2024-06-20 需求变更 会员优惠计算优先级需在优惠活动之后
      PromotionType.MemberPrice,
      PromotionType.Member,
      PromotionType.ShoppingCreditsDeduction,
      PromotionType.ShoppingCreditsClaim,
      PromotionType.Coupon,
      PromotionType.SelectiveGift,
      PromotionType.ActuallyPaid,
    ];
    //遍历proms  根据type在priority的下标先后修改priority的值
    proms.forEach(prom => {
      const index = priority.indexOf(prom.type as PromotionType);
      if (index !== -1) {
        prom.priority = index;
      }
    });
    //根据priority排序
    proms.sort((a, b) => a.priority - b.priority);
    return proms;
  }

  //获取优惠券促销
  getCouponPromotions(ctx: RequestContext, customerId: ID, membershipPlanId?: ID) {
    return {
      id: Number(-1),
      type: PromotionType.Coupon,
      name: '优惠券',
      superimposeType: SuperimposeType.all,
      superimposeTypes: [],
      promProductScope: PromProductScope.all,
      promTime: '长久',
      group: false,
      priority: 9999,
      rules: {
        customerId,
        ctx: ctx,
        membershipPlanId,
      },
    };
  }

  getBlindBoxPromotion(ctx: RequestContext, customerId: ID, membershipPlanId?: ID) {
    return {
      id: Number(-1),
      type: PromotionType.BlindBox,
      name: '盲盒',
      superimposeType: SuperimposeType.limit,
      superimposeTypes: [],
      promProductScope: PromProductScope.all,
      promTime: '长久',
      group: false,
      priority: 9999,
      rules: {
        customerId,
        ctx: ctx,
        membershipPlanId,
      },
    };
  }

  //获取自动执行的促销
  async getAutomaticPromotion(ctx: RequestContext, membershipPlanId?: ID) {
    const qb = this.connection
      .getRepository(ctx, Promotion)
      .createQueryBuilder('promotion')
      .leftJoin('promotion.channels', 'channel')
      .andWhere('channel.id = :channelId', {channelId: ctx.channelId})
      .andWhere('promotion.enabled = :enabled', {enabled: true})
      .andWhere('promotion.startsAt <= :now', {now: new Date()})
      .andWhere('promotion.endsAt >= :now', {now: new Date()})
      .andWhere('promotion.customFieldsIsAutomatic = :isAutomatic', {isAutomatic: true})
      .andWhere('promotion.customFieldsType != :type', {type: PromotionType.AutomaticPromotion});
    const promotions = await qb.getMany();
    if (!promotions) return [];
    promotions.filter(promotion => {
      const prom = promotion.customFields as PromotionCustomFields;
      return prom.type === PromotionType.AutomaticPromotion || prom.type === PromotionType.Member;
    });
    const automaticProm = promotions.map(promotion => {
      const promType = (promotion.customFields as PromotionCustomFields).type;
      // const priority = this.getActionPriority(promotion);
      const prom = {
        id: Number(promotion.id),
        type: promType || PromotionType.AutomaticPromotion,
        name: promotion.name,
        superimposeType: promType === PromotionType.Coupon ? SuperimposeType.all : SuperimposeType.limit,
        superimposeTypes: (promotion.customFields as PromotionCustomFields).stackingDiscountSwitch
          ? (promotion.customFields as PromotionCustomFields).stackingPromotionTypes || []
          : [],
        promProductScope: PromProductScope.all,
        group: promType === PromotionType.Coupon ? false : true,
        priority: promotion.priorityScore,
        promTime:
          promotion.startsAt && promotion.endsAt
            ? `${DateTime.fromJSDate(promotion.startsAt).toFormat('yyyy-MM-dd')}~${DateTime.fromJSDate(
                promotion.endsAt,
              ).toFormat('yyyy-MM-dd')}`
            : '长久',
        rules: {
          conditions: promotion.conditions,
          actions: promotion.actions,
          ctx: ctx,
          membershipPlanId,
        },
      };
      return prom;
    });
    return automaticProm || [];
  }
  //直接取promotion的priorityScore 作为优先级 不使用下面的方法
  // getActionPriority(promotion: Promotion) {
  //   switch ((promotion.customFields as PromotionCustomFields).type) {
  //     case PromotionType.Member:
  //       return 1;
  //     case PromotionType.DiscountActivity:
  //     case PromotionType.FullDiscountPresent:
  //       return 2;
  //     case PromotionType.PurchaseAtAPremium:
  //       return 3;
  //     default:
  //       return 4;
  //   }
  // }
  async getMemberPromotion(ctx: RequestContext, customerId: ID): Promise<PromInstance | null> {
    const memberId = await this.orderPromotionResultService.getMemberId(ctx, customerId);
    if (!memberId) {
      return null;
    }
    const usedCount = await this.orderPromotionResultService.getMemberPurchaseCount(ctx, customerId, memberId);
    const member = await this.memberService.getUserMember(ctx, true, customerId);
    if (!member) return null;
    if (member?.membershipPlan?.state === MembershipPlanState.Disable) return null;
    // const restrictedUse = member.membershipPlan.rightsDiscount.restrictedUse || 0;
    const restrictedUse = await this.commonService.getMemberPromotionCount(ctx, member);
    // const usedCount = await this.orderPromotionResultService.getUsedMemberPromotionCount(ctx, customerId, member.id);
    const left = restrictedUse > usedCount ? restrictedUse - usedCount : 0;
    const memberProm = {
      id: Number(member.membershipPlan.promotion.id),
      type: PromotionType.Member,
      name: member.membershipPlan.name,
      superimposeType: SuperimposeType.all,
      promProductScope: PromProductScope.all,
      group: false,
      priority: 1,
      promTime: '长久',
      rules: {
        discountLv: member.membershipPlan.rightsDiscount.discountRate,
        max: left,
        membershipPlanId: member.membershipPlanId,
      },
    };
    return memberProm;
  }
}
