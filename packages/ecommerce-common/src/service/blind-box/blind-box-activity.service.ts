import {forwardRef, Inject, Injectable} from '@nestjs/common';
import {RedLockService} from '@scmally/red-lock';
import {
  ChannelService,
  EntityNotFoundError,
  ID,
  idsAreEqual,
  ListQueryBuilder,
  ListQueryOptions,
  Logger,
  RelationPaths,
  RequestContext,
  TransactionalConnection,
  UnauthorizedError,
} from '@vendure/core';
import {In} from 'typeorm';
import {
  BlindBox,
  BlindBoxActivity,
  BlindBoxActivityBooking,
  BlindBoxActivityBoxLink,
  TemplateConfig,
} from '../../entities';
import {
  BlindBoxActivityInput,
  BlindBoxActivityStatus,
  DeletionResult,
  ProductProbability,
  ProgramLinkInput,
} from '../../generated-admin-types';
import {JumpType, TemplateType} from '../../generated-shop-types';
import {CommonService} from '../common.service';
import {InterfaceCommonCustomer} from '../interface-customer';
import {ProductCustomService} from '../product-custom.service';
import {SendMessageService} from '../send-message.service';
import {BlindBoxOrderService} from './blind-box-order.service';
import {BlindBoxService} from './blind-box.service';
@Injectable()
export class BlindBoxActivityService {
  async failureBlindBoxActivity(ctx: RequestContext, blindBoxActivityId: ID) {
    const blindBoxActivity = await this.findOne(ctx, blindBoxActivityId);
    if (!blindBoxActivity) {
      throw new EntityNotFoundError('BlindBoxActivity', blindBoxActivityId);
    }
    blindBoxActivity.statue = BlindBoxActivityStatus.Failure;
    await this.connection.getRepository(ctx, BlindBoxActivity).update(blindBoxActivityId, {
      statue: BlindBoxActivityStatus.Failure,
    });
    return blindBoxActivity;
  }
  async getBlindBoxActivityQRCode(ctx: RequestContext, input: ProgramLinkInput) {
    const blindBoxActivityId = input.id;
    const blindBoxActivity = await this.findOne(ctx, blindBoxActivityId);
    if (!blindBoxActivity) {
      throw new EntityNotFoundError('BlindBoxActivity', blindBoxActivityId);
    }
    if (blindBoxActivity.smallProgramQRCodeLink) {
      return blindBoxActivity.smallProgramQRCodeLink;
    }
    const qrCodeLink = await this.commonService.generateSmallProgramQRCodeLink(ctx, input, input.path ?? '');
    await this.connection.getRepository(ctx, BlindBoxActivity).update(blindBoxActivityId, {
      smallProgramQRCodeLink: qrCodeLink,
    });
    return qrCodeLink;
  }
  constructor(
    private connection: TransactionalConnection,
    private listQueryBuilder: ListQueryBuilder,
    private channelService: ChannelService,
    private productCustomService: ProductCustomService,
    private redLockService: RedLockService,
    private commonService: CommonService,
    private sendMessageService: SendMessageService,
    @Inject(forwardRef(() => BlindBoxOrderService))
    private blindBoxOrderService: BlindBoxOrderService,
    private blindBoxService: BlindBoxService,
  ) {}
  public interfaceCustomer: InterfaceCommonCustomer;

  registerCustomer(interfaceCustomer: InterfaceCommonCustomer) {
    this.interfaceCustomer = interfaceCustomer;
  }
  async blindBoxActivityPreviewProduct(ctx: RequestContext, blindBoxId: ID, blindBoxActivityId: ID) {
    const qb = this.connection
      .getRepository(ctx, BlindBoxActivityBoxLink)
      .createQueryBuilder('blindBoxActivityBoxLink')
      .leftJoin('blindBoxActivityBoxLink.channels', 'channels')
      .leftJoinAndSelect('blindBoxActivityBoxLink.blindBox', 'blindBox')
      .leftJoinAndSelect('blindBoxActivityBoxLink.blindBoxActivity', 'blindBoxActivity')
      .andWhere('channels.id = :channelId', {channelId: ctx.channelId})
      .andWhere('blindBoxActivityBoxLink.blindBoxId = :blindBoxId', {blindBoxId});
    if (blindBoxActivityId) {
      qb.andWhere('blindBoxActivityBoxLink.blindBoxActivityId = :blindBoxActivityId', {blindBoxActivityId});
    }
    const blindBoxActivityBoxLink = await qb.take(1).getOne();
    const wishBlindBoxId = blindBoxActivityBoxLink?.blindBoxId;
    const baseBlindBoxId = blindBoxActivityBoxLink?.blindBoxActivity?.baseBlindBoxId;
    if (!wishBlindBoxId || !baseBlindBoxId) {
      throw new Error('盲盒不存在');
    }
    const blindBox = await this.connection
      .getRepository(ctx, BlindBox)
      .createQueryBuilder('blindBox')
      .andWhere(`blindBox.deletedAt IS NULL`)
      .leftJoin('blindBox.channels', 'channels')
      .leftJoinAndSelect('blindBox.blindBoxItems', 'blindBoxItems')
      .andWhere('blindBoxItems.deletedAt IS NULL')
      .leftJoinAndSelect('blindBoxItems.productVariant', 'productVariant')
      .andWhere(`blindBox.id in (:...ids)`, {ids: [wishBlindBoxId, baseBlindBoxId]})
      .getMany();
    const wishBlindBox = blindBox.find(box => idsAreEqual(box.id, wishBlindBoxId));
    if (wishBlindBox) {
      let wishBlindBoxItems = wishBlindBox.blindBoxItems;
      wishBlindBoxItems = await this.blindBoxService.filterAbnormalBlindBoxItems(ctx, wishBlindBoxItems);
      // 心愿盲盒中的商品需要根据是否是心愿商品进行排序 心愿商品在前
      wishBlindBox.blindBoxItems = wishBlindBoxItems?.sort((a, b) => {
        if (a.isWishItem && !b.isWishItem) {
          return -1;
        }
        if (!a.isWishItem && b.isWishItem) {
          return 1;
        }
        return 0;
      });
    }
    const baseBlindBox = blindBox.find(box => idsAreEqual(box.id, baseBlindBoxId));
    // 如果基础盲盒存在商品在心愿盲盒中则过滤基础盲盒中的商品
    if (baseBlindBox) {
      let baseBlindBoxItems = baseBlindBox.blindBoxItems;
      baseBlindBoxItems = await this.blindBoxService.filterAbnormalBlindBoxItems(ctx, baseBlindBoxItems);
      baseBlindBox.blindBoxItems = baseBlindBoxItems?.filter(
        item => !wishBlindBox?.blindBoxItems.some(wishItem => idsAreEqual(wishItem.targetId, item.targetId)),
      );
    }
    return {
      wishBlindBox,
      baseBlindBox,
    };
  }
  // 是否包含删除的活动
  async getActiveBlindBoxActivity(ctx: RequestContext, blindBoxActivityId: ID, includeDeleted = true) {
    // const now = new Date();
    const qb = this.connection.getRepository(ctx, BlindBoxActivity).createQueryBuilder('blindBoxActivity');
    qb.leftJoinAndSelect(`${qb.alias}.baseBlindBox`, 'baseBlindBox');
    qb.leftJoinAndSelect(`baseBlindBox.blindBoxItems`, 'blindBoxItems');
    qb.andWhere(`blindBoxItems.deletedAt IS NULL`);
    qb.leftJoinAndSelect(`blindBoxItems.productVariant`, 'baseProductVariant');
    qb.leftJoinAndSelect(`${qb.alias}.blindBoxActivityBoxLinks`, 'blindBoxActivityBoxLinks');
    qb.leftJoinAndSelect('blindBoxActivityBoxLinks.blindBox', 'blindBox');
    qb.leftJoinAndSelect('blindBox.blindBoxItems', 'linkBlindBoxItems');
    qb.andWhere(`linkBlindBoxItems.deletedAt IS NULL`);
    qb.leftJoinAndSelect('linkBlindBoxItems.productVariant', 'linkProductVariant');
    qb.leftJoinAndSelect(`blindBox.wishBlindBoxItem`, 'wishBlindBoxItem');
    qb.leftJoinAndSelect(`wishBlindBoxItem.productVariant`, 'wishProductVariant');
    qb.leftJoinAndSelect(`${qb.alias}.channels`, 'channel');
    qb.andWhere(`channel.id = :channelId`, {channelId: ctx.channelId});
    // qb.andWhere(`${qb.alias}.enabled = true`);
    // qb.andWhere(`${qb.alias}.startAt <= :now`, {now});
    // qb.andWhere(`${qb.alias}.endAt >= :now`, {now});
    // qb.andWhere(`${qb.alias}.deletedAt IS NULL`);
    qb.andWhere(`${qb.alias}.id = :blindBoxActivityId`, {blindBoxActivityId});
    qb.andWhere(`blindBox.deletedAt IS NULL`);
    if (!includeDeleted) {
      qb.andWhere(`${qb.alias}.deletedAt IS NULL`);
    }
    const item = await qb.take(1).getOne();
    return item;
  }

  async findOne(
    ctx: RequestContext,
    blindBoxId: ID,
    options?: ListQueryOptions<BlindBoxActivity>,
    relations?: RelationPaths<BlindBoxActivity>,
    isIncludedDeleted = true,
  ): Promise<BlindBoxActivity | null> {
    const qb = this.listQueryBuilder.build(BlindBoxActivity, options, {
      ctx,
      relations,
      channelId: ctx.channelId,
    });
    qb.andWhere(`${qb.alias}.id = :blindBoxId`, {blindBoxId});
    qb.leftJoinAndSelect(`${qb.alias}.baseBlindBox`, 'baseBlindBox');
    qb.leftJoinAndSelect(`${qb.alias}.blindBoxActivityBoxLinks`, 'blindBoxActivityBoxLinks');
    qb.leftJoinAndSelect(`blindBoxActivityBoxLinks.blindBox`, 'blindBox');
    if (!isIncludedDeleted) {
      qb.andWhere(`${qb.alias}.deletedAt IS NULL`);
    }
    const item = await qb.take(1).getOne();
    return item;
  }
  async findAll(
    ctx: RequestContext,
    options: ListQueryOptions<BlindBoxActivity>,
    relations: RelationPaths<BlindBoxActivity>,
  ) {
    const qb = this.listQueryBuilder.build(BlindBoxActivity, options, {
      ctx,
      relations,
      channelId: ctx.channelId,
    });
    qb.leftJoinAndSelect(`${qb.alias}.baseBlindBox`, 'baseBlindBox');
    qb.leftJoinAndSelect(`${qb.alias}.blindBoxActivityBoxLinks`, 'blindBoxActivityBoxLinks');
    qb.andWhere(`${qb.alias}.deletedAt IS NULL`);
    const [items, totalItems] = await qb.getManyAndCount();
    return {
      items,
      totalItems,
    };
  }

  async deleteBlindBoxActivity(ctx: RequestContext, blindBoxActivityId: string) {
    const blindBoxActivity = await this.findOne(ctx, blindBoxActivityId);
    if (!blindBoxActivity) {
      throw new Error('删除的盲盒活动不存在');
    }
    if (blindBoxActivity.statue === BlindBoxActivityStatus.Normal) {
      throw new Error('活动正在进行中，不能删除');
    }
    blindBoxActivity.deletedAt = new Date();
    await this.connection.getRepository(ctx, BlindBoxActivity).save(blindBoxActivity);
    return {
      result: DeletionResult.Deleted,
      message: '删除成功',
    };
  }
  async upsertBlindBoxActivity(ctx: RequestContext, input: BlindBoxActivityInput) {
    let statue = BlindBoxActivityStatus.Normal;
    await this.validateBlindBoxActivity(ctx, input);
    if (input.startAt < new Date() && input.endAt > new Date()) {
      statue = BlindBoxActivityStatus.Normal;
    } else if (input.startAt > new Date()) {
      statue = BlindBoxActivityStatus.NotStarted;
    } else if (input.endAt < new Date()) {
      statue = BlindBoxActivityStatus.HaveEnded;
    }
    if (input.id) {
      const blindBoxActivityId = input.id as ID;
      const blindBoxActivity = await this.findOne(ctx, blindBoxActivityId);
      if (!blindBoxActivity) {
        throw new Error('修改的盲盒活动不存在');
      }
      // 不存在于input的盲盒删除
      const deleteBlindBoxActivityBoxLinks = blindBoxActivity.blindBoxActivityBoxLinks.filter(
        link =>
          !input.blindBoxActivityBoxLinks?.some(inputLink =>
            idsAreEqual(inputLink?.blindBoxId ?? undefined, link.blindBoxId),
          ),
      );
      if (deleteBlindBoxActivityBoxLinks?.length > 0) {
        await this.connection.getRepository(ctx, BlindBoxActivityBoxLink).remove(deleteBlindBoxActivityBoxLinks);
      }

      // 存在于input的盲盒更新
      const updateBlindBoxActivityBoxLinks = blindBoxActivity.blindBoxActivityBoxLinks.filter(link =>
        input.blindBoxActivityBoxLinks?.some(inputLink =>
          idsAreEqual(inputLink?.blindBoxId ?? undefined, link.blindBoxId),
        ),
      );
      for (const link of updateBlindBoxActivityBoxLinks) {
        const inputLink = input.blindBoxActivityBoxLinks?.find(item =>
          idsAreEqual(item?.blindBoxId ?? undefined, link.blindBoxId),
        );
        if (!inputLink) {
          continue;
        }
        link.maxAssistLimit = inputLink.maxAssistLimit as number;
        link.productProbabilities = inputLink.productProbabilities as ProductProbability[][];
        link.assistLimits = inputLink.assistLimits as number[];
      }
      if (updateBlindBoxActivityBoxLinks.length > 0) {
        await this.connection.getRepository(ctx, BlindBoxActivityBoxLink).save(updateBlindBoxActivityBoxLinks);
      }

      // 不存在于数据库的盲盒新增
      const newBlindBoxActivityBoxLinks = input.blindBoxActivityBoxLinks?.filter(
        link =>
          !blindBoxActivity?.blindBoxActivityBoxLinks.some(item =>
            idsAreEqual(item.blindBoxId, link?.blindBoxId ?? undefined),
          ),
      );
      if (newBlindBoxActivityBoxLinks?.length > 0) {
        const newLinks = [];
        for (const link of newBlindBoxActivityBoxLinks) {
          let newLink = new BlindBoxActivityBoxLink({
            blindBoxActivityId: blindBoxActivityId,
            blindBoxId: link?.blindBoxId,
            maxAssistLimit: link?.maxAssistLimit,
            productProbabilities: link?.productProbabilities as ProductProbability[][],
            assistLimits: link?.assistLimits as number[],
          });
          newLink = await this.channelService.assignToCurrentChannel(newLink, ctx);
          newLinks.push(newLink);
        }
        await this.connection.getRepository(ctx, BlindBoxActivityBoxLink).save(newLinks);
      }
      await this.connection.getRepository(ctx, BlindBoxActivity).update(blindBoxActivityId, {
        name: input.name ?? undefined,
        description: input.description ?? undefined,
        startAt: input.startAt,
        endAt: input.endAt,
        assistLimit: input.assistLimit as number,
        isFreeShipping: input.isFreeShipping ?? undefined,
        freeShippingThreshold: input.freeShippingThreshold ?? undefined,
        price: input.price!,
        baseBlindBoxProbability: input.baseBlindBoxProbability!,
        baseBlindBoxId: input.baseBlindBoxId ?? undefined,
        remarks: input.remarks ?? undefined,
        statue,
        assistPagePoster: input.assistPagePoster ?? undefined,
        assistPosterType: input.assistPosterType ?? undefined,
        openBoxMode: input.openBoxMode ?? undefined,
        stock: input.stock as number,
      });
      return this.findOne(ctx, blindBoxActivityId);
    } else {
      let blindBoxActivity = new BlindBoxActivity({
        name: input.name,
        description: input.description,
        statue: statue,
        startAt: input.startAt,
        endAt: input.endAt,
        enabled: statue === BlindBoxActivityStatus.Normal,
        isFreeShipping: input.isFreeShipping,
        freeShippingThreshold: input.freeShippingThreshold,
        price: input.price,
        baseBlindBoxProbability: input.baseBlindBoxProbability,
        baseBlindBoxId: input.baseBlindBoxId,
        assistLimit: input.assistLimit,
        remarks: input.remarks ?? '',
        assistPagePoster: input.assistPagePoster,
        assistPosterType: input.assistPosterType,
        openBoxMode: input.openBoxMode,
        stock: input.stock ?? 0,
      });
      blindBoxActivity = await this.channelService.assignToCurrentChannel(blindBoxActivity, ctx);
      blindBoxActivity = await this.connection.getRepository(ctx, BlindBoxActivity).save(blindBoxActivity);
      const newLinks = [];
      for (const link of input.blindBoxActivityBoxLinks) {
        let newLink = new BlindBoxActivityBoxLink({
          blindBoxActivityId: blindBoxActivity.id,
          blindBoxId: link.blindBoxId,
          maxAssistLimit: link.maxAssistLimit,
          productProbabilities: link.productProbabilities as ProductProbability[][],
          assistLimits: link.assistLimits as number[],
        });
        newLink = await this.channelService.assignToCurrentChannel(newLink, ctx);
        newLinks.push(newLink);
      }
      await this.connection.getRepository(ctx, BlindBoxActivityBoxLink).save(newLinks);
      return this.findOne(ctx, blindBoxActivity.id);
    }
  }
  async validateBlindBoxActivity(ctx: RequestContext, input: BlindBoxActivityInput) {
    if (input.id) {
      const blindBoxActivity = await this.findOne(ctx, input.id);
      if (!blindBoxActivity) {
        throw new Error('修改的盲盒活动不存在');
      }
    }
    if (!input.name) {
      throw new Error('盲盒活动名称不能为空');
    }
    if (!input.startAt) {
      throw new Error('盲盒活动开始时间不能为空');
    }
    if (!input.endAt) {
      throw new Error('盲盒活动结束时间不能为空');
    }
    if (input.startAt >= input.endAt) {
      throw new Error('盲盒活动开始时间不能大于结束时间');
    }
    if ((input.stock ?? 0) < 0) {
      throw new Error('盲盒库存不能小于0');
    }

    // if (!input.isFreeShipping) {
    //   // 包邮门槛存在并且大于0
    //   if (!input.freeShippingThreshold || input.freeShippingThreshold < 0) {
    //     throw new Error('包邮门槛必须大于0');
    //   }
    // }

    // 盲盒价格
    if ((input.price ?? 0) <= 0) {
      throw new Error('盲盒价格必须大于0');
    }

    if (!input.baseBlindBoxId) {
      throw new Error('前置盲盒不能为空');
    }

    // 前置盲盒概率必须存在并且大于0并且小于等于100
    if (!input.baseBlindBoxProbability || input.baseBlindBoxProbability <= 0 || input.baseBlindBoxProbability > 100) {
      throw new Error('前置盲盒概率必须大于0并且小于等于100');
    }

    if (!input.blindBoxActivityBoxLinks) {
      throw new Error('后置盲盒不能为空');
    }

    if (input.blindBoxActivityBoxLinks.length === 0) {
      throw new Error('后置盲盒不能为空');
    }

    const blindBoxActivityBoxLinks = input.blindBoxActivityBoxLinks;
    const blindBoxIds = blindBoxActivityBoxLinks.map(link => link.blindBoxId);
    // 后置盲盒不能重复
    if (blindBoxIds.length !== new Set(blindBoxIds).size) {
      throw new Error('后置盲盒不能重复');
    }
    blindBoxActivityBoxLinks.forEach((link, linkIndex) => {
      link.productProbabilities?.forEach((productProbability, probIndex) => {
        productProbability?.forEach((item, itemIndex) => {
          if (
            item?.targetProbability === 0 ||
            (item?.targetProbability && (item?.targetProbability <= 0 || item?.targetProbability > 100))
          ) {
            throw new Error(
              `商品概率无效:Link索引 ${linkIndex}, Prob索引 ${probIndex}, Item索引 ${itemIndex},概率应大于0且小于等于100`,
            );
          }
        });

        // 校验总概率
        // TODO 需确认cur?.targetProbability ?? 0增加后是否正确
        const totalProbability = productProbability?.reduce((acc, cur) => acc + (cur?.targetProbability ?? 0), 0);
        if ((totalProbability ?? 0) > 100) {
          throw new Error(
            `商品概率总和无效:Link索引 ${linkIndex}, Prob索引 ${probIndex},总和应小于等于100,当前总和为 ${totalProbability}`,
          );
        }
      });
      const assistLimits = link.assistLimits;
      if (assistLimits?.length !== link.productProbabilities?.length) {
        throw new Error('助力次数数组长度和开盒概率数组长度不一致');
      }
      // 助力次数数组必须大于0
      if (assistLimits?.some(limit => !limit || limit <= 0)) {
        throw new Error('助力次数必须存在并且大于0');
      }
    });

    // productProbabilities 所有概率必须大于0 并且小于等于100
    // const invalidProductProbabilities = blindBoxActivityBoxLinks.some(link =>
    //   link.productProbabilities.some(
    //     productProbability => productProbability.targetProbability <= 0 || productProbability.targetProbability > 100,
    //   ),
    // );
    // if (invalidProductProbabilities) {
    //   throw new Error('商品概率必须大于0并且小于等于100');
    // }
    // // 总概率不能大于100
    // for (const link of blindBoxActivityBoxLinks) {
    //   const totalProbability = link.productProbabilities.reduce((acc, cur) => acc + cur.targetProbability, 0);
    //   if (totalProbability > 100) {
    //     throw new Error('商品概率总和不能大于100');
    //   }
    // }
  }

  async blindBoxActivityStateChangeAll() {
    const ctxs = await this.productCustomService.getAllCtxs();
    for (const ctx of ctxs) {
      await this.blindBoxActivityStateChange(ctx);
    }
  }
  async blindBoxActivityStateChange(ctx: RequestContext) {
    const now = new Date();
    const notStarted = await this.connection
      .getRepository(ctx, BlindBoxActivity)
      .createQueryBuilder('blindBoxActivity')
      .leftJoinAndSelect('blindBoxActivity.channels', 'channel')
      .andWhere(`channel.id = :channelId`, {channelId: ctx.channelId})
      .andWhere(`blindBoxActivity.startAt <= :now`, {now})
      .andWhere(`blindBoxActivity.statue = :statue`, {statue: BlindBoxActivityStatus.NotStarted})
      .andWhere(`blindBoxActivity.deletedAt IS NULL`)
      .getMany();
    if (notStarted.length > 0) {
      const notStartedIds = notStarted.map(item => item.id as ID);
      await this.connection
        .getRepository(ctx, BlindBoxActivity)
        .update({id: In(notStartedIds)}, {statue: BlindBoxActivityStatus.Normal});
    }

    const haveEnded = await this.connection
      .getRepository(ctx, BlindBoxActivity)
      .createQueryBuilder('blindBoxActivity')
      .leftJoinAndSelect('blindBoxActivity.channels', 'channel')
      .andWhere(`channel.id = :channelId`, {channelId: ctx.channelId})
      .andWhere(`blindBoxActivity.endAt <= :now`, {now})
      .andWhere(`blindBoxActivity.statue = :statue`, {statue: BlindBoxActivityStatus.Normal})
      .andWhere(`blindBoxActivity.deletedAt IS NULL`)
      .getMany();

    if (haveEnded.length > 0) {
      const haveEndedIds = haveEnded.map(item => item.id as ID);
      await this.connection
        .getRepository(ctx, BlindBoxActivity)
        .update({id: In(haveEndedIds)}, {statue: BlindBoxActivityStatus.HaveEnded});
    }
  }

  async blindBoxActivityIsBooking(ctx: RequestContext, blindBoxActivityId: ID, customerId?: ID) {
    if (!customerId) {
      const customer = await this.interfaceCustomer.getCustomer(ctx);
      if (!customer) {
        throw new UnauthorizedError();
      }
      customerId = customer.id;
    }
    const booking = await this.connection
      .getRepository(ctx, BlindBoxActivityBooking)
      .createQueryBuilder('blindBoxActivityBooking')
      .leftJoin('blindBoxActivityBooking.channels', 'channel')
      .andWhere('channel.id = :channelId', {channelId: ctx.channelId})
      .andWhere('blindBoxActivityBooking.customerId = :customerId', {customerId: customerId})
      .andWhere('blindBoxActivityBooking.blindBoxActivityId = :blindBoxActivityId', {blindBoxActivityId})
      .take(1)
      .getOne();
    if (booking) {
      return true;
    }
    return false;
  }

  async blindBoxActivityBooking(ctx: RequestContext, blindBoxActivityId: ID) {
    const customer = await this.interfaceCustomer.getCustomer(ctx);
    if (!customer) {
      throw new UnauthorizedError();
    }
    const lock = await this.redLockService.lockResource(`blindBoxActivityBooking:${customer.id}:${blindBoxActivityId}`);
    try {
      const blindBoxActivity = await this.findOne(ctx, blindBoxActivityId);
      if (blindBoxActivity?.statue !== BlindBoxActivityStatus.NotStarted) {
        throw new Error('活动不在预约时间内');
      }
      const isBooking = await this.blindBoxActivityIsBooking(ctx, blindBoxActivityId, customer.id);
      if (isBooking) {
        throw new Error('您已经预约过了');
      }
      let booking = new BlindBoxActivityBooking({
        blindBoxActivityId,
        customerId: customer.id,
        isSend: false,
      });
      booking = await this.channelService.assignToCurrentChannel(booking, ctx);
      await this.connection.getRepository(ctx, BlindBoxActivityBooking).save(booking);
      return true;
    } catch (e) {
      Logger.error(e);
      throw e;
    } finally {
      await this.redLockService.unlockResource(lock);
    }
  }

  async blindBoxActivityRemindAll() {
    const ctxs = await this.productCustomService.getAllCtxs();
    for (const ctx of ctxs) {
      await this.blindBoxActivityRemind(ctx);
    }
  }
  async blindBoxActivityRemind(ctx: RequestContext) {
    const templateConfig = await this.connection.getRepository(ctx, TemplateConfig).findOne({
      where: {
        channelId: ctx.channelId,
        templateType: TemplateType.BlindBoxActivityBooking,
      },
    });
    if (!templateConfig) {
      return;
    }
    // 查询还有十分钟就开始的盲盒活动
    const now = new Date();
    const tenMinutesLater = new Date(now.getTime() + 10 * 60 * 1000);
    const blindBoxActivities = await this.connection
      .getRepository(ctx, BlindBoxActivity)
      .createQueryBuilder('blindBoxActivity')
      .leftJoinAndSelect('blindBoxActivity.channels', 'channel')
      .andWhere(`channel.id = :channelId`, {channelId: ctx.channelId})
      .andWhere(`blindBoxActivity.startAt <= :tenMinutesLater`, {tenMinutesLater})
      .andWhere(`blindBoxActivity.startAt >= :now`, {now})
      .andWhere(`blindBoxActivity.statue = :statue`, {statue: BlindBoxActivityStatus.NotStarted})
      .andWhere(`blindBoxActivity.deletedAt IS NULL`)
      .getMany();
    if (blindBoxActivities.length === 0) {
      return;
    }
    for (const blindBoxActivity of blindBoxActivities) {
      let hasMore = true;
      const pageSize = 100; // 每页记录数
      let currentPage = 0;
      while (hasMore) {
        // 分页查询 bookings
        const bookings = await this.connection
          .getRepository(ctx, BlindBoxActivityBooking)
          .createQueryBuilder('blindBoxActivityBooking')
          .leftJoin('blindBoxActivityBooking.channels', 'channel')
          .leftJoinAndSelect('blindBoxActivityBooking.customer', 'customer')
          .where('channel.id = :channelId', {channelId: ctx.channelId})
          .andWhere('blindBoxActivityBooking.blindBoxActivityId = :blindBoxActivityId', {
            blindBoxActivityId: blindBoxActivity.id,
          })
          .andWhere('blindBoxActivityBooking.isSend = false')
          .skip(currentPage * pageSize)
          .take(pageSize)
          .getMany();
        hasMore = bookings.length === pageSize;
        currentPage++;
        if (bookings.length === 0) continue;
        for (const booking of bookings) {
          try {
            // 发送提醒消息
            await this.sendMessageService.blindBoxActivityRemind(
              ctx,
              booking.customer.id,
              blindBoxActivity.name,
              blindBoxActivity.startAt,
              JumpType.BlindBoxActivity,
              blindBoxActivity.id as string,
              templateConfig,
            );
            // 更新 isSend 状态
            await this.connection.getRepository(ctx, BlindBoxActivityBooking).update(booking.id, {isSend: true});
          } catch (error) {
            // 记录失败的提醒
            Logger.error(
              `Failed to send reminder for bookingId ${booking.id} in activity ${blindBoxActivity.id}`,
              error,
            );
            continue; // 跳过失败记录，继续处理其他记录
          }
        }
      }
    }
  }
}
