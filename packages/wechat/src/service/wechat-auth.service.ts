import {Injectable} from '@nestjs/common';
import {KvsService} from '@scmally/kvs';
import {
  AccountRegistrationEvent,
  AuthenticatedSession,
  ChannelService,
  Customer,
  CustomerEvent,
  CustomerService,
  EventBus,
  ID,
  ListQueryBuilder,
  Logger,
  NATIVE_AUTH_STRATEGY_NAME,
  RequestContext,
  RoleService,
  SessionService,
  Transaction,
  TransactionalConnection,
  User,
  UserInputError,
} from '@vendure/core';
import {randomUUID} from 'crypto';
import {DateTime} from 'luxon';
import request from 'request';
import shortid from 'shortid';
import {IsNull} from 'typeorm';
import {TokenOpenidMapping, WeChatConfig} from '../entities';
import {WeChatProgramUser} from '../entities/wechat-program-user.entity';
import {CustomerLoginEvent} from '../event';
import {GainPhone, LoginReturn} from '../generated-shop-types';
import {ExternalLoginMutationArgs, UserInfoData, WeChatToken} from '../types';
import {WxChatUtil} from '../utils/wx_chat_util';
import {InterfaceCommon} from './abstract-common';
import {InterfaceYouZan} from './abstract-you-zan';
import {WeChatConfigService} from './wechat_config.service';
@Injectable()
export class WeChatAuthService {
  // weChatToken: Map<string, string> = new Map();
  private weChatLoginUrl: string;
  private weChatGetPhoneUrl: string;
  private setWeChatMsgJumpPathUrl: string;
  private interfaceYouZan: InterfaceYouZan;
  public interfaceCommon: InterfaceCommon;
  constructor(
    private connection: TransactionalConnection,
    private channelService: ChannelService,
    private listQueryBuilder: ListQueryBuilder,
    private sessionService: SessionService,
    private roleService: RoleService,
    private weChatConfigService: WeChatConfigService,
    private kvsService: KvsService,
    private eventBus: EventBus,
    private customerService: CustomerService,
  ) {
    this.weChatLoginUrl = `https://api.weixin.qq.com/sns/jscode2session?appid={{weChatAppid}}&secret={{weChatAppSecret}}&js_code={{code}}&grant_type=authorization_code`;
    this.weChatGetPhoneUrl = `https://api.weixin.qq.com/wxa/business/getuserphonenumber?access_token={{access_token}}`;
    this.setWeChatMsgJumpPathUrl = `https://api.weixin.qq.com/wxa/sec/order/set_msg_jump_path?access_token={{access_token}}`;
  }
  registerYouZan(interfaceYouZan: InterfaceYouZan) {
    this.interfaceYouZan = interfaceYouZan;
  }
  registerCommon(interfaceCommon: InterfaceCommon) {
    this.interfaceCommon = interfaceCommon;
  }

  async authenticate(ctx: RequestContext, args: ExternalLoginMutationArgs) {
    const loginReturn: LoginReturn = {};
    const {jsCode, nickname, profilePictureUrl, md5Str, sourceCode, distributionId} = args;
    const profileData: UserInfoData = await this.getWeChatUserInfoByJsCode(ctx, jsCode);
    if (md5Str || sourceCode) {
      await this.interfaceCommon.updateDistributorRecord(ctx, profileData.id, md5Str, sourceCode, distributionId);
    }
    let wechatUser = await this.getWechatUserByIdentifier(ctx, profileData.id);
    const openIdKey = shortid.generate();
    await this.kvsService.openIdStore.set(openIdKey, profileData.id);
    loginReturn.openIdKey = openIdKey;
    if (!wechatUser) {
      wechatUser = await this.createWechatUser(ctx, nickname, profilePictureUrl, profileData.id, profileData.unionId);
    }
    const customer = await this.getCustomerByPhone(ctx, wechatUser.phone);
    if (!customer) {
      return loginReturn;
    }
    const user = await this.getUserByIdentifier(ctx, wechatUser.phone);
    if (!user) {
      // user = await this.createExternalUser(profileData);
      return loginReturn;
    }
    if (ctx.session?.activeOrderId) {
      await this.sessionService.deleteSessionsByActiveOrderId(ctx, ctx.session?.activeOrderId as ID);
    }
    const session = await this.sessionService.createNewAuthenticatedSession(ctx, user, NATIVE_AUTH_STRATEGY_NAME);
    const newSession = await this.connection.getRepository(ctx, AuthenticatedSession).save(session);
    loginReturn.token = newSession.token;
    // 记录token和openId的映射关系
    await this.saveTokenOpenIdMapping(ctx, newSession.token, profileData.id);
    this.eventBus.publish(new CustomerLoginEvent(ctx, customer));
    try {
      await this.interfaceYouZan.syncUser(ctx, customer);
    } catch (error) {
      Logger.error(`同步有赞用户会员信息错误:${error}`);
    }
    return loginReturn;
  }
  async saveTokenOpenIdMapping(ctx: RequestContext, token: string, openId: string) {
    try {
      const tokenOpenIdMapping = new TokenOpenidMapping({
        token,
        openId: openId,
      });
      await this.connection.getRepository(ctx, TokenOpenidMapping).save(tokenOpenIdMapping);
    } catch (error) {
      Logger.error(`saveTokenOpenIdMapping error:${error}`);
    }
  }

  async getUserPhone(ctx: RequestContext, args: GainPhone) {
    const {jsCode, openIdKey} = args;
    if (!jsCode) {
      Logger.error(`jsCode abnormal, jsCode:${jsCode}`);
      throw new UserInputError(`jsCode error:${jsCode}`);
    }
    const openId = await this.kvsService.openIdStore.get(openIdKey);
    if (!openId) {
      Logger.error(`jsCode abnormal, openId:${openId}`);
      throw new UserInputError(`openId error:${openId}`);
    }
    const loginReturn: LoginReturn = {openIdKey: openIdKey};
    let wxUser = await this.getWechatUserByIdentifier(ctx, openId);
    if (!wxUser) {
      Logger.error(`Please authorize the phone number before obtaining it openId:${openId}`);
      throw new Error('Please authorize the phone number before obtaining it');
    }
    const phone = await this.getWechatUserPhone(ctx, jsCode); //'***********';
    if (!phone) {
      Logger.error(`Failed to obtain the mobile phone number  jsCode:${jsCode}`);
      throw new Error('Failed to obtain the mobile phone number');
    }
    wxUser.phone = phone;
    wxUser = await this.connection.getRepository(ctx, WeChatProgramUser).save(wxUser);
    const customer = await this.getCustomerByPhone(ctx, wxUser.phone);
    let user = await this.getUserByIdentifier(ctx, wxUser.phone);
    if (!customer) {
      user = await this.createExternalCustomer(ctx, wxUser);
    } else {
      this.eventBus.publish(new CustomerLoginEvent(ctx, customer));
      try {
        await this.interfaceYouZan.syncUser(ctx, customer);
      } catch (error) {
        Logger.error(`同步有赞用户会员信息错误:${error}`);
      }
    }
    if (!user) {
      Logger.error(`user error`);
      throw new Error('user error');
    }
    if (ctx.session?.activeOrderId) {
      await this.sessionService.deleteSessionsByActiveOrderId(ctx, ctx.session?.activeOrderId as ID);
    }

    const session = await this.sessionService.createNewAuthenticatedSession(ctx, user, NATIVE_AUTH_STRATEGY_NAME);
    const newSession = await this.connection.getRepository(ctx, AuthenticatedSession).save(session);
    loginReturn.token = newSession.token;
    // 记录token和openId的映射关系
    await this.saveTokenOpenIdMapping(ctx, newSession.token, openId);
    return loginReturn;
  }

  /**
   * 根据openId获取微信用户信息
   * @param openId
   * @returns
   */
  async getWechatUserByIdentifier(ctx: RequestContext, openId: string): Promise<WeChatProgramUser | null> {
    const wechatUser = await this.connection.getRepository(ctx, WeChatProgramUser).findOne({
      where: {openId: openId},
    });
    return wechatUser;
  }

  /**
   * 根据手机号获取当前channel的微信用户信息
   * @param phone
   * @returns
   */
  async getUserByPhone(ctx: RequestContext, phone: string): Promise<WeChatProgramUser | undefined> {
    const qb = this.connection.getRepository(ctx, WeChatProgramUser).createQueryBuilder('weChatUser');
    qb.andWhere('phone =:phone', {phone});
    qb.andWhere('channelId =:channelId', {channelId: ctx.channelId});
    //TODO getOnt()不会返回关联关系表
    const items = await qb.getMany();
    if (items.length === 0) {
      return undefined;
    }
    if (items.length === 1) {
      return items[0];
    }
    const token = ctx.session?.token;
    if (token) {
      const tokenOpenIdMapping = await this.connection.getRepository(ctx, TokenOpenidMapping).findOne({
        where: {token},
      });
      const openId = tokenOpenIdMapping?.openId;
      if (openId) {
        const wechatProgramUser = items.find(item => item.openId === openId);
        if (wechatProgramUser) {
          return wechatProgramUser;
        }
      }
    }
    return items[0];
  }

  /**
   * 根据唯一标识符获取用户
   * @param identifier 唯一标识符
   * @returns
   */
  async getUserByIdentifier(ctx: RequestContext, identifier: string): Promise<User | null> {
    return this.connection.getRepository(ctx, User).findOne({
      where: {identifier: identifier, deletedAt: IsNull()},
      relations: ['roles', 'roles.channels'],
    });
  }

  /**
   * 根据openID获取customer用户信息
   * @param ctx
   * @param openId
   * @returns
   */
  async getCustomerByOpenId(ctx: RequestContext, openId: string): Promise<Customer | null> {
    const wechatUser = await this.getWechatUserByIdentifier(ctx, openId);
    if (!wechatUser) {
      return null;
    }
    return this.getCustomerByPhone(ctx, wechatUser.phone);
  }

  private async generateCode(ctx: RequestContext): Promise<string> {
    const code = WxChatUtil.getRandomStr(32);
    const count = await this.connection.getRepository(ctx, WeChatProgramUser).count({where: {code: code}});
    if (count === 0) {
      return code;
    } else {
      return this.generateCode(ctx);
    }
  }

  private async createWechatUser(
    ctx: RequestContext,
    nickname: string,
    profilePictureUrl: string,
    openId: string,
    unionId: string,
  ) {
    const code = await this.generateCode(ctx);
    const channel = await this.channelService.findOne(ctx, ctx.channelId);
    const wechatUser = new WeChatProgramUser({
      nickname,
      profilePictureUrl,
      openId,
      unionId,
      code,
      channel,
    });
    return this.connection.getRepository(ctx, WeChatProgramUser).save(wechatUser);
  }

  private async createExternalCustomer(ctx: RequestContext, weChatUser: WeChatProgramUser): Promise<User> {
    const phone = weChatUser.phone;
    if (!phone) {
      throw Error('The phone cannot be empty');
    }
    let customer = await this.getCustomerByPhone(ctx, weChatUser.phone);
    if (!customer) {
      customer = await this.connection
        .getRepository(ctx, Customer)
        .findOne({where: {phoneNumber: weChatUser.phone, deletedAt: IsNull()}});
      if (customer) {
        customer = await this.channelService.assignToChannels(ctx, Customer, customer.id, [ctx.channelId]);
      } else {
        let user = await this.getUserByIdentifier(ctx, weChatUser.phone);
        if (!user) {
          user = new User({
            identifier: weChatUser.phone,
            verified: true,
            roles: [await this.roleService.getCustomerRole()],
          });
          await this.connection.getRepository(ctx, User).save(user);
          this.eventBus.publish(new AccountRegistrationEvent(ctx, user));
        }
        customer = new Customer({
          emailAddress: randomUUID() + '@chocoly.io',
          firstName: weChatUser.nickname,
          phoneNumber: weChatUser.phone,
          lastName: weChatUser.nickname,
          user: user,
        });
        customer = await this.channelService.assignToCurrentChannel(customer, ctx);
      }
      Logger.debug(`小程序保存用户信息:${customer?.id ?? '新用户'};时间:${new Date().getTime()}`);
      customer = await this.connection.getRepository(ctx, Customer).save(customer);
      this.eventBus.publish(
        new CustomerEvent(ctx, customer, 'created', {
          ...customer,
        }),
      );
      try {
        await this.interfaceYouZan.syncUser(ctx, customer);
      } catch (error) {
        Logger.error(`同步有赞用户会员信息错误:${error}`);
      }
    }
    weChatUser.customer = customer;
    await this.connection.getRepository(ctx, WeChatProgramUser).save(weChatUser);
    return this.connection.getRepository(ctx, User).findOneOrFail({
      where: {identifier: weChatUser.phone, deletedAt: IsNull()},
      relations: ['roles', 'roles.channels'],
    });
  }

  async getCustomerByPhone(ctx: RequestContext, phone: string) {
    const qb = this.listQueryBuilder.build(Customer, undefined, {
      ctx,
      channelId: ctx.channelId,
    });
    qb.andWhere(`${qb.alias}.phoneNumber= :phoneNumber`, {phoneNumber: phone});
    qb.andWhere(`${qb.alias}.deletedAt is null`);
    return qb.take(1).getOne();
  }

  async getWeChatUserInfoByJsCode(ctx: RequestContext, jsCode: string): Promise<UserInfoData> {
    const loginUrl = await this.getWeChatLoginUrl(ctx, jsCode);
    if (process.env.IS_TEST_LOGIN === 'true') {
      return {
        id: randomUUID(),
        email: '',
        firstName: 'test',
        lastName: 'test',
        unionId: randomUUID(),
      };
    }
    return new Promise((resolve, rejects) => {
      request(loginUrl, function (err, _res, data) {
        if (err) {
          rejects(err);
        }
        const returnData = JSON.parse(data);
        if (returnData.errcode && returnData.errcode !== 0) {
          Logger.error(`loginUrl:${loginUrl}`);
          Logger.error(`getWeChatUserInfoByJsCode error:${JSON.stringify(returnData)}`);
          rejects(returnData);
        }
        const profileData: UserInfoData = {
          id: returnData.openid,
          email: returnData.email!,
          firstName: returnData.given_name || '',
          lastName: returnData.family_name || '',
          unionId: returnData.unionid, //session_key:3JtHN52FGzXRfTOiF9QWhA==
        };
        if (process.env.APP_ENV === 'dev') {
          Logger.info(`开发环境不获取微信用户信息:${JSON.stringify(returnData)}`);
        }
        resolve(profileData);
      });
    });
  }
  generateRandomPhoneNumber() {
    // 中国手机号以1开头，第二位是3-9的任意数字，后面9位是随机数字
    const prefix = '1';
    const secondDigit = Math.floor(Math.random() * 7) + 3; // 生成3到9之间的数字
    const restDigits = Math.floor(Math.random() * 1000000000)
      .toString()
      .padStart(9, '0');
    return `${prefix}${secondDigit}${restDigits}`;
  }

  async getWechatUserPhone(ctx: RequestContext, jsCode: string): Promise<string> {
    const getPhoneUrl = await this.getWeChatPhoneUrl(ctx);
    if (process.env.IS_TEST_LOGIN === 'true') {
      return this.generateRandomPhoneNumber();
    }
    return new Promise((resolve, rejects) => {
      request(
        getPhoneUrl,
        {
          method: 'post',
          json: true,
          headers: {
            'content-type': 'application/json',
          },
          body: {code: jsCode},
        },
        function (_err, _res, data) {
          if (_err) {
            rejects(_err);
          }
          if (data?.errcode === 0) {
            if (data?.phone_info?.purePhoneNumber) {
              resolve(data.phone_info.purePhoneNumber);
            }
            rejects();
          }
          rejects();
        },
      );
    });
  }

  private async getWeChatPhoneUrl(ctx: RequestContext): Promise<string> {
    const wechatConfig = await this.weChatConfigService.findOne(ctx);
    if (!wechatConfig?.wechatProgram?.weChatAppId || !wechatConfig.wechatProgram.weChatAppSecret) {
      throw new Error('Configure the wechat information corresponding to the channel');
    }
    const token = await this.getAccessToken(ctx);
    if (!token) {
      throw new Error('wechat token not exist');
    }
    return this.weChatGetPhoneUrl.replace('{{access_token}}', token);
  }

  private async getWeChatLoginUrl(ctx: RequestContext, jsCode: string): Promise<string> {
    const wechatConfig = await this.weChatConfigService.findOne(ctx);
    if (!wechatConfig?.wechatProgram?.weChatAppId || !wechatConfig.wechatProgram.weChatAppSecret) {
      throw new Error('Configure the wechat information corresponding to the channel');
    }
    let weChatLoginUrl = this.weChatLoginUrl.replace('{{weChatAppSecret}}', wechatConfig.wechatProgram.weChatAppSecret);
    weChatLoginUrl = weChatLoginUrl.replace('{{weChatAppid}}', wechatConfig.wechatProgram.weChatAppId);
    return weChatLoginUrl.replace('{{code}}', jsCode);
  }

  async getAccessToken(ctx: RequestContext) {
    const weChatConfig = await this.weChatConfigService.findOne(ctx);
    const weChatAppId = weChatConfig?.wechatProgram.weChatAppId;
    const weChatAppSecret = weChatConfig?.wechatProgram.weChatAppSecret;
    if (!weChatAppId || !weChatAppSecret) {
      throw new Error('The channel is not configured with the public number');
    }
    const officialToken = (await this.kvsService.wechatToken.get(weChatAppId)) as WeChatToken;
    if (officialToken && new Date(officialToken.expiredAt) > DateTime.now().toJSDate()) {
      return officialToken.accessToken;
    }

    return this.requestToken(weChatAppId, weChatAppSecret);
  }

  async requestToken(weChatAppId: string, weChatAppSecret: string): Promise<string> {
    return new Promise((resolve, reject) => {
      request(
        `https://api.weixin.qq.com/cgi-bin/token?grant_type=client_credential&appid=${weChatAppId}&secret=${weChatAppSecret}`,
        (err, _res, data) => {
          if (err) {
            Logger.error(err);
            reject(err);
          }
          const jsonObj = JSON.parse(data);
          if (jsonObj?.access_token) {
            const expiredAt = DateTime.now().plus({hour: 1});
            const tokenObj: WeChatToken = {
              expiredAt: expiredAt.toJSDate(),
              accessToken: jsonObj.access_token,
            };
            // eslint-disable-next-line @typescript-eslint/no-floating-promises
            this.kvsService.wechatToken.set(weChatAppId, tokenObj, jsonObj.expires_in);
            resolve(jsonObj.access_token);
          } else {
            Logger.error(data);
            reject(data);
          }
        },
      );
    });
  }

  @Transaction()
  async deleteWechatUser(ctx: RequestContext, customerId: ID) {
    const customer = await this.connection.getRepository(ctx, Customer).findOneOrFail({where: {id: customerId}});
    const weChatUser = await this.getUserByPhone(ctx, customer.phoneNumber);
    if (weChatUser) {
      Logger.error(`删除customer时删除微信用户:${weChatUser.id}`);
      await this.connection.getRepository(ctx, WeChatProgramUser).delete({id: weChatUser.id});
    }
  }

  async getWeChatUserInfoByUserId(ctx: RequestContext, activeUserId: ID) {
    const customer = await this.customerService.findOneByUserId(ctx, activeUserId);
    if (!customer) {
      return null;
    }
    const weChatUser = await this.connection
      .getRepository(ctx, WeChatProgramUser)
      .createQueryBuilder('weChatUser')
      .leftJoinAndSelect('weChatUser.customer', 'customer')
      .leftJoinAndSelect('weChatUser.channel', 'channel')
      .where('customer.id = :customerId', {customerId: customer.id})
      .andWhere('channel.id = :channelId', {channelId: ctx.channelId})
      .take(1)
      .getOne();
    if (!weChatUser) {
      return null;
    }
    return weChatUser;
  }

  async setWechatMsgJumpPath(ctx: RequestContext, path: string) {
    const wechatConfig = await this.weChatConfigService.findOne(ctx);
    if (!wechatConfig) {
      throw Error('微信小程序配置未设置');
    }
    await this.setMsgJumpPath(ctx, path);
    await this.connection.getRepository(ctx, WeChatConfig).update(wechatConfig.id, {msgJumpPath: path});
    return this.weChatConfigService.findOne(ctx);
  }

  async setMsgJumpPath(ctx: RequestContext, path: string) {
    if (process.env.APP_ENV === 'dev') {
      Logger.info(`开发环境不设置微信消息跳转路径:${path}`);
      return;
    }
    const token = await this.getAccessToken(ctx);
    const url = this.setWeChatMsgJumpPathUrl.replace('{{access_token}}', token);
    return new Promise((resolve, reject) => {
      request(
        url,
        {
          method: 'post',
          json: true,
          headers: {
            'content-type': 'application/json',
          },
          body: {
            path: path,
          },
        },
        function (err, _res, data) {
          if (err) {
            Logger.error(err);
            reject(err);
          }
          if (data?.errcode === 0) {
            resolve(data);
          }
          reject(data);
        },
      );
    });
  }
}
