import {Injectable} from '@nestjs/common';
import {KvsService, MemoryStorageService} from '@scmally/kvs';
import {GiftCardOrderService, MemberService} from '@scmally/member';
import {CacheService} from '@scmally/member/dist/service/cache.service';
import {MinioService} from '@scmally/minio';
import {RedLockService} from '@scmally/red-lock';
import {
  ActiveOrderService,
  ChannelService,
  ConfigService,
  EntityHydrator,
  ErrorResult,
  EventBus,
  ID,
  idsAreEqual,
  Logger,
  Order,
  OrderLine,
  OrderPlacedEvent,
  OrderService,
  OrderSplitter,
  Payment,
  PaymentMetadata,
  PaymentMethod,
  PaymentMethodService,
  RequestContext,
  Transaction,
  TransactionalConnection,
  UserInputError,
} from '@vendure/core';
import {OrderStateTransitionError} from '@vendure/core/dist/common/error/generated-graphql-shop-errors';
import {randomUUID} from 'crypto';
import fs from 'fs';
import https from 'http';
import {DateTime} from 'luxon';
import WxPay from 'wechatpay-node-v3';
import {Ijsapi, Irefunds1, Irefunds2} from 'wechatpay-node-v3/dist/lib/interface';
import {TokenOpenidMapping, WeChatOfficialUser, WeChatOrderPaymentInfo, WeChatProgramUser} from '../entities';
import {ExclusionGroupError} from '../error.type';
import {
  OrderBuyType,
  OrderCustomFields,
  OrderPurchaseType,
  PaymentOrderType,
  PurchasePattern,
  WeChatPaymentType,
} from '../generated-shop-types';
import {NotificationResponse, WeChatInitPayInfo, WxCallBackState} from '../types';
import {WxChatUtil} from '../utils/wx_chat_util';
import {InterfaceBlindBoxOrder} from './abstract-blind-box';
import {InterfaceCommon} from './abstract-common';
import {InterfaceCoupon} from './abstract-coupon';
import {InterfaceExclusionGroup} from './abstract-exclusion-group';
import {InterfaceOrderPromotionResult} from './abstract-order-promotion';
import {InterfacePoints} from './abstract-points';
import {InterfaceRestrictions} from './abstract-restriction';
import {InterfaceShoppingCredits} from './abstract-shopping-credits';
import {InterfaceOrder} from './interface-order';
import {WeChatAuthService} from './wechat-auth.service';
import {WechatComponentService} from './wechat_component.service';
import {WeChatConfigService} from './wechat_config.service';
import {wechatPaymentHandler} from './wechat_payment_handler';
@Injectable()
export class WeChatPaymentService {
  // privateKey: fs.readFileSync(process.env.WECHAT_APICLIENT_KEY || ''), // 秘钥
  public interfaceBlindBoxOrder: InterfaceBlindBoxOrder;
  private interfaceCoupon: InterfaceCoupon;
  public interfaceCommon: InterfaceCommon;
  public interfaceRestrictions: InterfaceRestrictions;
  public interfacePoints: InterfacePoints;
  public interfaceOrder: InterfaceOrder;
  public interfaceExclusionGroup: InterfaceExclusionGroup;

  public interfaceOrderPromotionResult: InterfaceOrderPromotionResult;
  public interfaceShoppingCredits: InterfaceShoppingCredits;
  // <ChannelID,<appid,Pay>>
  // private payMap: Map<ID, Map<string, WxPay>>;
  // <PaymentMethodId,<appid,Pay>>
  private refundPayMp: Map<ID, Map<string, WxPay>>;
  constructor(
    private connection: TransactionalConnection,
    private orderService: OrderService,
    private activeOrderService: ActiveOrderService,
    private entityHydrator: EntityHydrator,
    private paymentMethodService: PaymentMethodService,
    private channelService: ChannelService,
    private minioService: MinioService,
    private memberService: MemberService,
    private weChatAuthService: WeChatAuthService,
    private weChatConfigService: WeChatConfigService,
    private wechatComponentService: WechatComponentService,

    private giftCardOrderService: GiftCardOrderService,

    private configService: ConfigService,
    private eventBus: EventBus,
    private orderSplitter: OrderSplitter,
    private redLockService: RedLockService,
    private memoryStorageService: MemoryStorageService,
    private kvsService: KvsService,
    private cacheService: CacheService,
  ) {
    // this.payMap = new Map();
    this.refundPayMp = new Map();
  }

  registerOrderPromotionResult(interfaceOrderPromotionResult: InterfaceOrderPromotionResult) {
    this.interfaceOrderPromotionResult = interfaceOrderPromotionResult;
  }

  registerBlindBoxOrder(interfaceBlindBoxOrder: InterfaceBlindBoxOrder) {
    this.interfaceBlindBoxOrder = interfaceBlindBoxOrder;
  }

  registerRestrictions(interfaceRestrictions: InterfaceRestrictions) {
    this.interfaceRestrictions = interfaceRestrictions;
  }

  registerOrder(interfaceOrder: InterfaceOrder) {
    this.interfaceOrder = interfaceOrder;
  }

  registerPoints(interfacePoints: InterfacePoints) {
    this.interfacePoints = interfacePoints;
  }

  registerShoppingCredits(interfaceShoppingCredits: InterfaceShoppingCredits) {
    this.interfaceShoppingCredits = interfaceShoppingCredits;
  }

  registerCoupon(interfaceCoupon: InterfaceCoupon) {
    this.interfaceCoupon = interfaceCoupon;
  }
  registerCommon(interfaceCommon: InterfaceCommon) {
    this.interfaceCommon = interfaceCommon;
  }
  registerExclusionGroup(interfaceExclusionGroup: InterfaceExclusionGroup) {
    this.interfaceExclusionGroup = interfaceExclusionGroup;
  }

  // 获取渠道的支付信息
  getChannelPaymentMethod(ctx: RequestContext) {
    const channelId = ctx.channelId;
    const memoryStorageCacheKey = `channelPaymentInfo:${channelId}`;
    const channelPaymentMethod = this.memoryStorageService.get(memoryStorageCacheKey) as Map<string, WxPay>;
    if (channelPaymentMethod) {
      return channelPaymentMethod;
    }
    return;
  }

  // 设置渠道的支付信息
  async setChannelPaymentMethod(ctx: RequestContext, channelPaymentMethod: Map<string, WxPay>) {
    const channelId = ctx.channelId;
    const memoryStorageCacheKey = `channelPaymentInfo:${channelId}`;
    const kvsCacheKey = `${channelId}`;
    // 存储到redis和内存
    await this.kvsService.channelPaymentInfo.set(kvsCacheKey, channelPaymentMethod);
    this.memoryStorageService.set(memoryStorageCacheKey, channelPaymentMethod);
  }

  // 清除渠道的支付信息
  async clearChannelPaymentMethod(ctx: RequestContext) {
    const channelId = ctx.channelId;
    const memoryStorageCacheKey = `channelPaymentInfo:${channelId}`;
    const kvsCacheKey = `${channelId}`;
    // 清除redis和内存
    await this.kvsService.channelPaymentInfo.del(kvsCacheKey);
    await this.cacheService.removeCache(memoryStorageCacheKey);
    this.memoryStorageService.delete(memoryStorageCacheKey);
  }

  async initPay(ctx: RequestContext, wechatAppId: string) {
    const channelPay = this.getChannelPaymentMethod(ctx);
    if (!channelPay?.get(wechatAppId)) {
      const {apiV3Key, mchAppId, apiclientKey, apiclientCert} = await this.getWxChatPaymentMethod(ctx);
      if (apiV3Key && mchAppId && apiclientKey && apiclientCert) {
        const apiclientCertUrl = await this.minioService.getFile(process.env.PRIVATE_BUCKET || '', apiclientCert);
        const apiclientKeyUrl = await this.minioService.getFile(process.env.PRIVATE_BUCKET || '', apiclientKey);
        const pay = new WxPay({
          appid: wechatAppId,
          mchid: mchAppId,
          // publicKey: fs.readFileSync(process.env.WECHAT_APICLIENT_CERT || ''), // 公钥
          // privateKey: fs.readFileSync(process.env.WECHAT_APICLIENT_KEY || ''), // 秘钥
          publicKey: fs.readFileSync(apiclientCertUrl), // 公钥
          privateKey: fs.readFileSync(apiclientKeyUrl), // 秘钥
          key: apiV3Key,
        });
        if (channelPay) {
          channelPay.set(wechatAppId, pay);
          // this.payMap.set(ctx.channelId, channelPay);
          await this.setChannelPaymentMethod(ctx, channelPay);
        } else {
          const wechatPay = new Map();
          wechatPay.set(wechatAppId, pay);
          // this.payMap.set(ctx.channelId, wechatPay);
          await this.setChannelPaymentMethod(ctx, wechatPay);
        }
      } else {
        throw Error('Wechat payment not configured');
      }
    }
  }

  async initRefundPay(ctx: RequestContext, wechatAppId: string, paymentMethodId: ID) {
    const paymentMethod = this.refundPayMp.get(paymentMethodId);
    if (!paymentMethod?.get(wechatAppId)) {
      const {apiV3Key, mchAppId, apiclientKey, apiclientCert} = await this.getWxChatPaymentMethod(ctx, paymentMethodId);
      if (apiV3Key && mchAppId && apiclientKey && apiclientCert) {
        const apiclientCertUrl = await this.minioService.getFile(process.env.PRIVATE_BUCKET || '', apiclientCert);
        const apiclientKeyUrl = await this.minioService.getFile(process.env.PRIVATE_BUCKET || '', apiclientKey);
        const pay = new WxPay({
          appid: wechatAppId,
          mchid: mchAppId,
          // publicKey: fs.readFileSync(process.env.WECHAT_APICLIENT_CERT || ''), // 公钥
          // privateKey: fs.readFileSync(process.env.WECHAT_APICLIENT_KEY || ''), // 秘钥
          publicKey: fs.readFileSync(apiclientCertUrl), // 公钥
          privateKey: fs.readFileSync(apiclientKeyUrl), // 秘钥
          key: apiV3Key,
        });
        if (paymentMethod) {
          paymentMethod.set(wechatAppId, pay);
          this.refundPayMp.set(paymentMethodId, paymentMethod);
        } else {
          const wechatPay = new Map();
          wechatPay.set(wechatAppId, pay);
          this.refundPayMp.set(paymentMethodId, wechatPay);
        }
      } else {
        throw Error('Wechat payment not configured');
      }
    }
  }
  /**
   * 创建支付订单
   */
  async createPaymentIntent(ctx: RequestContext, paymentType: WeChatPaymentType, orderId: ID) {
    const lock = await this.redLockService.lockResource(`Order:CreatePaymentIntent:${orderId}`);
    try {
      const weChatConfig = await this.weChatConfigService.findOne(ctx);
      let wechatAppId;
      if (paymentType === WeChatPaymentType.WechatProgram) {
        wechatAppId = weChatConfig?.wechatProgram.weChatAppId;
      } else if (paymentType === WeChatPaymentType.WechatOfficialAccounts) {
        wechatAppId = weChatConfig?.officialAccount.officialAppId;
      }
      Logger.debug(`paymentType:${paymentType},wechatAppId:${wechatAppId}`);
      if (!wechatAppId) {
        Logger.error(`Wechat mini program information is not configured`);
        throw new Error('Wechat mini program information is not configured');
      }
      const params = await this.getPaymentMethodArgs(ctx, paymentType, orderId);
      if (params.amount.total <= 0) {
        Logger.error(`订单支付金额为0`);
        await this.skipPayment(ctx, orderId, params);
        return {isSkipPay: true} as WeChatInitPayInfo;
      } else {
        Logger.debug(`订单支付数据为：${JSON.stringify(params)}`);
        await this.initPay(ctx, wechatAppId);
        // const pay = this.payMap.get(ctx.channelId)?.get(wechatAppId);
        const pay = this.getChannelPaymentMethod(ctx)?.get(wechatAppId);
        if (!pay) {
          Logger.error(`payment error! pay not exist!`);
          throw new Error('payment error! pay not exist!');
        }
        const wxPayInfo = await pay.transactions_jsapi(params);
        const wxPayInitData: WeChatInitPayInfo = {
          status: wxPayInfo.status,
          appId: wxPayInfo.appId,
          timeStamp: wxPayInfo.timeStamp,
          nonceStr: wxPayInfo.nonceStr,
          package: wxPayInfo.package,
          signType: wxPayInfo.signType,
          paySign: wxPayInfo.paySign,
        };
        return wxPayInitData;
      }
    } catch (error) {
      Logger.error(`创建支付订单失败:${error}`);
      // throw new Error(`创建支付订单失败:${error}`);
      throw error;
    } finally {
      await this.redLockService.unlockResource(lock);
    }
  }

  // 判断已经存在的支付订单，并且如果更换了OPENID，需要重新取消前一个支付订单，重新创建支付订单
  async checkOrderCancelPaymentInfo(ctx: RequestContext, order: Order) {
    const result = {
      isCancel: false,
      tokenOpenidMapping: null as TokenOpenidMapping | null,
      needsSaveNewPaymentInfo: false,
    };
    const orderId = order.id;
    const token = ctx.session?.token;
    if (token) {
      const tokenOpenIdMapping = await this.connection.getRepository(ctx, TokenOpenidMapping).findOne({
        where: {token},
      });
      result.tokenOpenidMapping = tokenOpenIdMapping;
      const openId = tokenOpenIdMapping?.openId;
      if (openId) {
        const orderPaymentInfo = await this.connection
          .getRepository(ctx, WeChatOrderPaymentInfo)
          .createQueryBuilder('orderPaymentInfo')
          .andWhere('orderPaymentInfo.orderId = :orderId', {orderId})
          .take(1)
          .getOne();
        if (orderPaymentInfo) {
          if (orderPaymentInfo.openId !== openId) {
            await this.cancelWeChatPayment(ctx, order);
            await this.generateNewOrderCode(ctx, order);
            await this.connection.getRepository(ctx, WeChatOrderPaymentInfo).delete(orderPaymentInfo.id);
            result.isCancel = true;
            result.needsSaveNewPaymentInfo = true;
          }
        } else {
          result.needsSaveNewPaymentInfo = true;
        }
      }
    }
    return result;
  }

  async generateNewOrderCode(ctx: RequestContext, order: Order) {
    try {
      const newOrderCode = this.generateDateString('Z');
      order.code = newOrderCode;
      // 修改订单的新订单号
      await this.connection
        .getRepository(ctx, Order)
        .update({id: order.id}, {customFields: {newOrderCode: newOrderCode}});
    } catch (error) {
      Logger.error(`update order newOrderCode error: ${error}`);
    }
  }

  generateDateString(str = 'Z') {
    const now = new Date();
    const year = now.getFullYear().toString().padStart(4, '0');
    const month = (now.getMonth() + 1).toString().padStart(2, '0');
    const day = now.getDate().toString().padStart(2, '0');
    const hours = now.getHours().toString().padStart(2, '0');
    const minutes = now.getMinutes().toString().padStart(2, '0');
    const seconds = now.getSeconds().toString().padStart(2, '0');
    const randomDigits = this.generateRandomNumber(9);

    const dateString = `${str}${year}${month}${day}${hours}${minutes}${seconds}${randomDigits}`;
    return dateString;
  }

  generateRandomNumber(length: number) {
    const randomNumbers = [];
    for (let i = 0; i < length; i++) {
      const randomNumber = Math.floor(Math.random() * 10); // 生成0到9之间的随机数字
      randomNumbers.push(randomNumber);
    }
    return randomNumbers.join('');
  }

  // 跳过支付完成订单
  @Transaction()
  async skipPayment(ctx: RequestContext, orderId: ID, params: Ijsapi) {
    const order = await this.orderService.findOne(ctx, orderId);
    if (!order) {
      Logger.error(`Unable to find order ${orderId}, unable to settle payment!`);
      throw Error(`Unable to find order ${orderId}, unable to settle payment!`);
    }
    const {method} = await this.getWxChatPaymentMethod(ctx);
    const channelToken = ctx.channel.token;
    const adminCtx = new RequestContext({
      apiType: 'admin',
      isAuthorized: true,
      channel: await this.channelService.getChannelFromToken(channelToken),
      authorizedAsOwnerOnly: false,
    });
    const addPaymentToOrderResult = await this.orderService.addPaymentToOrder(adminCtx, order.id, {
      method: method.code,
      metadata: {
        methodId: method.id,
        transactionId: randomUUID(),
        payerTotal: params.amount.total,
        channelToken: ctx.channel.token,
        appid: params.appid,
      },
    });
    const orderCode = order.code;
    await this.orderStatusJump(ctx, order);
    if ((addPaymentToOrderResult as ErrorResult).errorCode) {
      Logger.error(`Error adding payment to order ${orderCode}: ${(addPaymentToOrderResult as ErrorResult).message}`);
      throw Error(`Error adding payment to order ${orderCode}: ${(addPaymentToOrderResult as ErrorResult).message}`);
    }
    Logger.debug(`Payment for order ${orderCode} settled`);
  }

  /**
   * 获取支付配置信息
   */
  public async getWxChatPaymentMethod(ctx: RequestContext, paymentMethodId?: ID) {
    let method: PaymentMethod | undefined;
    if (!paymentMethodId) {
      const {items} = await this.paymentMethodService.findAll(ctx, {filter: {enabled: {eq: true}}});
      method = items.find(item => item.handler.code === wechatPaymentHandler.code);
    } else {
      method = await this.paymentMethodService.findOne(ctx, paymentMethodId);
    }
    if (!method) {
      throw Error(`No paymentMethod configured with handler ${wechatPaymentHandler.code}`);
    }
    const apiV3Key = method.handler.args.find(arg => arg.name === 'apiV3Key');
    const mchAppId = method.handler.args.find(arg => arg.name === 'mchAppId');
    // const redirectUrl = method.handler.args.find(arg => arg.name === 'redirectUrl');
    // const wechatAppId = method.handler.args.find(arg => arg.name === 'wechatAppId');
    const apiclientKey = method.handler.args.find(arg => arg.name === 'apiclientKey');
    const apiclientCert = method.handler.args.find(arg => arg.name === 'apiclientCert');
    const mchAppName = method.handler.args.find(arg => arg.name === 'mchAppName');
    if (!apiV3Key || !mchAppId || !apiclientKey || !mchAppName || !apiclientCert) {
      Logger.error(`CreatePaymentIntent failed, because no apiKey or redirect is configured for ${method.code}`);
      throw Error(`Payment method ${method.code} has no apiKey, sharedSecret or redirectUrl configured`);
    }
    return {
      apiV3Key: apiV3Key.value,
      mchAppId: mchAppId.value,
      // wechatAppId: wechatAppId.value,
      apiclientKey: apiclientKey.value,
      apiclientCert: apiclientCert.value,
      mchAppName: mchAppName.value,
      // redirectUrl: redirectUrl?.value.endsWith('/') ? redirectUrl?.value.slice(0, -1) : redirectUrl?.value, // remove appending slash
      method,
    };
  }

  async settlePayment(
    orderCode: string,
    channelToken: string,
    transactionId: string,
    payerTotal: number,
    appid: string,
    attach: PaymentOrderType,
  ): Promise<void> {
    Logger.debug(`支付回调开始验证,orderCode:${orderCode}`);
    const ctx = new RequestContext({
      apiType: 'admin',
      isAuthorized: true,
      channel: await this.channelService.getChannelFromToken(channelToken),
      authorizedAsOwnerOnly: false,
    });
    const {method} = await this.getWxChatPaymentMethod(ctx);
    if (orderCode.indexOf('_') !== -1) {
      orderCode = orderCode.slice(0, orderCode.indexOf('_'));
    }
    if (attach === PaymentOrderType.Order) {
      const order = await this.getOrderByOrderCode(ctx, orderCode);
      if (!order) {
        Logger.error(`Unable to find order ${orderCode}, unable to settle payment!`);
        throw Error(`Unable to find order ${orderCode}, unable to settle payment!`);
      }
      if (order.state !== 'ArrangingPayment') {
        const lock = await this.redLockService.lockResource(`Order:OrderUpdate:${order.id}`);
        try {
          const transitionToStateResult = await this.orderService.transitionToState(ctx, order.id, 'ArrangingPayment');
          if (transitionToStateResult instanceof OrderStateTransitionError) {
            Logger.error(
              `Error transitioning order ${order.code} from ${transitionToStateResult.fromState} to ${transitionToStateResult.toState}: ${transitionToStateResult.message}`,
            );
            throw Error(
              `Error transitioning order ${order.code} from ${transitionToStateResult.fromState} to ${transitionToStateResult.toState}: ${transitionToStateResult.message}`,
            );
          }
        } catch (error) {
          Logger.error(`订单支付失败,更新订单状态失败,订单ID:${order.id}`);
          throw Error(`订单支付失败,更新订单状态失败,订单ID:${order.id}`);
        } finally {
          await this.redLockService.unlockResource(lock);
        }
      }
      const addPaymentToOrderResult = await this.orderService.addPaymentToOrder(ctx, order.id, {
        method: method.code,
        metadata: {
          methodId: method.id,
          transactionId: transactionId,
          payerTotal: payerTotal,
          channelToken: channelToken,
          appid: appid,
        },
      });

      await this.orderStatusJump(ctx, order);
      if ((addPaymentToOrderResult as ErrorResult).errorCode) {
        Logger.error(`Error adding payment to order ${orderCode}: ${(addPaymentToOrderResult as ErrorResult).message}`);
        throw Error(`Error adding payment to order ${orderCode}: ${(addPaymentToOrderResult as ErrorResult).message}`);
      }
      // Logger.debug(`addPaymentToOrderResult:${JSON.stringify(addPaymentToOrderResult)}`);
      Logger.debug(`Payment for order ${orderCode} settled`);
    } else if (attach === PaymentOrderType.MembershipOrder) {
      const memberOrder = await this.memberService.findOneOrderByCode(ctx, orderCode);
      if (!memberOrder) {
        Logger.error(`Unable to find memberOrder ${orderCode}, unable to settle payment!`);
        throw Error(`Unable to find memberOrder ${orderCode}, unable to settle payment!`);
      }
      await this.memberService.addPaymentToOrder(ctx, memberOrder.id, {
        method: method.code,
        metadata: {
          methodId: method.id,
          transactionId: transactionId,
          payerTotal: payerTotal,
          channelToken: channelToken,
          appid: appid,
        },
      });
    } else if (attach === PaymentOrderType.GiftCardOrder) {
      const giftCardOrder = await this.giftCardOrderService.findOneByCode(ctx, orderCode);
      if (!giftCardOrder) {
        Logger.error(`Unable to find giftCardOrder ${orderCode}, unable to settle payment!`);
        throw Error(`Unable to find giftCardOrder ${orderCode}, unable to settle payment!`);
      }
      await this.giftCardOrderService.addPaymentToOrder(ctx, giftCardOrder.id, {
        method: method.code,
        metadata: {
          methodId: method.id,
          transactionId: transactionId,
          payerTotal: payerTotal,
          channelToken: channelToken,
          appid: appid,
        },
      });
    } else if (attach === PaymentOrderType.BlindBoxBuyOrder) {
      const blindBoxOrderId = await this.interfaceBlindBoxOrder.findOneByCode(ctx, orderCode);
      if (!blindBoxOrderId) {
        Logger.error(`Unable to find blindBoxOrder ${orderCode}, unable to settle payment!`);
        throw Error(`Unable to find blindBoxOrder ${orderCode}, unable to settle payment!`);
      }
      await this.interfaceBlindBoxOrder.addPaymentToOrder(ctx, blindBoxOrderId, {
        method: method.code,
        metadata: {
          methodId: method.id,
          transactionId: transactionId,
          payerTotal: payerTotal,
          channelToken: channelToken,
          appid: appid,
        },
      });
    }
  }
  async getOrderByOrderCode(ctx: RequestContext, orderCode: string) {
    let order = await this.orderService.findOneByCode(ctx, orderCode);
    if (!order) {
      const orderInfo = await this.connection
        .getRepository(ctx, Order)
        .createQueryBuilder('order')
        .andWhere('order.customFieldsNewordercode = :orderCode', {orderCode})
        .select('order.id', 'orderId')
        .getRawOne<{orderId: ID}>();
      if (orderInfo) {
        order = await this.orderService.findOne(ctx, orderInfo.orderId);
      }
      if (!order) {
        Logger.error(`Unable to find order ${orderCode}, unable to settle payment!`);
        throw Error(`Unable to find order ${orderCode}, unable to settle payment!`);
      }
    }
    return order;
  }
  //因为在创建待付款订单时把订单的活跃状态改成false了，状态跳转方法onTransitionEnd修改支付时间代码不执行
  //所以这里要把订单的下单时间手动修改一下
  async orderStatusJump(ctx: RequestContext, order: Order) {
    const lock = await this.redLockService.lockResource(`Order:OrderUpdate:${order.id}`);
    try {
      const {orderPlacedStrategy} = this.configService.orderOptions;
      const shouldSetAsPlaced = orderPlacedStrategy.shouldSetAsPlaced(ctx, 'ArrangingPayment', 'PaymentSettled', order);
      if (shouldSetAsPlaced) {
        // await Promise.all(
        //   order.lines.map(line =>
        //     this.connection.getRepository(ctx, OrderLine).update(line.id, {orderPlacedQuantity: line.quantity}),
        //   ),
        // );
        // 批量更新订单行的下单数量
        await this.connection
          .getRepository(ctx, OrderLine)
          .createQueryBuilder()
          .update(OrderLine)
          .set({orderPlacedQuantity: () => 'quantity'})
          .where('orderId = :orderId', {orderId: order.id})
          .execute();
        await this.connection
          .getRepository(ctx, Order)
          .update({id: order.id}, {active: false, orderPlacedAt: new Date()});
        this.eventBus.publish(new OrderPlacedEvent('ArrangingPayment', 'PaymentSettled', ctx, order));
        await this.orderSplitter.createSellerOrders(ctx, order);
      }
    } catch (error) {
      Logger.error(`订单状态跳转失败,订单ID:${order.id}`);
      throw Error(`订单状态跳转失败,订单ID:${order.id}`);
    } finally {
      await this.redLockService.unlockResource(lock);
    }
  }

  /**
   * 退款
   */
  async createRefund(oldCtx: RequestContext, amount: number, payment: Payment, order: Order) {
    Logger.debug(`微信退款订单:${order.code},退款金额:${amount}`);
    const metadata = payment.metadata;
    Logger.debug(`微信退款订单:${order.code},支付方式:${metadata.methodId},支付金额:${metadata.payerTotal}`);
    const ctx = new RequestContext({
      apiType: 'admin',
      isAuthorized: true,
      channel: await this.channelService.getChannelFromToken(metadata.channelToken),
      authorizedAsOwnerOnly: false,
    });
    await this.initRefundPay(ctx, metadata.appid, metadata.methodId);
    //TODO 退款金额高于支付金额 非生产环境下退款金额等于支付金额
    if (amount > metadata.payerTotal && process.env.APP_ENV !== 'production') {
      Logger.debug(`refund amount > payment amount!`);
      amount = metadata.payerTotal;
      payment.amount = metadata.payerTotal;
      Logger.debug(`refund amount > payment amount!amount:${amount},payment.amount:${payment.amount}}`);
    }
    let orderTotalAmount = 0;
    if (order instanceof Order) {
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      orderTotalAmount = (order.customFields as any).orderPromotionResult?.promResult?.orderTotalPrice;
    } else {
      orderTotalAmount = payment.amount;
    }
    if (orderTotalAmount && amount > orderTotalAmount) {
      amount = orderTotalAmount;
    }
    if (process.env.APP_ENV !== 'production') {
      orderTotalAmount = metadata.payerTotal;
    }

    const pay = this.refundPayMp.get(metadata.methodId)?.get(metadata.appid);
    if (!pay) {
      Logger.error(`payment error! pay not exist!`);
      throw new Error('payment error! pay not exist!');
    }
    const params = await this.getRefundArgs(metadata, payment, amount, order, orderTotalAmount);
    Logger.debug(`微信退款订单:${order.code},退款参数:${JSON.stringify(params)}`);
    return pay.refunds(params);
  }

  /**
   * 商家向用户转账
   * @param ctx
   * @param outDetailNo 订单号
   * @param transferRemark 转账备注
   * @param transferAmount 转账金额
   * @param userIdentifier 用户标识
   * @returns
   */
  async batchesTransfer(
    ctx: RequestContext,
    outDetailNo: string,
    transferRemark: string,
    transferAmount: number,
    userIdentifier: string,
  ) {
    const wxUser = await this.weChatAuthService.getUserByPhone(ctx, userIdentifier);
    if (!wxUser) {
      throw new Error('wechat user not exist');
    }
    const params = {
      // eslint-disable-next-line @typescript-eslint/naming-convention
      out_batch_no: outDetailNo,
      // eslint-disable-next-line @typescript-eslint/naming-convention
      batch_name: transferRemark,
      // eslint-disable-next-line @typescript-eslint/naming-convention
      batch_remark: transferRemark,
      // eslint-disable-next-line @typescript-eslint/naming-convention
      total_amount: transferAmount,
      // eslint-disable-next-line @typescript-eslint/naming-convention
      total_num: 1,
      // eslint-disable-next-line @typescript-eslint/naming-convention
      transfer_detail_list: [
        {
          // eslint-disable-next-line @typescript-eslint/naming-convention
          out_detail_no: outDetailNo,
          // eslint-disable-next-line @typescript-eslint/naming-convention
          transfer_amount: transferAmount,
          // eslint-disable-next-line @typescript-eslint/naming-convention
          transfer_remark: transferRemark,
          openid: wxUser.openId,
        },
      ],
    };
    const weChatConfig = await this.weChatConfigService.findOne(ctx);
    const wechatAppId = weChatConfig?.wechatProgram.weChatAppId;
    if (!wechatAppId) {
      throw new Error('Wechat mini program information is not configured');
    }
    await this.initPay(ctx, wechatAppId);
    // const pay = this.payMap.get(ctx.channelId)?.get(wechatAppId);
    const pay = this.getChannelPaymentMethod(ctx)?.get(wechatAppId);
    if (!pay) {
      throw new Error('payment error! pay not exist!');
    }
    return pay.batches_transfer(params);
  }

  /**
   * 创建退款信息
   */
  async getRefundArgs(
    metadata: PaymentMetadata,
    payment: Payment,
    amount: number,
    order: Order,
    orderTotalAmount: number,
  ): Promise<Irefunds1 | Irefunds2> {
    const jsapiData: Irefunds1 = {
      // eslint-disable-next-line @typescript-eslint/naming-convention
      transaction_id: metadata.transactionId,
      // eslint-disable-next-line @typescript-eslint/naming-convention
      out_refund_no: `${order.code}_${WxChatUtil.getRandomStr(6)}`,
      // eslint-disable-next-line @typescript-eslint/naming-convention
      notify_url: process.env.PROJECT_ADDRESS + '/wechatCallBacks/refundCallBacks',
      amount: {
        total: orderTotalAmount || payment.metadata.payerTotal,
        currency: 'CNY',
        refund: amount,
      },
    };
    return jsapiData;
  }

  /**
   * 创建支付信息
   */
  @Transaction()
  async getPaymentMethodArgs(ctx: RequestContext, paymentType: WeChatPaymentType, orderId: ID): Promise<Ijsapi> {
    // 是否再次拉起支付
    let isAgain = false;
    let order;
    if (!orderId) {
      order = await this.activeOrderService.getActiveOrder(ctx, undefined);
    } else {
      order = await this.orderService.findOne(ctx, orderId);
    }
    if (!order) {
      throw Error('No active order found for session');
    }
    if (order?.state === 'ArrangingPayment') {
      isAgain = true;
    }
    await this.entityHydrator.hydrate(ctx, order, {
      relations: ['lines', 'customer', 'shippingLines'],
    });
    if (!order.lines?.length) {
      throw Error('Cannot create payment intent for empty order');
    }
    if (!order.customer) {
      throw Error('Cannot create payment intent for order without customer');
    }
    if (!order.shippingLines?.length) {
      throw Error('Cannot create payment intent for order without shippingMethod');
    }
    const user = order.customer.user;
    if (!user) {
      throw Error('Cannot create payment intent for order without user');
    }
    // 验证订单商品库存
    if (!isAgain) {
      const inventoryCheckResult = await this.interfaceOrderPromotionResult.checkInventory(ctx, order, true);
      if (inventoryCheckResult?.isUpdateCount) {
        throw new Error('订单存在商品库存不足');
      }
    }
    const skuIds = order.lines.map(line => line.productVariantId);
    // 产品要求积分兑换商品不需要验证 限购组和限购区域购买逻辑
    if ((order.customFields as OrderCustomFields)?.buyType !== OrderBuyType.PointsExchange) {
      const checkExclusionResult = await this.interfaceExclusionGroup.checkProductCanCheckout(ctx, skuIds);
      if (checkExclusionResult && checkExclusionResult.canCheckout === false) {
        throw new ExclusionGroupError(checkExclusionResult.reason);
      }
      await this.interfaceRestrictions.checkProductRegionRestrictions(ctx, order);
    }
    await this.interfaceCommon.checkWhetherTheUserIsAMember(ctx, order);
    // 验证优惠券是否可用
    const isCouponAvailable = await this.interfaceCoupon.isCouponAvailable(ctx, order.customer.id, order.id);
    if (!isCouponAvailable) {
      throw Error('Coupon is not available, please reorder');
    }
    // 修改使用了盲盒订单的状态
    await this.interfaceBlindBoxOrder.updateBlindBoxOrderStatus(ctx, order);
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    if ((order.customFields as any).purchaseType === OrderPurchaseType.OutrightPurchase) {
      await this.interfaceCommon.bindingCartOrderToOutrightPurchase(ctx, order.id);
      await this.removeShoppingCartProduct(ctx, order);
    }
    const result = await this.checkOrderCancelPaymentInfo(ctx, order);
    let weChatCallBackUrl = '';
    let wechatUser: WeChatProgramUser | WeChatOfficialUser | undefined;
    if (paymentType === WeChatPaymentType.WechatProgram) {
      wechatUser = await this.weChatAuthService.getUserByPhone(ctx, user.identifier);
      weChatCallBackUrl = process.env.PROJECT_ADDRESS + '/wechatCallBacks/programPaymentCallBacks';
    } else if (paymentType === WeChatPaymentType.WechatOfficialAccounts) {
      wechatUser = await this.wechatComponentService.getUserByPhone(ctx, user.identifier);
      weChatCallBackUrl = process.env.PROJECT_ADDRESS + 'wechatCallBacks/officialPaymentCallBacks';
    }
    if (!wechatUser) {
      throw Error('Cannot create payment intent for order without wechatUser');
    }
    let total = 0;
    const customField = order.customFields as OrderCustomFields;
    const periods = customField.periods || 1;
    if (!customField) {
      throw Error('order customField Not set');
    }
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    const orderTotalPrice = (order.customFields as any).orderPromotionResult?.promResult?.orderTotalPrice;
    if (!orderTotalPrice && orderTotalPrice !== 0) {
      throw Error(`orderTotalPrice Not set,customFields:${JSON.stringify(order.customFields)}`);
    }
    const orderTotalPoints = Number(
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      (order.customFields as any).orderPromotionResult?.promResult?.orderTotalPoints ?? 0,
    );
    await this.interfaceShoppingCredits.checkAndDeductShoppingCredits(ctx, order, isAgain);
    await this.interfacePoints.checkPoints(ctx, order, orderTotalPoints, isAgain);
    if (process.env.APP_ENV !== 'production' && orderTotalPrice !== 0) {
      //测试订单金额等于 订单项的长度    方便测试退款
      const orderLineLength = order.lines.length || 1;
      total = orderLineLength * periods;
    } else {
      total = orderTotalPrice * periods;
    }
    Logger.debug(`channelToken:${ctx.channel.token}`);
    if (order.state !== 'ArrangingPayment') {
      const lock = await this.redLockService.lockResource(`Order:OrderUpdate:${order.id}`);
      try {
        const transitionToStateResult = await this.orderService.transitionToState(ctx, order.id, 'ArrangingPayment');
        if (transitionToStateResult instanceof OrderStateTransitionError) {
          Logger.error(
            `Error transitioning order ${order.code} from ${transitionToStateResult.fromState} to ${transitionToStateResult.toState}: ${transitionToStateResult.message}`,
          );
          throw Error(
            `Error transitioning order ${order.code} from ${transitionToStateResult.fromState} ` +
              `to ${transitionToStateResult.toState}: ${transitionToStateResult.message}`,
          );
        }
      } catch (error) {
        Logger.error(`订单支付失败,更新订单状态失败,订单ID:${order.id}`);
        throw Error(`订单支付失败,更新订单状态失败,订单ID:${order.id}`);
      } finally {
        await this.redLockService.unlockResource(lock);
      }
    }
    const timeoutPeriodToBePaid = DateTime.fromJSDate(new Date())
      .plus({minutes: Number(process.env.TIMEOUT_ORDER_TO_BE_PAID_MINUTES || 30)})
      .toJSDate();
    if (orderTotalPoints > 0) {
      // 积分冻结
      await this.interfacePoints.freezePoints(ctx, order, orderTotalPoints, isAgain);
      // 添加积分兑换商品销量
      await this.interfacePoints.addPointsExchangeProductSales(ctx, order, isAgain);
    }
    const lock = await this.redLockService.lockResource(`Order:OrderUpdate:${order.id}`);
    try {
      await this.connection.getRepository(ctx, Order).update(order.id, {active: false});
      await this.orderService.updateCustomFields(ctx, order.id, {
        timeToPlaceOrder: new Date(),
        timeoutPeriodToBePaid: timeoutPeriodToBePaid,
      });
    } catch (error) {
      Logger.error(`订单支付失败,更新订单状态失败,订单ID:${order.id}`);
    } finally {
      await this.redLockService.unlockResource(lock);
    }
    let orderCode = order.code;
    if (customField?.newOrderCode) {
      orderCode = customField.newOrderCode;
    }
    const paymentObj: Ijsapi = {
      //订单描述
      description: `Order ${orderCode}`,
      // eslint-disable-next-line @typescript-eslint/naming-convention
      out_trade_no: `${orderCode}`,
      // eslint-disable-next-line @typescript-eslint/naming-convention
      notify_url: `${weChatCallBackUrl}/${ctx.channel.token}`,
      amount: {
        total: total,
      },
      payer: {
        openid: wechatUser.openId,
      },
      attach: PaymentOrderType.Order,
    };
    if (result.needsSaveNewPaymentInfo) {
      await this.connection.getRepository(ctx, WeChatOrderPaymentInfo).save({
        orderId: order.id,
        openId: wechatUser.openId,
        paymentParams: paymentObj,
      });
    }
    return paymentObj;
  }

  async removeShoppingCartProduct(ctx: RequestContext, order: Order) {
    Logger.debug(`购物车下单，需要删除购物车订单项`);
    const shoppingTrolleyOrder = await this.getActiveOrderByType(ctx, OrderPurchaseType.ShoppingTrolley);
    const lineIds = [];
    for (const line of order.lines) {
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      if (!line || (line.customFields as any).purchasePattern === PurchasePattern.PurchasePremium) {
        continue;
      }
      const productVariantId = line.productVariantId;
      for (const shoppingTrolleyLine of shoppingTrolleyOrder.lines) {
        const orderLineId = shoppingTrolleyLine.id;
        Logger.debug(`需要删除的购物车订单项有:${orderLineId}}`);
        if (shoppingTrolleyLine.productVariantId === productVariantId) {
          lineIds.push(orderLineId);
        }
      }
    }
    await this.interfaceOrder.removeItemFromOrder(ctx, shoppingTrolleyOrder.id, lineIds, shoppingTrolleyOrder, true);
    // if (shoppingOrder?.lines?.length === 0) {
    //   Logger.debug(`购物车订单项已经全部删除,删除购物车优惠券`);
    //   await this.interfaceCoupon.deleteShoppingCartPromotion(ctx, shoppingOrder.id);
    // }
    return order;
  }

  getOrderLineOrThrow(order: Order, orderLineId: ID): OrderLine {
    const orderLine = order.lines.find(line => idsAreEqual(line.id, orderLineId));
    if (!orderLine) {
      throw new UserInputError(`error.order-does-not-contain-line-with-id`, {id: orderLineId});
    }
    return orderLine;
  }

  async getActiveOrderByType(ctx: RequestContext, type: OrderPurchaseType) {
    const sessionOrder = await this.activeOrderService.getActiveOrder(ctx, {activeOrderType: {type}}, true);
    if (!sessionOrder) {
      throw new Error('order empty');
    }
    const order = await this.orderService.findOne(ctx, sessionOrder.id, ['lines', 'lines.productVariant']);
    if (!order) {
      throw new Error('order empty');
    }
    return order;
  }

  async wechatPaymentCallBack(
    reqCtx: RequestContext,
    channelToken: string,
    wechatPaySignature: string,
    wechatPaySerial: string,
    wechatPayNonce: string,
    wechatPayTimestamp: string,
    paymentType: WeChatPaymentType,
  ): Promise<NotificationResponse> {
    const params = {
      body: reqCtx.req?.body, // 请求体 body
      signature: wechatPaySignature,
      serial: wechatPaySerial,
      nonce: wechatPayNonce,
      timestamp: wechatPayTimestamp,
    };
    const ctx = new RequestContext({
      apiType: 'admin',
      isAuthorized: true,
      channel: await this.channelService.getChannelFromToken(channelToken),
      authorizedAsOwnerOnly: false,
    });
    Logger.debug(`成功接受支付回调信息。${JSON.stringify(params)}`);
    const weChatConfig = await this.weChatConfigService.findOne(ctx);
    Logger.debug(`微信配置信息：${JSON.stringify(weChatConfig)},ctx.channelID:${ctx.channelId}`);
    let wechatAppId;
    if (paymentType === WeChatPaymentType.WechatProgram) {
      wechatAppId = weChatConfig?.wechatProgram.weChatAppId;
    } else if (paymentType === WeChatPaymentType.WechatOfficialAccounts) {
      wechatAppId = weChatConfig?.officialAccount.officialAppId;
    }
    Logger.debug(`支付回调的type是:${paymentType},wechatAppId:${wechatAppId}`);
    if (!wechatAppId) {
      throw new Error('Wechat mini program information is not configured');
    }
    const dataBody = reqCtx.req?.body;
    await this.initPay(ctx, wechatAppId);
    // const pay = this.payMap.get(ctx.channelId)?.get(wechatAppId);
    const pay = this.getChannelPaymentMethod(ctx)?.get(wechatAppId);
    if (!pay) {
      throw new Error('payment error! pay not exist!');
    }

    // TODO 平台证书验签，新的微信支付不支持了，暂时不通古哦这个方式来验证，使用查询订单的方式验证
    // const ret = await pay.verifySign(params);
    const ret = true;
    // 临时解决验签名

    if (ret) {
      const resource = dataBody.resource;
      const ciphertext = resource.ciphertext;
      const associatedData = resource.associated_data;
      const nonce = resource.nonce;
      // eslint-disable-next-line
      const result: any = pay.decipher_gcm(ciphertext, associatedData, nonce);

      // 临时解决验签名
      // eslint-disable-next-line
      const weOrder =
        result && result['trade_state'] === WxCallBackState.SUCCESS && result.out_trade_no // eslint-disable-next-line
          ? await pay.query({out_trade_no: result.out_trade_no})
          : null;
      if (result && weOrder && weOrder.trade_state === WxCallBackState.SUCCESS) {
        // 临时解决验签名
        // if (result) {
        const tradeState = result['trade_state'];
        switch (tradeState) {
          case WxCallBackState.SUCCESS:
            // eslint-disable-next-line no-case-declarations
            const outTradeNo = result.out_trade_no; //订单号
            // eslint-disable-next-line no-case-declarations
            const transactionId = result.transaction_id;
            // eslint-disable-next-line no-case-declarations
            const payerTotal = result.amount.payer_total;
            // eslint-disable-next-line no-case-declarations
            const attach = result.attach;
            // eslint-disable-next-line no-case-declarations
            const appid = result.appid;
            await this.settlePayment(outTradeNo, channelToken, transactionId, payerTotal, appid, attach);
            Logger.debug(`支付回调验证完成`);
            break;
          case WxCallBackState.REFUND:
            break;
          case WxCallBackState.NOT_PAY:
            break;
          case WxCallBackState.CLOSED:
            break;
          case WxCallBackState.REVOKED:
            break;
          case WxCallBackState.USER_PAYING:
            break;
          case WxCallBackState.PAY_ERROR:
            break;
          default:
            break;
        }
      }
      const notificationResponse: NotificationResponse = {
        code: 'SUCCESS',
        message: '接收通知成功',
      };
      return notificationResponse;
    }
    const notificationResponse: NotificationResponse = {
      code: 'FAIL',
      message: '失败',
    };
    return notificationResponse;
  }

  /**
   * 发送微信红包
   * @param ctx
   * @param userIdentifier 用户code
   * @param outDetailNo 订单编号
   * @param amount 红包金额
   * @param wishing 祝福语
   * @param actName 活动名称
   * @param transferRemark 活动备注
   * @returns
   */
  async giveRedEnvelopes(
    ctx: RequestContext,
    userIdentifier: string,
    outDetailNo: string,
    amount: number,
    wishing: string,
    actName: string,
    transferRemark: string,
  ) {
    const wxUser = await this.weChatAuthService.getUserByPhone(ctx, userIdentifier);
    if (!wxUser) {
      throw new Error('wechat user not exist');
    }
    const weChatConfig = await this.weChatConfigService.findOne(ctx);
    if (!weChatConfig?.wechatProgram?.weChatAppId) {
      throw new Error('Wechat mini program information is not configured');
    }
    const wechatAppId = weChatConfig.wechatProgram.weChatAppId;
    const {apiV3Key, mchAppId, mchAppName} = await this.getWxChatPaymentMethod(ctx);
    return new Promise((resolve, rejects) => {
      //设置参数模型
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      const paramModel: any = {
        // eslint-disable-next-line @typescript-eslint/naming-convention
        nonce_str: WxChatUtil.getRandomStr(32),
        wxappid: wechatAppId,
        // eslint-disable-next-line @typescript-eslint/naming-convention
        mch_id: mchAppId,
        // eslint-disable-next-line @typescript-eslint/naming-convention
        mch_billno: outDetailNo,
        // eslint-disable-next-line @typescript-eslint/naming-convention
        send_name: mchAppName,
        // eslint-disable-next-line @typescript-eslint/naming-convention
        re_openid: wxUser.openId,
        // eslint-disable-next-line @typescript-eslint/naming-convention
        total_amount: amount,
        // eslint-disable-next-line @typescript-eslint/naming-convention
        total_num: 1,
        wishing: wishing,
        // eslint-disable-next-line @typescript-eslint/naming-convention
        client_ip: '***************',
        // eslint-disable-next-line @typescript-eslint/naming-convention
        act_name: actName,
        remark: transferRemark,
        // eslint-disable-next-line @typescript-eslint/naming-convention
        notify_way: 'JSAPI',
        // eslint-disable-next-line @typescript-eslint/naming-convention
        scene_id: 'PRODUCT_5',
      };
      let attrStr = WxChatUtil.getSortedParameter(paramModel);
      attrStr += 'key=' + apiV3Key; //key为商户平台设置的密钥key
      paramModel.sign = WxChatUtil.getMD5(attrStr);

      //构造POST参数xml
      let contents = '<xml>';
      for (const attr in paramModel) {
        contents += WxChatUtil.makeXMLNode(attr, paramModel[attr], false);
      }
      contents += '</xml>';
      //配置请求选项
      const options = {
        host: 'api.mch.weixin.qq.com',
        port: 443,
        path: '/mmpaymkttransfers/sendredpack',
        key: fs.readFileSync(__dirname + '/cert/apiclient_key.pem'),
        cert: fs.readFileSync(__dirname + '/cert/apiclient_cert.pem'),
        method: 'POST',
        agent: new https.Agent(),
      };

      options.agent = new https.Agent(options);
      //发送Post请求
      const request = https.request(options, function (response) {
        response.setEncoding('utf8');
        response.on('data', function (data) {
          resolve(data);
        });
      });

      request.write(contents);
      request.end();
      request.on('error', function (err) {
        rejects(err);
      });
    });
  }

  async purchaseGiftCard(ctx: RequestContext, paymentType: WeChatPaymentType, giftCardId: ID) {
    const giftCardOrder = await this.giftCardOrderService.createGiftCardOrder(ctx, giftCardId);
    return this.createAPurchasePaymentOrder(
      ctx,
      paymentType,
      giftCardOrder.id,
      PaymentOrderType.GiftCardOrder,
      giftCardOrder.code,
    );
  }

  async purchaseMembership(ctx: RequestContext, paymentType: WeChatPaymentType, membershipPlanId: ID) {
    const memberOrder = await this.memberService.purchaseMembership(ctx, membershipPlanId);
    return this.createAPurchasePaymentOrder(
      ctx,
      paymentType,
      memberOrder.id,
      PaymentOrderType.MembershipOrder,
      memberOrder.code,
    );
  }

  /**
   * 创建额外购买支付订单
   */
  async createAPurchasePaymentOrder(
    ctx: RequestContext,
    paymentType: WeChatPaymentType,
    orderId: ID,
    type = PaymentOrderType.MembershipOrder,
    orderCode?: string,
  ) {
    const weChatConfig = await this.weChatConfigService.findOne(ctx);
    let wechatAppId;
    if (paymentType === WeChatPaymentType.WechatProgram) {
      wechatAppId = weChatConfig?.wechatProgram.weChatAppId;
    } else if (paymentType === WeChatPaymentType.WechatOfficialAccounts) {
      wechatAppId = weChatConfig?.officialAccount.officialAppId;
    }
    Logger.error(`paymentType:${paymentType},wechatAppId:${wechatAppId}`);
    if (!wechatAppId) {
      Logger.error(`Wechat mini program information is not configured`);
      throw new Error('Wechat mini program information is not configured');
    }
    const params = await this.createPurchasePaymentInformation(ctx, paymentType, orderId, type);
    Logger.debug(`订单支付数据为：${JSON.stringify(params)}`);
    await this.initPay(ctx, wechatAppId);
    // const pay = this.payMap.get(ctx.channelId)?.get(wechatAppId);
    const pay = this.getChannelPaymentMethod(ctx)?.get(wechatAppId);
    if (!pay) {
      Logger.error(`payment error! pay not exist!`);
      throw new Error('payment error! pay not exist!');
    }
    const wxPayInfo = await pay.transactions_jsapi(params);
    Logger.debug(`微信支付返回数据为：${JSON.stringify(wxPayInfo)}`);
    const wxPayInitData: WeChatInitPayInfo = {
      status: wxPayInfo.status,
      appId: wxPayInfo.appId,
      timeStamp: wxPayInfo.timeStamp,
      nonceStr: wxPayInfo.nonceStr,
      package: wxPayInfo.package,
      signType: wxPayInfo.signType,
      paySign: wxPayInfo.paySign,
      orderCode: orderCode,
    };
    return wxPayInitData;
  }

  /**
   * 创建购买支付信息
   */
  async createPurchasePaymentInformation(
    ctx: RequestContext,
    paymentType: WeChatPaymentType,
    orderId: ID,
    paymentOrderType: PaymentOrderType = PaymentOrderType.MembershipOrder,
  ): Promise<Ijsapi> {
    let user = null;
    let price = 0;
    let code = '';
    if (paymentOrderType === PaymentOrderType.MembershipOrder) {
      const membershipOrder = await this.memberService.findOneOrder(ctx, orderId, undefined, [
        'customer',
        'customer.user',
      ]);
      if (!membershipOrder) {
        throw Error('No active membership order found for session');
      }
      user = membershipOrder.customer.user;
      price = membershipOrder.amount;
      code = membershipOrder.code;
    } else if (paymentOrderType === PaymentOrderType.GiftCardOrder) {
      const giftCardOrder = await this.giftCardOrderService.findOne(ctx, orderId, undefined, [
        'customer',
        'customer.user',
      ]);
      if (!giftCardOrder) {
        throw Error('No active giftCard order found for session');
      }
      user = giftCardOrder.customer.user;
      price = giftCardOrder.amount;
      code = giftCardOrder.code;
    } else if (paymentOrderType === PaymentOrderType.BlindBoxBuyOrder) {
      const blindBoxOrder = await this.interfaceBlindBoxOrder.getBlindBoxOrderByOrderId(ctx, orderId);
      if (!blindBoxOrder) {
        throw Error('No active blindBox order found for session');
      }
      user = blindBoxOrder.customer.user;
      price = blindBoxOrder.amount;
      code = blindBoxOrder.code;
    }

    if (!user) {
      throw Error('Cannot create payment intent for order without user');
    }
    let weChatCallBackUrl = '';
    let wechatUser: WeChatProgramUser | WeChatOfficialUser | undefined;
    if (paymentType === WeChatPaymentType.WechatProgram) {
      wechatUser = await this.weChatAuthService.getUserByPhone(ctx, user.identifier);
      weChatCallBackUrl = process.env.PROJECT_ADDRESS + '/wechatCallBacks/programPaymentCallBacks';
    } else if (paymentType === WeChatPaymentType.WechatOfficialAccounts) {
      wechatUser = await this.wechatComponentService.getUserByPhone(ctx, user.identifier);
      weChatCallBackUrl = process.env.PROJECT_ADDRESS + '/wechatCallBacks/officialPaymentCallBacks';
    }
    if (!wechatUser) {
      throw Error('Cannot create payment intent for order without wechatUser');
    }
    let total = 0;
    if (process.env.APP_ENV !== 'production') {
      total = 1;
    } else {
      total = Number(price);
    }
    Logger.debug(`channelToken:${ctx.channel.token}`);
    const paymentObj: Ijsapi = {
      //订单描述
      description: `${paymentOrderType} ${code}`,
      // eslint-disable-next-line @typescript-eslint/naming-convention
      out_trade_no: `${code}`,
      // eslint-disable-next-line @typescript-eslint/naming-convention
      notify_url: `${weChatCallBackUrl}/${ctx.channel.token}`,
      amount: {
        total: total,
      },
      payer: {
        openid: wechatUser.openId,
      },
      attach: paymentOrderType,
    };
    return paymentObj;
  }

  @Transaction()
  async switchingPaymentMethods(ctx: RequestContext, paymentMethod: PaymentMethod) {
    if (paymentMethod.handler.code === wechatPaymentHandler.code && paymentMethod.enabled) {
      const {items} = await this.paymentMethodService.findAll(ctx, {
        filter: {enabled: {eq: true}},
      });
      for (const item of items) {
        if (item.handler.code === wechatPaymentHandler.code && item.id !== paymentMethod.id) {
          await this.paymentMethodService.update(ctx, {id: item.id, enabled: false});
        }
      }
      // this.payMap.delete(ctx.channelId);
      await this.clearChannelPaymentMethod(ctx);
    }
    return;
  }

  async cancelWeChatPayment(ctx: RequestContext, order: Order, paymentType = WeChatPaymentType.WechatProgram) {
    let orderCode = order.code;
    if ((order?.customFields as OrderCustomFields)?.newOrderCode) {
      orderCode = (order?.customFields as OrderCustomFields)?.newOrderCode as string;
    }
    await this.cancelWeChatPaymentByOrderCode(ctx, orderCode, paymentType);
  }

  async cancelWeChatPaymentByOrderCode(
    ctx: RequestContext,
    orderCode: string,
    paymentType = WeChatPaymentType.WechatProgram,
  ) {
    const weChatConfig = await this.weChatConfigService.findOne(ctx);
    let wechatAppId;
    if (paymentType === WeChatPaymentType.WechatProgram) {
      wechatAppId = weChatConfig?.wechatProgram.weChatAppId;
    } else if (paymentType === WeChatPaymentType.WechatOfficialAccounts) {
      wechatAppId = weChatConfig?.officialAccount.officialAppId;
    }
    if (!wechatAppId) {
      throw new Error('Wechat mini program information is not configured');
    }
    await this.initPay(ctx, wechatAppId);
    // const pay = this.payMap.get(ctx.channelId)?.get(wechatAppId);
    const pay = this.getChannelPaymentMethod(ctx)?.get(wechatAppId);
    await pay?.close(orderCode);
  }
}

export interface ParamModel {
  // eslint-disable-next-line @typescript-eslint/naming-convention
  nonce_str: string; //随机字符串，不长于32位
  wxappid: string; //公众账号appid
  // eslint-disable-next-line @typescript-eslint/naming-convention
  mch_id: string; //商户号 微信支付分配的商户号 申请中
  // eslint-disable-next-line @typescript-eslint/naming-convention
  mch_billno: string; //商户订单号 字母加数字构成 长度最多28位且必须唯一
  // eslint-disable-next-line @typescript-eslint/naming-convention
  send_name: string; //商户名称 红包发送者名称
  // eslint-disable-next-line @typescript-eslint/naming-convention
  re_openid: string; //公众号下 用户openid
  // eslint-disable-next-line @typescript-eslint/naming-convention
  total_amount: number; //付款金额，单位分
  // eslint-disable-next-line @typescript-eslint/naming-convention
  total_num: number; //红包发放总个数
  wishing: string; //红包祝福语 最大长度128位
  // eslint-disable-next-line @typescript-eslint/naming-convention
  client_ip: string; //调用接口的机器Ip地址
  // eslint-disable-next-line @typescript-eslint/naming-convention
  act_name: string; //活动名称
  remark: string; //备注信息
  // eslint-disable-next-line @typescript-eslint/naming-convention
  notify_way: string;
  // eslint-disable-next-line @typescript-eslint/naming-convention
  scene_id?: string; // 非必须   PRODUCT_6 渠道分润  红包金额大于200或者小于1元时必传 PRODUCT_1
  sign?: string;
}
